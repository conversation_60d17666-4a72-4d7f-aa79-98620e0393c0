"""
SFTP Kivy Application - Milestone 3: Cyberpunk Theme
===================================================

Enhanced cyberpunk theme with stone/chrome/dark aesthetic:
- Metallic chrome buttons with sharp edges
- Neon cyan glow effects
- Dark stone-like backgrounds
- Electric accent colors

Version: 1.2 - Cyberpunk Theme Enhanced
"""

import os
import json
import logging
from datetime import datetime

# Kivy imports
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.switch import Switch
from kivy.uix.tabbedpanel import TabbedPanel, TabbedPanelItem
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
from kivy.graphics import Color, Rectangle, Line, Ellipse
from kivy.graphics.instructions import InstructionGroup
from kivy.metrics import dp
from kivy.core.window import Window
from kivy.animation import Animation

# Set window properties
Window.size = (1400, 900)
Window.title = "V1's SFTP Doofer - Cyberpunk Chrome Edition"

# ============================================================================
# ENHANCED CYBERPUNK THEME DEFINITIONS
# ============================================================================

CLASSIC_THEME = {
    "name": "Classic FileZilla Enhanced",
    "background": (0.94, 0.94, 0.96, 1),
    "panel": (0.88, 0.90, 0.94, 1),
    "button": (0.65, 0.75, 0.88, 1),
    "button_hover": (0.55, 0.65, 0.82, 1),
    "button_pressed": (0.45, 0.55, 0.75, 1),
    "button_glow": (0.4, 0.6, 0.9, 0.6),
    "text": (0.15, 0.15, 0.2, 1),
    "text_light": (0.3, 0.3, 0.35, 1),
    "accent": (0.2, 0.5, 0.8, 1),
    "success": (0.2, 0.7, 0.3, 1),
    "error": (0.8, 0.25, 0.25, 1),
    "warning": (0.9, 0.6, 0.1, 1),
    "border": (0.6, 0.65, 0.7, 1),
    "separator": (0.75, 0.78, 0.82, 1),
}

CYBERPUNK_THEME = {
    "name": "Cyberpunk Chrome Stone",
    "background": (0.01, 0.01, 0.03, 1),      # Deep void black
    "panel": (0.06, 0.08, 0.10, 1),           # Dark stone panels
    "button": (0.12, 0.15, 0.18, 1),          # Chrome base
    "button_hover": (0.20, 0.25, 0.30, 1),    # Bright chrome hover
    "button_pressed": (0.30, 0.35, 0.40, 1),  # Shiny chrome pressed
    "button_glow": (0, 0.9, 1, 0.9),          # Intense cyan glow
    "text": (0.85, 0.95, 1, 1),               # Ice blue text
    "text_light": (0.5, 0.6, 0.7, 1),         # Dimmed ice blue
    "accent": (0, 0.9, 1, 1),                 # Electric cyan
    "success": (0, 1, 0.2, 1),                # Neon green
    "error": (1, 0.05, 0.3, 1),               # Hot pink error
    "warning": (1, 0.7, 0, 1),                # Electric amber
    "border": (0.25, 0.3, 0.35, 1),           # Metallic border
    "separator": (0.15, 0.2, 0.25, 1),        # Dark chrome separator
    "chrome_highlight": (0.4, 0.45, 0.5, 1),  # Chrome highlight
    "stone_texture": (0.03, 0.04, 0.05, 1),   # Stone texture
}

# ============================================================================
# CYBERPUNK THEMED WIDGETS
# ============================================================================

class CyberButton(Button):
    """Cyberpunk chrome button with sharp edges and neon glow"""
    
    def __init__(self, theme, intensity=1.0, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        self.intensity = intensity
        self.is_hovered = False
        self.is_pressed = False
        
        # Remove default background
        self.background_normal = ''
        self.background_down = ''
        self.background_color = (0, 0, 0, 0)
        
        self.apply_cyberpunk_style()
        
        # Bind events
        self.bind(on_press=self.on_cyber_press)
        self.bind(on_release=self.on_cyber_release)
        
    def apply_cyberpunk_style(self):
        """Apply cyberpunk chrome styling with sharp edges"""
        self.color = self.theme["text"]
        
        with self.canvas.before:
            # Outer glow (neon effect)
            Color(*self.theme["button_glow"])
            self.outer_glow = Rectangle(
                pos=(self.x - dp(6), self.y - dp(6)),
                size=(self.width + dp(12), self.height + dp(12))
            )
            
            # Chrome base (sharp rectangle)
            Color(*self.theme["button"])
            self.chrome_base = Rectangle(
                pos=self.pos,
                size=self.size
            )
            
            # Chrome highlight (top edge)
            Color(*self.theme["chrome_highlight"])
            self.chrome_highlight = Rectangle(
                pos=(self.x, self.y + self.height - dp(3)),
                size=(self.width, dp(3))
            )
            
            # Chrome shadow (bottom edge)
            Color(*self.theme["stone_texture"])
            self.chrome_shadow = Rectangle(
                pos=(self.x, self.y),
                size=(self.width, dp(3))
            )
            
            # Border lines (sharp edges)
            Color(*self.theme["border"])
            self.border_lines = Line(
                rectangle=(self.x, self.y, self.width, self.height),
                width=1.5
            )
        
        self.bind(pos=self.update_cyber_graphics, size=self.update_cyber_graphics)
    
    def update_cyber_graphics(self, *args):
        """Update cyberpunk graphics elements"""
        if hasattr(self, 'outer_glow'):
            self.outer_glow.pos = (self.x - dp(6), self.y - dp(6))
            self.outer_glow.size = (self.width + dp(12), self.height + dp(12))
            
        if hasattr(self, 'chrome_base'):
            self.chrome_base.pos = self.pos
            self.chrome_base.size = self.size
            
        if hasattr(self, 'chrome_highlight'):
            self.chrome_highlight.pos = (self.x, self.y + self.height - dp(3))
            self.chrome_highlight.size = (self.width, dp(3))
            
        if hasattr(self, 'chrome_shadow'):
            self.chrome_shadow.pos = (self.x, self.y)
            self.chrome_shadow.size = (self.width, dp(3))
            
        if hasattr(self, 'border_lines'):
            self.border_lines.rectangle = (self.x, self.y, self.width, self.height)
    
    def on_cyber_press(self, *args):
        """Handle cyberpunk button press with intense glow"""
        self.is_pressed = True
        
        with self.canvas.before:
            # Intense glow when pressed
            Color(*self.theme["button_glow"][:3] + (1.0,))
            self.outer_glow = Rectangle(
                pos=(self.x - dp(10), self.y - dp(10)),
                size=(self.width + dp(20), self.height + dp(20))
            )
            
            # Bright chrome when pressed
            Color(*self.theme["button_pressed"])
            self.chrome_base = Rectangle(
                pos=self.pos,
                size=self.size
            )
    
    def on_cyber_release(self, *args):
        """Handle cyberpunk button release"""
        self.is_pressed = False
        
        # Return to normal state
        target_color = self.theme["button_hover"] if self.is_hovered else self.theme["button"]
        
        with self.canvas.before:
            # Normal glow
            Color(*self.theme["button_glow"])
            self.outer_glow = Rectangle(
                pos=(self.x - dp(6), self.y - dp(6)),
                size=(self.width + dp(12), self.height + dp(12))
            )
            
            # Normal chrome
            Color(*target_color)
            self.chrome_base = Rectangle(
                pos=self.pos,
                size=self.size
            )

class StonePanel(BoxLayout):
    """Panel with stone/chrome texture background"""
    
    def __init__(self, theme, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        
        with self.canvas.before:
            # Stone texture base
            Color(*theme["stone_texture"])
            self.stone_base = Rectangle(
                pos=self.pos,
                size=self.size
            )
            
            # Panel overlay
            Color(*theme["panel"])
            self.panel_overlay = Rectangle(
                pos=self.pos,
                size=self.size
            )
            
            # Metallic border
            Color(*theme["border"])
            self.border = Line(
                rectangle=(self.x, self.y, self.width, self.height),
                width=2
            )
        
        self.bind(pos=self.update_stone_graphics, size=self.update_stone_graphics)
    
    def update_stone_graphics(self, *args):
        if hasattr(self, 'stone_base'):
            self.stone_base.pos = self.pos
            self.stone_base.size = self.size
            
        if hasattr(self, 'panel_overlay'):
            self.panel_overlay.pos = self.pos
            self.panel_overlay.size = self.size
            
        if hasattr(self, 'border'):
            self.border.rectangle = (self.x, self.y, self.width, self.height)

class NeonLabel(Label):
    """Label with neon glow effect"""
    
    def __init__(self, theme, glow=False, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        self.glow = glow
        
        if glow:
            self.color = theme["accent"]
        else:
            self.color = theme["text"]

# ============================================================================
# CONFIGURATION MANAGER (Enhanced for Cyberpunk)
# ============================================================================

class ConfigManager:
    """Enhanced configuration manager with cyberpunk preferences"""
    
    def __init__(self):
        self.config_file = "sftp_kivy_cyberpunk_config.json"
        self.config = self.load_config()
    
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"Config load error: {e}")
        
        return {
            "theme_mode": "cyberpunk",  # Default to cyberpunk for this version
            "window_size": [1400, 900],
            "last_sftp_host": "cyber.matrix.net",
            "user_labels": ["MAINFRAME", "SUBNET_A", "BACKUP_NODE", "DEPLOY_ZONE", "ARCHIVE_VAULT"],
            "connection_history": [],
            "cyberpunk_preferences": {
                "glow_intensity": 1.5,
                "chrome_level": "high",
                "neon_effects": True,
                "matrix_mode": False
            }
        }
    
    def save_config(self):
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            logging.error(f"Config save error: {e}")

# ============================================================================
# MAIN CYBERPUNK APPLICATION
# ============================================================================

class CyberpunkSFTPApp(App):
    """Cyberpunk-themed SFTP Application"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.current_theme = self.get_current_theme()
        
        # Setup cyberpunk logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - CYBER - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sftp_cyberpunk.log'),
                logging.StreamHandler()
            ]
        )
        
        logging.info(f"Cyberpunk SFTP App initialized - Theme: {self.current_theme['name']}")
    
    def get_current_theme(self):
        theme_mode = self.config_manager.config.get("theme_mode", "cyberpunk")
        return CLASSIC_THEME if theme_mode == "classic" else CYBERPUNK_THEME
    
    def build(self):
        """Build cyberpunk chrome interface"""
        Window.clearcolor = self.current_theme["background"]
        
        # Main container with cyberpunk styling
        main_layout = BoxLayout(orientation='vertical', padding=dp(5), spacing=dp(3))
        
        # Cyberpunk header
        header = self.create_cyber_header()
        main_layout.add_widget(header)
        
        # Chrome toolbar
        toolbar = self.create_chrome_toolbar()
        main_layout.add_widget(toolbar)
        
        # Main content with stone panels
        content = self.create_cyber_content()
        main_layout.add_widget(content)
        
        # Neon status bar
        status_bar = self.create_neon_status_bar()
        main_layout.add_widget(status_bar)
        
        return main_layout
    
    def create_cyber_header(self):
        """Create cyberpunk header with neon styling"""
        header = StonePanel(self.current_theme, orientation='horizontal', 
                           size_hint_y=None, height=dp(80))
        
        # Title with neon glow
        title_layout = BoxLayout(orientation='vertical', size_hint_x=0.6)
        
        main_title = NeonLabel(self.current_theme, text="V1'S SFTP MATRIX", 
                              font_size='24sp', glow=True, bold=True)
        title_layout.add_widget(main_title)
        
        subtitle = NeonLabel(self.current_theme, text=">> CYBERPUNK CHROME EDITION <<", 
                            font_size='12sp')
        title_layout.add_widget(subtitle)
        
        header.add_widget(title_layout)
        
        # Theme controls with cyberpunk styling
        theme_panel = StonePanel(self.current_theme, orientation='horizontal', size_hint_x=0.4)
        
        theme_label = NeonLabel(self.current_theme, text="THEME_MODE:", size_hint_x=0.4)
        theme_panel.add_widget(theme_label)
        
        theme_switch = Switch(active=(self.config_manager.config.get("theme_mode") == "cyberpunk"),
                             size_hint_x=0.3)
        theme_switch.bind(active=self.toggle_theme)
        theme_panel.add_widget(theme_switch)
        
        mode_text = "CYBER" if theme_switch.active else "CLASSIC"
        mode_label = NeonLabel(self.current_theme, text=mode_text, glow=True, size_hint_x=0.3)
        theme_panel.add_widget(mode_label)
        
        header.add_widget(theme_panel)
        return header
    
    def create_chrome_toolbar(self):
        """Create chrome toolbar with cyberpunk buttons"""
        toolbar = StonePanel(self.current_theme, orientation='horizontal', 
                            size_hint_y=None, height=dp(70))
        
        # Cyberpunk connection buttons
        connect_btn = CyberButton(self.current_theme, text="⚡ JACK_IN", intensity=1.5)
        connect_btn.bind(on_press=self.jack_in)
        toolbar.add_widget(connect_btn)
        
        disconnect_btn = CyberButton(self.current_theme, text="🔌 JACK_OUT")
        disconnect_btn.bind(on_press=self.jack_out)
        toolbar.add_widget(disconnect_btn)
        
        scan_btn = CyberButton(self.current_theme, text="🔍 SCAN_NET", intensity=1.2)
        scan_btn.bind(on_press=self.scan_network)
        toolbar.add_widget(scan_btn)
        
        hack_btn = CyberButton(self.current_theme, text="💀 HACK_MODE")
        hack_btn.bind(on_press=self.hack_mode)
        toolbar.add_widget(hack_btn)
        
        return toolbar
    
    def create_cyber_content(self):
        """Create cyberpunk content area with stone panels"""
        content_layout = BoxLayout(orientation='horizontal', spacing=dp(5))
        
        # Left terminal - Local matrix
        left_terminal = self.create_cyber_terminal("LOCAL_MATRIX", "📁 LOCAL_NODE")
        content_layout.add_widget(left_terminal)
        
        # Chrome separator
        separator = BoxLayout(size_hint_x=None, width=dp(3))
        with separator.canvas.before:
            Color(*self.current_theme["accent"])
            separator.rect = Rectangle(pos=separator.pos, size=separator.size)
        content_layout.add_widget(separator)
        
        # Right terminal - Remote matrix
        right_terminal = self.create_cyber_terminal("REMOTE_MATRIX", "🌐 REMOTE_NODE")
        content_layout.add_widget(right_terminal)
        
        return content_layout
    
    def create_cyber_terminal(self, title, placeholder):
        """Create a cyberpunk-style terminal pane"""
        terminal = StonePanel(self.current_theme, orientation='vertical')
        
        # Terminal header with neon
        header = StonePanel(self.current_theme, orientation='horizontal', 
                           size_hint_y=None, height=dp(45))
        
        title_label = NeonLabel(self.current_theme, text=title, 
                               font_size='16sp', glow=True, bold=True)
        header.add_widget(title_label)
        
        terminal.add_widget(header)
        
        # Terminal content area
        terminal_area = ScrollView()
        terminal_content = NeonLabel(self.current_theme, 
                                    text=f"{placeholder}\n\n>> NEURAL_INTERFACE_LOADING...\n>> FILE_BROWSER_MODULE_PENDING\n>> MATRIX_CONNECTION_STANDBY",
                                    text_size=(None, None))
        terminal_area.add_widget(terminal_content)
        terminal.add_widget(terminal_area)
        
        return terminal
    
    def create_neon_status_bar(self):
        """Create neon status bar with cyberpunk indicators"""
        status_bar = StonePanel(self.current_theme, orientation='horizontal', 
                               size_hint_y=None, height=dp(40))
        
        # Main status with neon glow
        self.status_label = NeonLabel(self.current_theme, 
                                     text=f">> SYSTEM_READY - {self.current_theme['name']} ACTIVE",
                                     font_size='12sp', glow=True)
        status_bar.add_widget(self.status_label)
        
        # Connection status
        self.connection_status = NeonLabel(self.current_theme, 
                                          text="JACKED_OUT",
                                          font_size='12sp',
                                          size_hint_x=0.3)
        self.connection_status.color = self.current_theme["error"]
        status_bar.add_widget(self.connection_status)
        
        return status_bar
    
    # Cyberpunk event handlers
    def jack_in(self, button):
        self.status_label.text = ">> JACKING_INTO_MATRIX..."
        self.connection_status.text = "CONNECTING..."
        self.connection_status.color = self.current_theme["warning"]
        logging.info("Matrix jack-in initiated")
        
        Clock.schedule_once(self.simulate_jack_in, 2.5)
    
    def simulate_jack_in(self, dt):
        self.status_label.text = ">> CONNECTED_TO_CYBER.MATRIX.NET"
        self.connection_status.text = "JACKED_IN"
        self.connection_status.color = self.current_theme["success"]
        self.config_manager.config["connection_history"].insert(0, {
            "host": "cyber.matrix.net",
            "timestamp": datetime.now().isoformat(),
            "success": True
        })
    
    def jack_out(self, button):
        self.status_label.text = ">> DISCONNECTED_FROM_MATRIX"
        self.connection_status.text = "JACKED_OUT"
        self.connection_status.color = self.current_theme["error"]
        logging.info("Matrix jack-out complete")
    
    def scan_network(self, button):
        self.status_label.text = ">> SCANNING_NETWORK_NODES..."
        logging.info("Network scan initiated")
        Clock.schedule_once(self.simulate_scan, 2)
    
    def simulate_scan(self, dt):
        self.status_label.text = ">> SCAN_COMPLETE - 42_NODES_DETECTED"
    
    def hack_mode(self, button):
        self.status_label.text = ">> HACK_MODE_ACTIVATED - BE_CAREFUL_CHOOM"
        logging.info("Hack mode activated")
    
    def toggle_theme(self, switch, value):
        new_theme = "cyberpunk" if value else "classic"
        self.config_manager.config["theme_mode"] = new_theme
        self.config_manager.save_config()
        
        popup = Popup(title='THEME_CHANGED',
                     content=Label(text='RESTART_REQUIRED\nTO_APPLY_NEW_THEME'),
                     size_hint=(0.4, 0.3))
        popup.open()
        
        logging.info(f"Theme changed to: {new_theme}")
    
    def on_stop(self):
        self.config_manager.save_config()
        logging.info("Cyberpunk SFTP App shutdown complete")

if __name__ == '__main__':
    app = CyberpunkSFTPApp()
    app.run()
