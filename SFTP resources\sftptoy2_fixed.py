
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import paramiko
import threading
import shutil
import subprocess

class Application(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("SFTP Toy")
        self.geometry("900x600")
        self.protocol("WM_DELETE_WINDOW", self.on_close)

        self.local_start_dir = os.getcwd()
        self.selected_local_folder = self.local_start_dir

        self.sftp_client = None
        self.sftp_connected = False
        self.remote_start_dir = "/"
        self.selected_remote_folder = self.remote_start_dir

        self.clipboard_path = None  # For copy-paste operations

        self.create_widgets()

    def create_widgets(self):
        # Main container frames
        top_frame = ttk.Frame(self)
        top_frame.pack(side="top", fill="x")

        # Folder selection and display
        folder_frame = ttk.Frame(top_frame)
        folder_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)

        folder_label = ttk.Label(folder_frame, text="Local Folder View")
        folder_label.pack(anchor="w")

        # Treeview for local folder
        self.folder_tree = ttk.Treeview(folder_frame, columns=("size", "type"), show="tree headings")
        self.folder_tree.heading("#0", text="Name")
        self.folder_tree.heading("size", text="Size")
        self.folder_tree.heading("type", text="Type")
        self.folder_tree.column("size", width=80, anchor="e")
        self.folder_tree.column("type", width=80, anchor="center")
        self.folder_tree.pack(fill="both", expand=True)

        self.folder_tree.bind("<Button-3>", self.show_local_context_menu)
        self.folder_tree.bind("<Double-1>", self.local_item_double_click)

        # Right frame for checkboxes and details
        right_frame = ttk.Frame(top_frame)
        right_frame.pack(side="right", fill="y", padx=10, pady=10)

        self.checkbox_var = tk.BooleanVar()
        self.ini_checkbox = ttk.Checkbutton(right_frame, text="serversettings.ini selectable", variable=self.checkbox_var)
        self.ini_checkbox.pack(anchor="n")

        # Bottom frame for buttons
        bottom_frame = ttk.Frame(self)
        bottom_frame.pack(side="bottom", fill="x", padx=10, pady=10)

        self.refresh_button = ttk.Button(bottom_frame, text="Refresh Local Folder", command=self.refresh_dir_list)
        self.refresh_button.pack(side="left")

        self.connect_sftp_button = ttk.Button(bottom_frame, text="Connect SFTP", command=self.open_sftp_connect_window)
        self.connect_sftp_button.pack(side="left", padx=5)

        # Populate initial local folder list
        self.refresh_dir_list()

    def refresh_dir_list(self):
        self.folder_tree.delete(*self.folder_tree.get_children())
        self.populate_treeview(self.selected_local_folder)

    def populate_treeview(self, folder_path):
        try:
            entries = os.listdir(folder_path)
            entries.sort(key=lambda x: (not os.path.isdir(os.path.join(folder_path, x)), x.lower()))
            for entry in entries:
                full_path = os.path.join(folder_path, entry)
                if os.path.isdir(full_path):
                    iid = self.folder_tree.insert("", "end", text=entry, values=("", "Folder"))
                    # Add children placeholder for lazy loading
                    self.folder_tree.insert(iid, "end", text="Loading...")
                else:
                    size = os.path.getsize(full_path)
                    ext = os.path.splitext(entry)[1].lower()
                    filetype = "INI File" if entry.lower() == "serversettings.ini" else ext if ext else "File"
                    self.folder_tree.insert("", "end", text=entry, values=(self.format_size(size), filetype))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to list directory:
{str(e)}")

    def format_size(self, size):
        # Format file size in KB, MB etc.
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024:
                return f"{size} {unit}"
            size /= 1024
        return f"{size:.2f} TB"

    def local_item_double_click(self, event):
        item_id = self.folder_tree.focus()
        if not item_id:
            return
        item_text = self.folder_tree.item(item_id, "text")
        path = os.path.join(self.selected_local_folder, item_text)
        if os.path.isdir(path):
            self.selected_local_folder = path
            self.refresh_dir_list()

    def show_local_context_menu(self, event):
        iid = self.folder_tree.identify_row(event.y)
        if not iid:
            return
        self.folder_tree.selection_set(iid)
        item_text = self.folder_tree.item(iid, "text")
        path = os.path.join(self.selected_local_folder, item_text)

        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Open in Notepad", command=lambda: self.open_in_notepad(path))
        menu.add_separator()
        menu.add_command(label="Copy", command=lambda: self.copy_path(path))
        menu.add_command(label="Paste", command=self.paste_path)
        menu.add_command(label="Delete", command=lambda: self.delete_path(path))
        menu.add_separator()
        menu.add_command(label="Move to Another Folder...", command=lambda: self.move_to_folder(path))
        menu.tk_popup(event.x_root, event.y_root)

    def open_in_notepad(self, path):
        try:
            if os.name == 'nt':  # Windows
                subprocess.Popen(['notepad.exe', path])
            else:  # Unix-like, fallback to xdg-open or open
                subprocess.Popen(['xdg-open', path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open file:
{str(e)}")

    def copy_path(self, path):
        self.clipboard_path = path
        messagebox.showinfo("Copied", f"Copied path:
{path}")

    def paste_path(self):
        if not self.clipboard_path:
            messagebox.showwarning("Paste", "Clipboard is empty.")
            return
        dest_folder = self.selected_local_folder
        try:
            if os.path.isdir(self.clipboard_path):
                base_name = os.path.basename(self.clipboard_path)
                dest_path = os.path.join(dest_folder, base_name)
                shutil.copytree(self.clipboard_path, dest_path)
            else:
                base_name = os.path.basename(self.clipboard_path)
                dest_path = os.path.join(dest_folder, base_name)
                shutil.copy2(self.clipboard_path, dest_path)
            self.refresh_dir_list()
            messagebox.showinfo("Paste", f"Pasted to:
{dest_path}")
        except Exception as e:
            messagebox.showerror("Paste Error", f"Failed to paste:
{str(e)}")

    def delete_path(self, path):
        confirm = messagebox.askyesno("Delete Confirmation", f"Are you sure you want to delete:
{path}?")
        if not confirm:
            return
        try:
            if os.path.isdir(path):
                shutil.rmtree(path)
            else:
                os.remove(path)
            self.refresh_dir_list()
        except Exception as e:
            messagebox.showerror("Delete Error", f"Failed to delete:
{str(e)}")

    def move_to_folder(self, path):
        dest_folder = filedialog.askdirectory(title="Select Destination Folder")
        if not dest_folder:
            return
        try:
            base_name = os.path.basename(path)
            dest_path = os.path.join(dest_folder, base_name)
            shutil.move(path, dest_path)
            self.refresh_dir_list()
            messagebox.showinfo("Move", f"Moved to:
{dest_path}")
        except Exception as e:
            messagebox.showerror("Move Error", f"Failed to move:
{str(e)}")

    def open_sftp_connect_window(self):
        SFTPConnectWindow(self)

    def on_close(self):
        if self.sftp_connected and self.sftp_client:
            self.sftp_client.close()
        self.destroy()


class SFTPConnectWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.title("Connect to SFTP Server")
        self.geometry("600x400")
        self.parent = parent
        self.sftp_client = None
        self.transport = None
        self.connected = False
        self.remote_current_path = "/"

        self.create_widgets()

    def create_widgets(self):
        # Frame for connection info (grid inside a packed frame)
        connect_frame = ttk.Frame(self)
        connect_frame.pack(side="top", fill="x", padx=10, pady=10)

        # Using grid inside connect_frame (which is packed to root)
        ttk.Label(connect_frame, text="Host:").grid(row=0, column=0, sticky="e")
        self.host_entry = ttk.Entry(connect_frame, width=30)
        self.host_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(connect_frame, text="Port:").grid(row=1, column=0, sticky="e")
        self.port_entry = ttk.Entry(connect_frame, width=10)
        self.port_entry.grid(row=1, column=1, padx=5, pady=2, sticky="w")
        self.port_entry.insert(0, "22")

        ttk.Label(connect_frame, text="Username:").grid(row=2, column=0, sticky="e")
        self.username_entry = ttk.Entry(connect_frame, width=30)
        self.username_entry.grid(row=2, column=1, padx=5, pady=2)

        ttk.Label(connect_frame, text="Password:").grid(row=3, column=0, sticky="e")
        self.password_entry = ttk.Entry(connect_frame, width=30, show="*")
        self.password_entry.grid(row=3, column=1, padx=5, pady=2)

        self.connect_btn = ttk.Button(connect_frame, text="Connect", command=self.connect_sftp)
        self.connect_btn.grid(row=4, column=0, columnspan=2, pady=10)

        # Treeview for remote folders
        remote_frame = ttk.Frame(self)
        remote_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.remote_tree = ttk.Treeview(remote_frame, columns=("type",), show="tree headings")
        self.remote_tree.heading("#0", text="Remote Path")
        self.remote_tree.heading("type", text="Type")
        self.remote_tree.column("type", width=100, anchor="center")
        self.remote_tree.pack(fill="both", expand=True)

        self.remote_tree.bind("<Button-3>", self.show_remote_context_menu)
        self.remote_tree.bind("<Double-1>", self.remote_item_double_click)

    def connect_sftp(self):
        host = self.host_entry.get()
        port = self.port_entry.get()
        username = self.username_entry.get()
        password = self.password_entry.get()
        try:
            port_num = int(port)
        except ValueError:
            messagebox.showerror("Input Error", "Port must be an integer")
            return

        try:
            transport = paramiko.Transport((host, port_num))
            transport.connect(username=username, password=password)
            self.sftp_client = paramiko.SFTPClient.from_transport(transport)
            self.transport = transport
            self.connected = True
            messagebox.showinfo("Connected", "SFTP connection established.")
            self.remote_current_path = "."
            self.refresh_remote_list()
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect:
{str(e)}")

    def refresh_remote_list(self):
        if not self.connected or not self.sftp_client:
            return
        self.remote_tree.delete(*self.remote_tree.get_children())
        self.populate_remote_tree(self.remote_current_path)

    def populate_remote_tree(self, path):
        try:
            items = self.sftp_client.listdir_attr(path)
            items.sort(key=lambda x: (not stat.S_ISDIR(x.st_mode), x.filename.lower()))
            for item in items:
                item_type = "Folder" if stat.S_ISDIR(item.st_mode) else "File"
                iid = self.remote_tree.insert("", "end", text=item.filename, values=(item_type,))
                if item_type == "Folder":
                    self.remote_tree.insert(iid, "end", text="Loading...")
        except Exception as e:
            messagebox.showerror("Remote List Error", f"Failed to list remote directory:
{str(e)}")

    def remote_item_double_click(self, event):
        item_id = self.remote_tree.focus()
        if not item_id:
            return
        item_text = self.remote_tree.item(item_id, "text")
        # Build new path for remote
        new_path = os.path.join(self.remote_current_path, item_text)
        try:
            attr = self.sftp_client.stat(new_path)
            if stat.S_ISDIR(attr.st_mode):
                self.remote_current_path = new_path
                self.refresh_remote_list()
        except Exception as e:
            messagebox.showerror("Remote Open Error", f"Failed to open remote folder:
{str(e)}")

    def show_remote_context_menu(self, event):
        iid = self.remote_tree.identify_row(event.y)
        if not iid:
            return
        self.remote_tree.selection_set(iid)
        item_text = self.remote_tree.item(iid, "text")
        remote_path = os.path.join(self.remote_current_path, item_text)

        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Copy Path", command=lambda: self.copy_remote_path(remote_path))
        menu.add_command(label="Paste Here (not implemented)", state="disabled")
        menu.add_command(label="Delete", command=lambda: self.delete_remote_path(remote_path))
        menu.tk_popup(event.x_root, event.y_root)

    def copy_remote_path(self, path):
        self.parent.clipboard_path = path
        messagebox.showinfo("Copied", f"Copied remote path:
{path}")

    def delete_remote_path(self, path):
        confirm = messagebox.askyesno("Delete Confirmation", f"Are you sure you want to delete remote path:
{path}?")
        if not confirm:
            return
        try:
            attr = self.sftp_client.stat(path)
            if stat.S_ISDIR(attr.st_mode):
                self._rmdir_remote_recursive(path)
            else:
                self.sftp_client.remove(path)
            self.refresh_remote_list()
        except Exception as e:
            messagebox.showerror("Delete Error", f"Failed to delete remote path:
{str(e)}")

    def _rmdir_remote_recursive(self, path):
        for fileattr in self.sftp_client.listdir_attr(path):
            file_path = os.path.join(path, fileattr.filename)
            if stat.S_ISDIR(fileattr.st_mode):
                self._rmdir_remote_recursive(file_path)
            else:
                self.sftp_client.remove(file_path)
        self.sftp_client.rmdir(path)


if __name__ == "__main__":
    import stat
    app = Application()
    app.mainloop()
