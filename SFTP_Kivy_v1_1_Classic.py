"""
SFTP Kivy Application - Milestone 3: Cyberpunk Theme
===================================================

Dual theme system with enhanced cyberpunk mode:
- Classic: Smooth glow buttons and FileZilla-ish feel
- Cyberpunk: Stone/chrome/dark aesthetic with neon accents

Version: 1.2 - Cyberpunk Theme Added
"""

import os
import json
import logging
from datetime import datetime

# Kivy imports
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.switch import Switch
from kivy.uix.tabbedpanel import TabbedPanel, TabbedPanelItem
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
from kivy.graphics import Color, RoundedRectangle, Line, Ellipse
from kivy.graphics.instructions import InstructionGroup
from kivy.metrics import dp
from kivy.core.window import Window
from kivy.animation import Animation

# Set window properties
Window.size = (1400, 900)
Window.title = "V1's SFTP Doofer - Classic FileZilla Style"

# ============================================================================
# ENHANCED THEME DEFINITIONS
# ============================================================================

CLASSIC_THEME = {
    "name": "Classic FileZilla Enhanced",
    "background": (0.94, 0.94, 0.96, 1),      # Soft light blue-grey
    "panel": (0.88, 0.90, 0.94, 1),           # Light blue panel
    "button": (0.65, 0.75, 0.88, 1),          # FileZilla blue buttons
    "button_hover": (0.55, 0.65, 0.82, 1),    # Darker blue on hover
    "button_pressed": (0.45, 0.55, 0.75, 1),  # Even darker when pressed
    "button_glow": (0.4, 0.6, 0.9, 0.6),      # Bright blue glow
    "text": (0.15, 0.15, 0.2, 1),             # Dark blue-grey text
    "text_light": (0.3, 0.3, 0.35, 1),        # Lighter text
    "accent": (0.2, 0.5, 0.8, 1),             # FileZilla blue accent
    "success": (0.2, 0.7, 0.3, 1),            # Success green
    "error": (0.8, 0.25, 0.25, 1),            # Error red
    "warning": (0.9, 0.6, 0.1, 1),            # Warning orange
    "border": (0.6, 0.65, 0.7, 1),            # Subtle border
    "separator": (0.75, 0.78, 0.82, 1),       # Separator lines
}

CYBERPUNK_THEME = {
    "name": "Cyberpunk Chrome",
    "background": (0.02, 0.02, 0.05, 1),      # Deep black-blue
    "panel": (0.08, 0.08, 0.12, 1),           # Dark metallic panels
    "button": (0.15, 0.18, 0.22, 1),          # Dark chrome buttons
    "button_hover": (0.25, 0.28, 0.32, 1),    # Lighter chrome hover
    "button_pressed": (0.35, 0.38, 0.42, 1),  # Bright chrome pressed
    "button_glow": (0, 0.8, 1, 0.8),          # Intense cyan glow
    "text": (0.85, 0.9, 0.95, 1),             # Cool white text
    "text_light": (0.6, 0.65, 0.7, 1),        # Dimmed text
    "accent": (0, 0.8, 1, 1),                 # Electric cyan
    "success": (0, 1, 0.4, 1),                # Neon green
    "error": (1, 0.1, 0.4, 1),                # Hot pink error
    "warning": (1, 0.8, 0, 1),                # Electric yellow
    "border": (0.3, 0.35, 0.4, 1),            # Metallic border
    "separator": (0.2, 0.25, 0.3, 1),         # Dark separator
}

# ============================================================================
# ENHANCED THEMED WIDGETS
# ============================================================================

class GlowButton(Button):
    """Enhanced button with smooth glow effects and animations"""
    
    def __init__(self, theme, glow_intensity=1.0, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        self.glow_intensity = glow_intensity
        self.is_hovered = False
        self.is_pressed = False
        
        # Remove default background
        self.background_normal = ''
        self.background_down = ''
        self.background_color = (0, 0, 0, 0)  # Transparent
        
        self.apply_theme()
        self.setup_animations()
        
        # Bind events
        self.bind(on_press=self.on_button_press)
        self.bind(on_release=self.on_button_release)
        
    def apply_theme(self):
        """Apply theme with glow effects"""
        self.color = self.theme["text"]
        
        with self.canvas.before:
            # Glow effect (outer)
            Color(*self.theme["button_glow"])
            self.glow_rect = RoundedRectangle(
                pos=(self.x - dp(4), self.y - dp(4)),
                size=(self.width + dp(8), self.height + dp(8)),
                radius=[dp(12)]
            )
            
            # Main button
            Color(*self.theme["button"])
            self.main_rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(8)]
            )
            
            # Highlight (inner glow)
            Color(*self.theme["button_glow"][:3] + (0.3,))
            self.highlight_rect = RoundedRectangle(
                pos=(self.x + dp(2), self.y + self.height - dp(6)),
                size=(self.width - dp(4), dp(4)),
                radius=[dp(2)]
            )
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
    
    def setup_animations(self):
        """Setup smooth animations"""
        self.hover_anim = Animation(duration=0.2)
        self.press_anim = Animation(duration=0.1)
    
    def update_graphics(self, *args):
        """Update all graphics elements"""
        if hasattr(self, 'glow_rect'):
            self.glow_rect.pos = (self.x - dp(4), self.y - dp(4))
            self.glow_rect.size = (self.width + dp(8), self.height + dp(8))
            
        if hasattr(self, 'main_rect'):
            self.main_rect.pos = self.pos
            self.main_rect.size = self.size
            
        if hasattr(self, 'highlight_rect'):
            self.highlight_rect.pos = (self.x + dp(2), self.y + self.height - dp(6))
            self.highlight_rect.size = (self.width - dp(4), dp(4))
    
    def on_button_press(self, *args):
        """Handle button press with animation"""
        self.is_pressed = True
        
        # Update colors for pressed state
        with self.canvas.before:
            Color(*self.theme["button_pressed"])
            self.main_rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(8)]
            )
            
            # Brighter glow when pressed
            Color(*self.theme["button_glow"][:3] + (0.8,))
            self.glow_rect = RoundedRectangle(
                pos=(self.x - dp(6), self.y - dp(6)),
                size=(self.width + dp(12), self.height + dp(12)),
                radius=[dp(14)]
            )
    
    def on_button_release(self, *args):
        """Handle button release with animation"""
        self.is_pressed = False
        
        # Return to normal or hover state
        target_color = self.theme["button_hover"] if self.is_hovered else self.theme["button"]
        
        with self.canvas.before:
            Color(*target_color)
            self.main_rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(8)]
            )
            
            # Normal glow
            Color(*self.theme["button_glow"])
            self.glow_rect = RoundedRectangle(
                pos=(self.x - dp(4), self.y - dp(4)),
                size=(self.width + dp(8), self.height + dp(8)),
                radius=[dp(12)]
            )
    
    def on_touch_down(self, touch):
        """Handle touch down for hover effects"""
        if self.collide_point(*touch.pos):
            return super().on_touch_down(touch)
        return False
    
    def on_touch_move(self, touch):
        """Handle hover effects"""
        if self.collide_point(*touch.pos) and not self.is_hovered:
            self.is_hovered = True
            self.apply_hover_effect()
        elif not self.collide_point(*touch.pos) and self.is_hovered:
            self.is_hovered = False
            self.remove_hover_effect()
        
        return super().on_touch_move(touch)
    
    def apply_hover_effect(self):
        """Apply hover glow effect"""
        if not self.is_pressed:
            with self.canvas.before:
                Color(*self.theme["button_hover"])
                self.main_rect = RoundedRectangle(
                    pos=self.pos,
                    size=self.size,
                    radius=[dp(8)]
                )
                
                # Enhanced glow on hover
                Color(*self.theme["button_glow"][:3] + (0.7,))
                self.glow_rect = RoundedRectangle(
                    pos=(self.x - dp(5), self.y - dp(5)),
                    size=(self.width + dp(10), self.height + dp(10)),
                    radius=[dp(13)]
                )
    
    def remove_hover_effect(self):
        """Remove hover glow effect"""
        if not self.is_pressed:
            with self.canvas.before:
                Color(*self.theme["button"])
                self.main_rect = RoundedRectangle(
                    pos=self.pos,
                    size=self.size,
                    radius=[dp(8)]
                )
                
                # Normal glow
                Color(*self.theme["button_glow"])
                self.glow_rect = RoundedRectangle(
                    pos=(self.x - dp(4), self.y - dp(4)),
                    size=(self.width + dp(8), self.height + dp(8)),
                    radius=[dp(12)]
                )

class FileZillaPanel(BoxLayout):
    """Panel with FileZilla-style gradient background"""
    
    def __init__(self, theme, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        
        with self.canvas.before:
            # Gradient effect (simulated with multiple rectangles)
            Color(*theme["panel"])
            self.bg_rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(6)]
            )
            
            # Subtle inner shadow
            Color(*theme["border"])
            Line(rounded_rectangle=(self.x, self.y, self.width, self.height, dp(6)), width=1)
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
    
    def update_graphics(self, *args):
        if hasattr(self, 'bg_rect'):
            self.bg_rect.pos = self.pos
            self.bg_rect.size = self.size

class StatusLabel(Label):
    """Enhanced label with theme-aware styling"""
    
    def __init__(self, theme, status_type="normal", **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        self.status_type = status_type
        
        # Set color based on status type
        if status_type == "success":
            self.color = theme["success"]
        elif status_type == "error":
            self.color = theme["error"]
        elif status_type == "warning":
            self.color = theme["warning"]
        else:
            self.color = theme["text"]

# ============================================================================
# CONFIGURATION MANAGER (Enhanced)
# ============================================================================

class ConfigManager:
    """Enhanced configuration manager with theme persistence"""
    
    def __init__(self):
        self.config_file = "sftp_kivy_config.json"
        self.config = self.load_config()
    
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"Config load error: {e}")
        
        return {
            "theme_mode": "classic",
            "window_size": [1400, 900],
            "last_sftp_host": "sftp.example.com",
            "user_labels": ["Production", "Staging", "Backup", "Archive", "Deploy"],
            "connection_history": [],
            "ui_preferences": {
                "show_hidden_files": False,
                "auto_refresh": True,
                "glow_intensity": 1.0
            }
        }
    
    def save_config(self):
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            logging.error(f"Config save error: {e}")
    
    def add_connection_history(self, host, success=True):
        """Add connection to history"""
        entry = {
            "host": host,
            "timestamp": datetime.now().isoformat(),
            "success": success
        }
        
        if "connection_history" not in self.config:
            self.config["connection_history"] = []
        
        self.config["connection_history"].insert(0, entry)
        
        # Keep only last 10 entries
        self.config["connection_history"] = self.config["connection_history"][:10]
        self.save_config()

# ============================================================================
# MAIN APPLICATION (Enhanced)
# ============================================================================

class SFTPKivyApp(App):
    """Enhanced SFTP Kivy Application with FileZilla-style UI"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.current_theme = self.get_current_theme()
        
        # Setup enhanced logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - KIVY-ENHANCED - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sftp_kivy_enhanced.log'),
                logging.StreamHandler()
            ]
        )
        
        logging.info(f"Enhanced SFTP Kivy App initialized - Theme: {self.current_theme['name']}")
    
    def get_current_theme(self):
        theme_mode = self.config_manager.config.get("theme_mode", "classic")
        return CLASSIC_THEME if theme_mode == "classic" else CYBERPUNK_THEME
    
    def build(self):
        """Build enhanced FileZilla-style interface"""
        Window.clearcolor = self.current_theme["background"]
        
        # Main container with FileZilla-style layout
        main_layout = BoxLayout(orientation='vertical', padding=dp(8), spacing=dp(6))
        
        # Enhanced header
        header = self.create_enhanced_header()
        main_layout.add_widget(header)
        
        # Toolbar with glow buttons
        toolbar = self.create_toolbar()
        main_layout.add_widget(toolbar)
        
        # Main content area
        content = self.create_enhanced_content()
        main_layout.add_widget(content)
        
        # Enhanced status bar
        status_bar = self.create_enhanced_status_bar()
        main_layout.add_widget(status_bar)
        
        return main_layout
    
    def create_enhanced_header(self):
        """Create enhanced header with FileZilla styling"""
        header = FileZillaPanel(self.current_theme, orientation='horizontal', 
                               size_hint_y=None, height=dp(70))
        
        # Title with enhanced styling
        title_layout = BoxLayout(orientation='vertical', size_hint_x=0.6)
        
        main_title = Label(text="V1's SFTP Doofer", 
                          font_size='22sp', 
                          color=self.current_theme["text"],
                          bold=True)
        title_layout.add_widget(main_title)
        
        subtitle = Label(text="FileZilla-Style Enhanced Edition", 
                        font_size='12sp', 
                        color=self.current_theme["text_light"])
        title_layout.add_widget(subtitle)
        
        header.add_widget(title_layout)
        
        # Theme controls
        theme_panel = FileZillaPanel(self.current_theme, orientation='horizontal', size_hint_x=0.4)
        
        theme_label = Label(text="Theme Mode:", 
                           color=self.current_theme["text"], 
                           size_hint_x=0.4)
        theme_panel.add_widget(theme_label)
        
        theme_switch = Switch(active=(self.config_manager.config.get("theme_mode") == "cyberpunk"),
                             size_hint_x=0.3)
        theme_switch.bind(active=self.toggle_theme)
        theme_panel.add_widget(theme_switch)
        
        mode_text = "Cyberpunk" if theme_switch.active else "Classic"
        mode_label = Label(text=mode_text, 
                          color=self.current_theme["accent"], 
                          size_hint_x=0.3)
        theme_panel.add_widget(mode_label)
        
        header.add_widget(theme_panel)
        return header
    
    def create_toolbar(self):
        """Create toolbar with enhanced glow buttons"""
        toolbar = FileZillaPanel(self.current_theme, orientation='horizontal', 
                                size_hint_y=None, height=dp(60))
        
        # Connection buttons with glow effects
        connect_btn = GlowButton(self.current_theme, text="🔗 Connect", 
                                glow_intensity=1.2)
        connect_btn.bind(on_press=self.connect_sftp)
        toolbar.add_widget(connect_btn)
        
        disconnect_btn = GlowButton(self.current_theme, text="❌ Disconnect")
        disconnect_btn.bind(on_press=self.disconnect_sftp)
        toolbar.add_widget(disconnect_btn)
        
        test_btn = GlowButton(self.current_theme, text="🧪 Test Connection", 
                             glow_intensity=0.8)
        test_btn.bind(on_press=self.test_connection)
        toolbar.add_widget(test_btn)
        
        refresh_btn = GlowButton(self.current_theme, text="🔄 Refresh")
        refresh_btn.bind(on_press=self.refresh_files)
        toolbar.add_widget(refresh_btn)
        
        return toolbar
    
    def create_enhanced_content(self):
        """Create enhanced content area with FileZilla-style dual panes"""
        content_layout = BoxLayout(orientation='horizontal', spacing=dp(8))
        
        # Left pane - Local files
        left_pane = self.create_file_pane("Local Files", "📁 Local Directory")
        content_layout.add_widget(left_pane)
        
        # Center separator
        separator = BoxLayout(size_hint_x=None, width=dp(2))
        with separator.canvas.before:
            Color(*self.current_theme["separator"])
            separator.rect = RoundedRectangle(pos=separator.pos, size=separator.size)
        content_layout.add_widget(separator)
        
        # Right pane - Remote files
        right_pane = self.create_file_pane("Remote Files", "🌐 Remote Directory")
        content_layout.add_widget(right_pane)
        
        return content_layout
    
    def create_file_pane(self, title, placeholder):
        """Create a FileZilla-style file pane"""
        pane = FileZillaPanel(self.current_theme, orientation='vertical')
        
        # Pane header
        header = FileZillaPanel(self.current_theme, orientation='horizontal', 
                               size_hint_y=None, height=dp(40))
        
        title_label = Label(text=title, 
                           font_size='16sp', 
                           color=self.current_theme["text"],
                           bold=True)
        header.add_widget(title_label)
        
        pane.add_widget(header)
        
        # File list area (placeholder for now)
        file_area = ScrollView()
        file_content = Label(text=f"{placeholder}\n\nFile browser will be\nimplemented in next milestone",
                            color=self.current_theme["text_light"],
                            text_size=(None, None))
        file_area.add_widget(file_content)
        pane.add_widget(file_area)
        
        return pane
    
    def create_enhanced_status_bar(self):
        """Create enhanced status bar with multiple status indicators"""
        status_bar = FileZillaPanel(self.current_theme, orientation='horizontal', 
                                   size_hint_y=None, height=dp(35))
        
        # Main status
        self.status_label = StatusLabel(self.current_theme, 
                                       text=f"Ready - {self.current_theme['name']} Active",
                                       font_size='12sp')
        status_bar.add_widget(self.status_label)
        
        # Connection status
        self.connection_status = StatusLabel(self.current_theme, 
                                           text="Disconnected",
                                           status_type="error",
                                           font_size='12sp',
                                           size_hint_x=0.3)
        status_bar.add_widget(self.connection_status)
        
        return status_bar
    
    # Event handlers
    def connect_sftp(self, button):
        self.status_label.text = "Connecting to SFTP server..."
        self.connection_status.text = "Connecting..."
        self.connection_status.color = self.current_theme["warning"]
        logging.info("SFTP connection initiated")
        
        # Simulate connection after delay
        Clock.schedule_once(self.simulate_connection, 2)
    
    def simulate_connection(self, dt):
        self.status_label.text = "Connected to sftp.example.com"
        self.connection_status.text = "Connected"
        self.connection_status.color = self.current_theme["success"]
        self.config_manager.add_connection_history("sftp.example.com", True)
    
    def disconnect_sftp(self, button):
        self.status_label.text = "Disconnected from SFTP server"
        self.connection_status.text = "Disconnected"
        self.connection_status.color = self.current_theme["error"]
        logging.info("SFTP disconnected")
    
    def test_connection(self, button):
        self.status_label.text = "Testing connection..."
        logging.info("Connection test initiated")
        Clock.schedule_once(self.simulate_test, 1.5)
    
    def simulate_test(self, dt):
        self.status_label.text = "Connection test successful ✓"
    
    def refresh_files(self, button):
        self.status_label.text = "Refreshing file lists..."
        Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', 'File lists refreshed'), 1)
    
    def toggle_theme(self, switch, value):
        new_theme = "cyberpunk" if value else "classic"
        self.config_manager.config["theme_mode"] = new_theme
        self.config_manager.save_config()
        
        popup = Popup(title='Theme Changed',
                     content=Label(text='Restart the application\nto apply the new theme.'),
                     size_hint=(0.4, 0.3))
        popup.open()
        
        logging.info(f"Theme changed to: {new_theme}")
    
    def on_stop(self):
        self.config_manager.save_config()
        logging.info("Enhanced SFTP Kivy App shutdown complete")

if __name__ == '__main__':
    app = SFTPKivyApp()
    app.run()
