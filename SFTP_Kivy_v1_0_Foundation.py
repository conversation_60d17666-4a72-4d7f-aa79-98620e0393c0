"""
SFTP Kivy Application - Milestone 1: Foundation
===============================================

Base Kivy app structure with dual theme system:
- Classic Mode: Smooth glow buttons, FileZilla-ish feel
- Cyberpunk Mode: Stone/chrome/dark aesthetic

Version: 1.0 - Foundation
"""

import os
import json
import logging
from datetime import datetime

# Kivy imports
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.switch import Switch
from kivy.uix.tabbedpanel import TabbedPanel, TabbedPanelItem
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
from kivy.graphics import Color, RoundedRectangle, Line
from kivy.metrics import dp
from kivy.core.window import Window

# Set window properties
Window.size = (1400, 900)
Window.title = "V1's SFTP Doofer - Kivy Edition"

# ============================================================================
# THEME DEFINITIONS
# ============================================================================

CLASSIC_THEME = {
    "name": "Classic FileZilla",
    "background": (0.95, 0.95, 0.95, 1),  # Light grey background
    "panel": (0.9, 0.9, 0.9, 1),          # Panel background
    "button": (0.7, 0.8, 0.9, 1),         # Soft blue buttons
    "button_hover": (0.6, 0.7, 0.85, 1),  # Darker blue on hover
    "button_glow": (0.5, 0.6, 0.8, 0.3),  # Glow effect
    "text": (0.1, 0.1, 0.1, 1),           # Dark text
    "accent": (0.2, 0.5, 0.8, 1),         # Blue accent
    "success": (0.2, 0.7, 0.2, 1),        # Green for success
    "error": (0.8, 0.2, 0.2, 1),          # Red for errors
    "border": (0.6, 0.6, 0.6, 1),         # Border color
}

CYBERPUNK_THEME = {
    "name": "Cyberpunk Chrome",
    "background": (0.05, 0.05, 0.05, 1),  # Almost black
    "panel": (0.1, 0.1, 0.1, 1),          # Dark grey panels
    "button": (0.2, 0.2, 0.25, 1),        # Dark metallic
    "button_hover": (0.3, 0.3, 0.35, 1),  # Lighter metallic
    "button_glow": (0, 0.8, 1, 0.4),      # Cyan glow
    "text": (0.9, 0.9, 0.9, 1),           # Light text
    "accent": (0, 0.8, 1, 1),             # Cyan accent
    "success": (0, 1, 0.3, 1),            # Neon green
    "error": (1, 0.1, 0.3, 1),            # Neon red
    "border": (0.4, 0.4, 0.4, 1),         # Grey border
}

# ============================================================================
# CONFIGURATION MANAGER
# ============================================================================

class ConfigManager:
    """Manages application configuration and theme persistence"""
    
    def __init__(self):
        self.config_file = "sftp_kivy_config.json"
        self.config = self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"Config load error: {e}")
        
        # Default configuration
        return {
            "theme_mode": "classic",  # classic or cyberpunk
            "window_size": [1400, 900],
            "last_sftp_host": "",
            "user_labels": ["Project A", "Project B", "Backup", "Deploy", "Archive"]
        }
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            logging.error(f"Config save error: {e}")

# ============================================================================
# THEMED WIDGETS
# ============================================================================

class ThemedButton(Button):
    """Button with theme-aware styling and glow effects"""
    
    def __init__(self, theme, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        self.apply_theme()
        self.bind(on_press=self.on_button_press)
        self.bind(on_release=self.on_button_release)
    
    def apply_theme(self):
        """Apply theme colors and styling"""
        self.background_color = self.theme["button"]
        self.color = self.theme["text"]
        self.background_normal = ''
        self.background_down = ''
        
        # Add rounded rectangle background
        with self.canvas.before:
            Color(*self.theme["button"])
            self.rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(8)]
            )
        
        self.bind(pos=self.update_rect, size=self.update_rect)
    
    def update_rect(self, *args):
        """Update rectangle position and size"""
        if hasattr(self, 'rect'):
            self.rect.pos = self.pos
            self.rect.size = self.size
    
    def on_button_press(self, *args):
        """Handle button press with visual feedback"""
        with self.canvas.before:
            Color(*self.theme["button_hover"])
            self.rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(8)]
            )
    
    def on_button_release(self, *args):
        """Handle button release"""
        with self.canvas.before:
            Color(*self.theme["button"])
            self.rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(8)]
            )

class ThemedLabel(Label):
    """Label with theme-aware styling"""
    
    def __init__(self, theme, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        self.color = theme["text"]

class ThemedPanel(BoxLayout):
    """Panel with theme-aware background"""
    
    def __init__(self, theme, **kwargs):
        super().__init__(**kwargs)
        self.theme = theme
        
        with self.canvas.before:
            Color(*theme["panel"])
            self.rect = RoundedRectangle(
                pos=self.pos,
                size=self.size,
                radius=[dp(5)]
            )
        
        self.bind(pos=self.update_rect, size=self.update_rect)
    
    def update_rect(self, *args):
        if hasattr(self, 'rect'):
            self.rect.pos = self.pos
            self.rect.size = self.size

# ============================================================================
# MAIN APPLICATION
# ============================================================================

class SFTPKivyApp(App):
    """Main SFTP Kivy Application"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.current_theme = self.get_current_theme()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - KIVY - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sftp_kivy.log'),
                logging.StreamHandler()
            ]
        )
        
        logging.info(f"SFTP Kivy App initialized - Theme: {self.current_theme['name']}")
    
    def get_current_theme(self):
        """Get the current theme based on configuration"""
        theme_mode = self.config_manager.config.get("theme_mode", "classic")
        return CLASSIC_THEME if theme_mode == "classic" else CYBERPUNK_THEME
    
    def build(self):
        """Build the main application interface"""
        # Set window background
        Window.clearcolor = self.current_theme["background"]
        
        # Main container
        main_layout = BoxLayout(orientation='vertical', padding=dp(10), spacing=dp(10))
        
        # Header with theme toggle
        header = self.create_header()
        main_layout.add_widget(header)
        
        # Content area with tabs
        content = self.create_content()
        main_layout.add_widget(content)
        
        # Status bar
        status_bar = self.create_status_bar()
        main_layout.add_widget(status_bar)
        
        return main_layout
    
    def create_header(self):
        """Create header with title and theme toggle"""
        header = ThemedPanel(self.current_theme, orientation='horizontal', size_hint_y=None, height=dp(60))
        
        # Title
        title = ThemedLabel(self.current_theme, text="V1's SFTP Doofer - Kivy Edition", 
                           font_size='20sp', size_hint_x=0.7)
        header.add_widget(title)
        
        # Theme toggle
        theme_layout = BoxLayout(orientation='horizontal', size_hint_x=0.3)
        
        theme_label = ThemedLabel(self.current_theme, text="Theme:", size_hint_x=0.5)
        theme_layout.add_widget(theme_label)
        
        theme_switch = Switch(active=(self.config_manager.config.get("theme_mode") == "cyberpunk"))
        theme_switch.bind(active=self.toggle_theme)
        theme_layout.add_widget(theme_switch)
        
        mode_label = ThemedLabel(self.current_theme, 
                                text="Cyberpunk" if theme_switch.active else "Classic",
                                size_hint_x=0.5)
        theme_layout.add_widget(mode_label)
        
        header.add_widget(theme_layout)
        return header
    
    def create_content(self):
        """Create main content area with tabs"""
        # Tabbed panel
        tab_panel = TabbedPanel(do_default_tab=False, tab_height=dp(40))
        
        # SFTP Tab
        sftp_tab = TabbedPanelItem(text='SFTP')
        sftp_content = self.create_sftp_tab()
        sftp_tab.add_widget(sftp_content)
        tab_panel.add_widget(sftp_tab)
        
        # Scheduler Tab
        scheduler_tab = TabbedPanelItem(text='Scheduler')
        scheduler_content = self.create_scheduler_tab()
        scheduler_tab.add_widget(scheduler_content)
        tab_panel.add_widget(scheduler_tab)
        
        # Settings Tab
        settings_tab = TabbedPanelItem(text='Settings')
        settings_content = self.create_settings_tab()
        settings_tab.add_widget(settings_content)
        tab_panel.add_widget(settings_tab)
        
        return tab_panel
    
    def create_sftp_tab(self):
        """Create SFTP tab content"""
        layout = BoxLayout(orientation='horizontal', spacing=dp(10))
        
        # Left panel - Local files
        left_panel = ThemedPanel(self.current_theme, orientation='vertical')
        left_title = ThemedLabel(self.current_theme, text="Local Files", 
                                font_size='16sp', size_hint_y=None, height=dp(40))
        left_panel.add_widget(left_title)
        
        # Placeholder for file browser
        left_content = ThemedLabel(self.current_theme, 
                                  text="Local file browser\nwill be implemented here...")
        left_panel.add_widget(left_content)
        
        layout.add_widget(left_panel)
        
        # Right panel - Remote files
        right_panel = ThemedPanel(self.current_theme, orientation='vertical')
        right_title = ThemedLabel(self.current_theme, text="Remote Files", 
                                 font_size='16sp', size_hint_y=None, height=dp(40))
        right_panel.add_widget(right_title)
        
        # Connection buttons
        button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50))
        
        connect_btn = ThemedButton(self.current_theme, text="Connect")
        connect_btn.bind(on_press=self.connect_sftp)
        button_layout.add_widget(connect_btn)
        
        test_btn = ThemedButton(self.current_theme, text="Test Connection")
        test_btn.bind(on_press=self.test_connection)
        button_layout.add_widget(test_btn)
        
        right_panel.add_widget(button_layout)
        
        # Placeholder for remote browser
        right_content = ThemedLabel(self.current_theme, 
                                   text="Remote file browser\nwill be implemented here...")
        right_panel.add_widget(right_content)
        
        layout.add_widget(right_panel)
        
        return layout
    
    def create_scheduler_tab(self):
        """Create scheduler tab content"""
        layout = ThemedPanel(self.current_theme, orientation='vertical')
        
        title = ThemedLabel(self.current_theme, text="Task Scheduler", 
                           font_size='18sp', size_hint_y=None, height=dp(50))
        layout.add_widget(title)
        
        content = ThemedLabel(self.current_theme, 
                             text="Interactive calendar and\nscheduler will be implemented here...")
        layout.add_widget(content)
        
        return layout
    
    def create_settings_tab(self):
        """Create settings tab content"""
        layout = ThemedPanel(self.current_theme, orientation='vertical')
        
        title = ThemedLabel(self.current_theme, text="Application Settings", 
                           font_size='18sp', size_hint_y=None, height=dp(50))
        layout.add_widget(title)
        
        content = ThemedLabel(self.current_theme, 
                             text="Configuration options\nwill be implemented here...")
        layout.add_widget(content)
        
        return layout
    
    def create_status_bar(self):
        """Create status bar"""
        status_bar = ThemedPanel(self.current_theme, orientation='horizontal', 
                                size_hint_y=None, height=dp(30))
        
        self.status_label = ThemedLabel(self.current_theme, 
                                       text=f"Ready - {self.current_theme['name']} Theme Active",
                                       font_size='12sp')
        status_bar.add_widget(self.status_label)
        
        return status_bar
    
    def toggle_theme(self, switch, value):
        """Toggle between classic and cyberpunk themes"""
        new_theme = "cyberpunk" if value else "classic"
        self.config_manager.config["theme_mode"] = new_theme
        self.config_manager.save_config()
        
        # Show restart message
        popup = Popup(title='Theme Changed',
                     content=Label(text='Restart the application\nto apply the new theme.'),
                     size_hint=(0.4, 0.3))
        popup.open()
        
        logging.info(f"Theme changed to: {new_theme}")
    
    def connect_sftp(self, button):
        """Handle SFTP connection"""
        self.status_label.text = "Connecting to SFTP server..."
        logging.info("SFTP connection initiated")
    
    def test_connection(self, button):
        """Handle connection testing"""
        self.status_label.text = "Testing connection..."
        logging.info("Connection test initiated")
    
    def on_stop(self):
        """Clean shutdown"""
        self.config_manager.save_config()
        logging.info("SFTP Kivy App shutdown complete")

# ============================================================================
# APPLICATION ENTRY POINT
# ============================================================================

if __name__ == '__main__':
    app = SFTPKivyApp()
    app.run()
