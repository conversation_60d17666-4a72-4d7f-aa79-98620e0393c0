﻿import tkinter as tk
import traceback
from tkinter import ttk, messagebox, filedialog, simpledialog, Menu
from PIL import Image, ImageTk
from moviepy.editor import VideoFileClip
from moviepy.video.io.bindings import PIL_to_npimage
from moviepy.video.io.bindings import mplfig_to_npimage
from moviepy.video.io.ImageSequenceClip import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from io import BytesIO
import numpy as np
import base64
import copy
import hashlib
import random
import glob
import os
import json
import threading
import time
from datetime import datetime, timedelta
import calendar
import shutil
import paramiko
import logging
import smtplib
from email.mime.text import MIMEText
from cryptography.fernet import Fernet, InvalidToken
import stat
from PIL import Image as PILImage
import pystray
import sys
from logging.handlers import RotatingFileHandler


try:
    from PIL import Image
    RESAMPLE_METHOD = Image.Resampling.LANCZOS
except AttributeError:
    RESAMPLE_METHOD = Image.ANTIALIAS

CONFIG_FILE = "sftp_config.json"
KEY_FILE = "sftp_key.key"
EMAIL_CONFIG_FILE = "email_config.json"
SCHEDULE_CONFIG_FILE = "schedule_config.json"
NOTIFICATION_CONFIG_FILE = "notification_settings.json"
LOCK_EMAIL_COOLDOWN_SECONDS = 900
notification_config = {"log_enabled": True}
LOG_FILE = "v1s_setting_doofer.log"
DEBUG_LOG_FILE = "v1s_debug_doofer.log"

if os.path.exists(NOTIFICATION_CONFIG_FILE):
    try:
        with open(NOTIFICATION_CONFIG_FILE, 'r') as f:
            notification_config.update(json.load(f))
    except Exception as e:
        pass 

def get_fernet():
    # Ideally this comes from a secure app-wide constant
    MASTER_SECRET = "sftp_toy_default_salt"  # 🔐 Replace with something app-specific
    key = hashlib.sha256(MASTER_SECRET.encode()).digest()
    return Fernet(base64.urlsafe_b64encode(key))

def backpropagate_to_credentials(config_manager, values, kind="sftp"):
    """
    Ensures that any saved changes in the SFTP or LAN settings dialog also
    update the corresponding default credential row stored in config.
    """
    key = f"{kind}_credentials"
    if key not in config_manager.config:
        return

    default_found = False
    for row in config_manager.config[key]:
        if row.get("default"):
            default_found = True
            for k in ("username", "password", "host", "port", "remote_path", "path"):
                if k in values and k in row:
                    row[k] = values[k]
            break

    if default_found:
        config_manager.save_config()

def deduplicate_lan_credentials(creds):
    seen = set()
    unique = []
    fernet = get_fernet()

    for cred in creds:
        username = cred.get("username", "").strip().lower()
        path = cred.get("path", "").strip()
        encrypted_pw = cred.get("password", "")

        # Attempt to decode
        try:
            decrypted_pw = fernet.decrypt(encrypted_pw.encode()).decode()
        except (InvalidToken, AttributeError):
            decrypted_pw = encrypted_pw  # fallback to raw if can't decrypt

        key = (username, decrypted_pw, path)
        if key not in seen:
            seen.add(key)
            unique.append(cred)
        else:
            print(f"[DevMode] Skipped duplicate LAN credential for: {key}")

    return unique

def setup_loggers(log_enabled=True, debug_enabled=False, log_file="v1s_setting_doofer.log", debug_log_file="v1s_debug_doofer.log"):
    from logging.handlers import RotatingFileHandler

    log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.handlers.clear()

    # Always log ERROR and WARNING to debug log if debug is enabled
    if debug_enabled:
        debug_handler = RotatingFileHandler(debug_log_file, maxBytes=2_000_000, backupCount=5)
        debug_handler.setLevel(logging.DEBUG)
        debug_handler.setFormatter(log_formatter)
        root_logger.addHandler(debug_handler)

    # Only log INFO to standard log file if logging is enabled
    if log_enabled:
        main_handler = RotatingFileHandler(log_file, maxBytes=1_000_000, backupCount=3)
        main_handler.setLevel(logging.INFO)
        main_handler.setFormatter(log_formatter)
        root_logger.addHandler(main_handler)

# Initial logging setup (uses notification_config and constants)
setup_loggers(
    log_enabled=notification_config.get("log_enabled", True),
    debug_enabled=notification_config.get("debug_enabled", False),
    log_file=LOG_FILE,
    debug_log_file=DEBUG_LOG_FILE
)

def safe_info(message):
    if notification_config.get("log_enabled", True):
        logging.info(message)

def safe_error(message):
    if notification_config.get("log_enabled", True):
        logging.error(message)

def safe_debug(message):
    if notification_config.get("debug_enabled", False):
        logging.debug(message)

def ensure_lock_config_in_sftp_config():
    if not os.path.exists(CONFIG_FILE):
        return

    try:
        with open(CONFIG_FILE, "r") as f:
            config = json.load(f)
    except Exception as e:
        safe_error(f"Failed to read {CONFIG_FILE}: {e}")
        return

    if "app_lock" not in config:
        config["app_lock"] = {
            "enabled": False,
            "password": ""
        }
        try:
            with open(CONFIG_FILE, "w") as f:
                json.dump(config, f, indent=4)
            safe_log("Inserted app_lock section into sftp_config.json")
        except Exception as e:
            safe_error(f"Failed to write updated config: {e}")

def ensure_default_files_exist():
    defaults = {
        CONFIG_FILE: {
            "sftp": {},
            "schedules": [],
            "clipboard": None,
            "enable_dir_copy": True
        },
        SCHEDULE_CONFIG_FILE: [],
        EMAIL_CONFIG_FILE: {
            "smtp_server": "",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "from": "",
            "to": [],
            "upload_failed": True,
            "upload_success": False,
            "scheduler_paused": True,
            "scheduler_pause_threshold": 600
        },
        NOTIFICATION_CONFIG_FILE: {
            "log_enabled": True,
            "email_enabled": False
        }
    }

    for path, content in defaults.items():
        if not os.path.exists(path):
            try:
                with open(path, 'w') as f:
                    json.dump(content, f, indent=4)
                safe_log(f"Created default config: {path}")
            except Exception as e:
                safe_error(f"Failed to create default config for {path}: {e}")

def set_default_lan_credential(rows, selected_var, config_manager=None, log_fn=None, refresh_fn=None):
    if not hasattr(selected_var, 'get'):
        return

    for row in rows:
        if not isinstance(row, dict):
            continue
        var = row.get("default_var")
        if var and var != selected_var:
            var.set(False)
        elif var == selected_var and var.get():
            path_entry = row.get("path_entry")
            if path_entry and hasattr(path_entry, 'get'):
                path = path_entry.get().strip()
                if path and config_manager:
                    config_manager.config["lan_path"] = path
                    if log_fn:
                        log_fn(f"Default LAN path updated to: {path}")
                    if refresh_fn:
                        refresh_fn()

def set_default_credential_row(rows, selected_var, config_manager=None, log_fn=None, refresh_fn=None):
    if not hasattr(selected_var, 'get'):
        return

    for row in rows:
        if not isinstance(row, dict):
            continue
        var = row.get("default_var")
        if var and var != selected_var:
            var.set(False)
        elif var == selected_var and var.get():
            path_entry = row.get("path_entry")
            if path_entry and hasattr(path_entry, 'get'):
                path = path_entry.get().strip()
                if path and config_manager:
                    # Determine storage key based on credential type
                    kind = row.get("kind", "")
                    if kind == "lan":
                        config_manager.config["lan_path"] = path
                        if log_fn:
                            log_fn(f"[LAN] Default path set to: {path}")
                    elif kind == "sftp":
                        config_manager.config["sftp_path"] = path
                        if log_fn:
                            log_fn(f"[SFTP] Default path set to: {path}")
                    if refresh_fn:
                        refresh_fn()

# Encryption
if not os.path.exists(KEY_FILE):
    key = Fernet.generate_key()
    with open(KEY_FILE, 'wb') as f:
        f.write(key)
with open(KEY_FILE, 'rb') as f:
    fernet = Fernet(f.read())

# Notification helper
notification_config = {"log_enabled": True, "email_enabled": False}
if os.path.exists(NOTIFICATION_CONFIG_FILE):
    try:
        with open(NOTIFICATION_CONFIG_FILE, "r") as f:
            notification_config = json.load(f)
    except:
        pass

def load_email_config():
    try:
        if os.path.exists(EMAIL_CONFIG_FILE):
            try:
                with open(EMAIL_CONFIG_FILE, 'r') as f:
                    return json.load(f)
            except OSError as e:
                print(f"Email config load error: {e}")
    except Exception as e:
        safe_error(f"Failed to load email config: {e}")
    return {}

def notify(message):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted = f"{timestamp} - {message}"

    if notification_config.get("log_enabled"):
        safe_log(message)

    if notification_config.get("email_enabled"):
        send_email("V1 Notification", formatted)

def safe_log(message):
    if notification_config.get("log_enabled", True):
        logging.info(message)

def safe_error(message):
    if (
        notification_config.get("log_enabled", True)
        or notification_config.get("debug_enabled", False)
    ):
        logging.error(message)

def safe_debug(message):
    if not notification_config.get("debug_enabled", False):
        return
    logging.debug(message)

# Call early during startup
ensure_default_files_exist()

class NotificationSettingsDialog(tk.Toplevel):
    def __init__(self, master):
        super().__init__(master)
        self.title("Notification Settings")
        self.geometry("300x150")

        self.log_var = tk.BooleanVar(value=notification_config.get("log_enabled", True))
        self.email_var = tk.BooleanVar(value=notification_config.get("email_enabled", False))

        ttk.Checkbutton(self, text="Enable Logging", variable=self.log_var).pack(anchor="w", padx=10, pady=5)
        ttk.Checkbutton(self, text="Enable Email Notifications", variable=self.email_var).pack(anchor="w", padx=10, pady=5)
        ttk.Button(self, text="Save", command=self.save).pack(pady=10)

    def save(self):
        notification_config["log_enabled"] = self.log_var.get()
        notification_config["email_enabled"] = self.email_var.get()
        with open(NOTIFICATION_CONFIG_FILE, "w") as f:
            json.dump(notification_config, f, indent=2)
        self.destroy()

def load_app_lock_config():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r") as f:
                config = json.load(f)
                return config.get("app_lock", {})
    except Exception as e:
        safe_error(f"Failed to load app lock from main config: {e}")
    return {}


def save_app_lock_config(lock_data):
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r") as f:
                config = json.load(f)
        else:
            config = {}

        config["app_lock"] = lock_data

        with open(CONFIG_FILE, "w") as f:
            json.dump(config, f, indent=4)
        safe_log("App lock config saved to main config.")
    except Exception as e:
        safe_error(f"Failed to save app lock to main config: {e}")

class PathLabel(ttk.Frame):
    def __init__(self, parent, path_var, refresh_callback, master=None, remote=False):
        super().__init__(parent)
        self.path_var = path_var
        self.refresh_callback = refresh_callback
        self.master = master  # Usually the MainApp instance
        self.remote = remote

        self.label = ttk.Label(self, textvariable=self.path_var, anchor="w")
        self.label.pack(fill=tk.X, expand=True)

        self.label.bind("<Button-1>", self.go_up)
        self.label.bind("<Button-3>", self.show_context_menu)

    def go_up(self, event=None):
        current = self.path_var.get()
        parent = os.path.dirname(current)

        if parent == current:
            drives = self.get_drives()
            if drives:
                selected = self.select_drive(drives)
                if selected:
                    self.path_var.set(selected)
                    if self.master:
                        self.master.local_path = selected
        else:
            self.path_var.set(parent)
            if self.master:
                self.master.local_path = parent

        if not self.remote or (self.master and getattr(self.master, "sftp_enabled", None) and self.master.sftp_enabled.get()):
            self.refresh_callback()

    def show_context_menu(self, event):
        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Copy Path", command=self.copy_path)
        menu.tk_popup(event.x_root, event.y_root)

    def copy_path(self):
        try:
            self.clipboard_clear()
            self.clipboard_append(self.path_var.get())
            self.update()
            messagebox.showinfo("Copied", f"Path copied to clipboard:\n{self.path_var.get()}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy path: {e}")

    def get_drives(self):
        if os.name == 'nt':
            import string
            return [f"{d}:/" for d in string.ascii_uppercase if os.path.exists(f"{d}:/")]
        else:
            return ['/']

    def select_drive(self, drives):
        win = tk.Toplevel(self)
        win.title("Select Drive")
        var = tk.StringVar()

        for d in drives:
            ttk.Radiobutton(win, text=d, value=d, variable=var).pack(anchor="w")

        ttk.Button(win, text="OK", command=win.destroy).pack(pady=5)

        win.grab_set()
        self.wait_window(win)

        return var.get()

def send_email(subject, body):
    if not os.path.exists(EMAIL_CONFIG_FILE):
        safe_error("Email config file not found.")
        messagebox.showerror("Email Error", "Email config file not found.")
        return

    try:
        with open(EMAIL_CONFIG_FILE, 'r') as f:
            config = json.load(f)
    except OSError as e:
        print(f"Email config load error: {e}")
        return
    except Exception as e:
        safe_error(f"Error reading email configuration: {e}")
        messagebox.showerror("Email Error", f"Error reading email configuration: {e}")
        return

    required_keys = ['smtp_server', 'smtp_port', 'username', 'password', 'from', 'to']
    if not all(k in config for k in required_keys):
        safe_error("Incomplete email configuration.")
        messagebox.showerror("Email Error", "Incomplete email configuration.")
        return

    msg = MIMEText(body)
    msg['Subject'] = subject
    msg['From'] = config['from']
    msg['To'] = ", ".join(config['to']) if isinstance(config['to'], list) else config['to']

    try:
        with smtplib.SMTP(config['smtp_server'], config['smtp_port']) as server:
            server.starttls()
            server.login(config['username'], config['password'])
            server.sendmail(config['from'], config['to'], msg.as_string())
        safe_log("Email sent successfully.")
    except Exception as smtp_error:
        safe_error(f"Failed to send email: {smtp_error}")
        messagebox.showerror("Email Error", f"Failed to send email: {smtp_error}")


class SetPasswordDialog(tk.Toplevel):
    def __init__(self, master):
        super().__init__(master)
        self.title("Set App Lock Password")
        self.geometry("300x150")
        self.app = master  # Reference to MainApp

        ttk.Label(self, text="Enter Password:").pack(pady=(10, 0))
        self.pw1_var = tk.StringVar()
        ttk.Entry(self, textvariable=self.pw1_var, show="*").pack()

        ttk.Label(self, text="Confirm Password:").pack(pady=(10, 0))
        self.pw2_var = tk.StringVar()
        ttk.Entry(self, textvariable=self.pw2_var, show="*").pack()

        ttk.Button(self, text="Set Password", command=self.save_password).pack(pady=10)

    def save_password(self):
        pw1 = self.pw1_var.get()
        pw2 = self.pw2_var.get()

        if not pw1:
            messagebox.showerror("Error", "Password cannot be empty.")
            return

        if pw1 != pw2:
            messagebox.showerror("Error", "Passwords do not match.")
            return

        try:
            encrypted = fernet.encrypt(pw1.encode()).decode()
            save_app_lock_config({"password": encrypted})
            # Save into the expected nested config structure
            self.app.config_manager.config.setdefault('app_lock', {})['password'] = encrypted
            
            self.app.config_manager.save_config()  # Persist change immediately
            messagebox.showinfo("Saved", "Password set successfully.")
            self.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to set password: {e}")

class LockScreen(tk.Toplevel):
    def __init__(self, master, fernet):
        super().__init__(master)
        self.fernet = fernet

        # Improved resolution of main app instance
        try:
            if hasattr(master, "get_lockscreen_image_source"):
                self.app = master
            elif hasattr(master, "master") and hasattr(master.master, "get_lockscreen_image_source"):
                self.app = master.master
            elif hasattr(master, "winfo_toplevel"):
                toplevel = master.winfo_toplevel()
                if hasattr(toplevel, "get_lockscreen_image_source"):
                    self.app = toplevel
                else:
                    self.app = None
            else:
                self.app = None

            if not self.app:
                safe_error("[LockScreen] Could not resolve app with get_lockscreen_image_source.")

        except Exception as e:
            self.app = None
            safe_error(f"[LockScreen] App reference resolution error: {e}")

        self.title("Application Locked")

        self.geometry("500x300")
        self.attributes('-topmost', True)
        self.grab_set()
        self.resizable(False, False)
        self.protocol("WM_DELETE_WINDOW", lambda: None)
        self.after(1000, self.check_minimize_to_tray)
        self.cooldown_active = False

        self.bg_frame = tk.Frame(self)
        self.bg_frame.pack(fill="both", expand=True)

        self.bg_label = tk.Label(self.bg_frame)
        self.bg_label.place(x=0, y=0, relwidth=1, relheight=1)

        self.set_background_media()
        self.build_overlay()
        self.update_idletasks()
        try:
            self.update_idletasks()
            app_x = self.app.winfo_rootx()
            app_y = self.app.winfo_rooty()
            app_w = self.app.winfo_width()
            app_h = self.app.winfo_height()
            win_w = 500
            win_h = 300
            pos_x = app_x + (app_w // 2) - (win_w // 2)
            pos_y = app_y + (app_h // 2) - (win_h // 2)
            self.geometry(f"{win_w}x{win_h}+{pos_x}+{pos_y}")
        except Exception as e:
            safe_error(f"[LockScreen] Failed to center: {e}")


    def set_background_media(self):
        try:
            source = self.app.get_lockscreen_image_source()
            safe_log(f"[LockScreen] Background source resolved: {source}")
        except Exception as e:
            safe_error(f"[LockScreen] Failed to fetch image source: {e}")
            return

        supported_images = (".png", ".jpg", ".jpeg", ".bmp")
        supported_gifs = (".gif",)

        if isinstance(source, str):
            if source == "__DEFAULT__":
                safe_log("[LockScreen] Using default lock_image* fallback")
                candidates = [f for f in os.listdir('.') if f.lower().startswith("lock_image") and f.lower().endswith(supported_images + supported_gifs)]
                safe_log(f"[LockScreen] Default candidates: {candidates}")
                if not candidates:
                    safe_log("[LockScreen] No default image files found")
                    return
                source = os.path.abspath(random.choice(candidates))

            if not os.path.exists(source):
                safe_error(f"[LockScreen] File not found: {source}")
                return

            if source.lower().endswith(supported_gifs):
                safe_log(f"[LockScreen] Playing GIF: {source}")
                self.play_gif(source)
            else:
                try:
                    img = Image.open(source)
                    img = img.resize((500, 300), Image.Resampling.LANCZOS)
                    self.bg_img = ImageTk.PhotoImage(img)
                    self.bg_label.config(image=self.bg_img)
                    safe_log(f"[LockScreen] Loaded image: {source}")
                except Exception as e:
                    safe_error(f"[LockScreen] Error loading image: {e}")

        elif isinstance(source, dict) and "folder" in source:
            folder = source["folder"]
            cycle = source.get("cycle_minutes", 5)
            safe_log(f"[LockScreen] Loading from folder: {folder}, cycle = {cycle}")
            try:
                files = [f for f in os.listdir(folder) if f.lower().endswith(supported_images + supported_gifs)]
                safe_log(f"[LockScreen] Folder candidates: {files}")
                if not files:
                    safe_log("[LockScreen] No valid files found in folder")
                    return
                selected = os.path.join(folder, random.choice(files))
                if selected.lower().endswith(supported_gifs):
                    safe_log(f"[LockScreen] Playing GIF from folder: {selected}")
                    self.play_gif(selected)
                else:
                    img = Image.open(selected)
                    img = img.resize((500, 300), Image.Resampling.LANCZOS)
                    self.bg_img = ImageTk.PhotoImage(img)
                    self.bg_label.config(image=self.bg_img)
                    safe_log(f"[LockScreen] Loaded image from folder: {selected}")
            except Exception as e:
                safe_error(f"[LockScreen] Failed to load from folder: {e}")

    def play_gif(self, path):
        def loop():
            try:
                img = Image.open(path)
                frames = []
                delays = []
                try:
                    while True:
                        frame = img.copy()
                        frames.append(ImageTk.PhotoImage(frame.resize((500, 300), Image.Resampling.LANCZOS)))
                        delays.append(img.info.get("duration", 100))
                        img.seek(len(frames))
                except EOFError:
                    pass

                if not frames:
                    safe_error(f"No frames found in gif: {path}")
                    return

                while self.winfo_exists():
                    for i, frame in enumerate(frames):
                        if not self.winfo_exists():
                            return
                        self.bg_label.config(image=frame)
                        self.bg_label.image = frame
                        time.sleep(delays[i] / 1000.0)
            except Exception as e:
                safe_error(f"Failed to play gif {path}: {e}")

        threading.Thread(target=loop, daemon=True).start()


    def minimize_to_tray(self):
        self.withdraw()
        image = PILImage.new('RGB', (64, 64), color='gray')
        def on_restore(icon, item):
            self.tray_icon_shown = False
            icon.stop()
            self.deiconify()
            self.lift()
            self.attributes('-topmost', True)
            self.after(300, lambda: self.attributes('-topmost', False))
        def on_quit(icon, item):
            icon.stop()
            self.destroy()
            sys.exit()
        menu = pystray.Menu(
            pystray.MenuItem('Restore', on_restore),
            pystray.MenuItem('Exit', on_quit)
        )
        self.tray_icon = pystray.Icon('V1 Lock', image, 'App Locked', menu)
        threading.Thread(target=self.tray_icon.run, daemon=True).start()

    def check_minimize_to_tray(self):
        if self.state() == 'iconic' and not hasattr(self, 'tray_icon_shown') and self.master.minimize_to_tray_var.get():
            self.tray_icon_shown = True
            self.minimize_to_tray()
        self.after(1000, self.check_minimize_to_tray)

    def build_overlay(self):
        font_opts = ("Segoe UI", 10)

        self.label = ttk.Label(self.bg_frame, text="Enter Password to Unlock:", font=font_opts)
        self.label.place(relx=0.5, rely=0.2, anchor="center")

        self.password_var = tk.StringVar()
        self.entry = ttk.Entry(self.bg_frame, textvariable=self.password_var, show="*", font=font_opts)
        self.entry.place(relx=0.5, rely=0.35, anchor="center", width=200)
        self.entry.focus()

        self.unlock_btn = ttk.Button(self.bg_frame, text="Unlock", command=self.attempt_unlock)
        self.unlock_btn.place(relx=0.5, rely=0.5, anchor="center")

        self.forgot_btn = ttk.Button(self.bg_frame, text="Forgot Password", command=self.send_unlock_email)
        self.forgot_btn.place(relx=0.5, rely=0.65, anchor="center")
        self.entry.bind("<Return>", lambda e: self.attempt_unlock())

    def attempt_unlock(self):
        entered = self.password_var.get()
        try:
            with open(CONFIG_FILE) as f:
                config = json.load(f)
            encrypted_pw = config.get("app_lock", {}).get("password", "")

            if not encrypted_pw:
                messagebox.showinfo("Unlocked", "No password is set.", parent=self)
                self.grab_release()
                self.destroy()
                return

            decrypted_pw = self.fernet.decrypt(encrypted_pw.encode()).decode()

            if entered == decrypted_pw:
                self.grab_release()
                self.destroy()
            else:
                messagebox.showerror("Error", "Incorrect password.")
        except Exception as e:
            messagebox.showerror("Error", f"Password check failed: {e}")

    def send_unlock_email(self):
        if self.cooldown_active:
            messagebox.showinfo("Cooldown", "Please wait before requesting another email.")
            return

        try:
            with open(CONFIG_FILE) as f:
                cfg = json.load(f)
            encrypted_pw = cfg.get("app_lock", {}).get("password")

            if not encrypted_pw:
                messagebox.showerror("Error", "No password is set.")
                return

            decrypted_pw = self.fernet.decrypt(encrypted_pw.encode()).decode()

            with open(EMAIL_CONFIG_FILE) as f:
                email_cfg = json.load(f)

            recipient = email_cfg.get("to")
            if isinstance(recipient, list):
                recipient = recipient[0] if recipient else None

            if not recipient:
                messagebox.showerror("Error", "No recipient configured in email settings.")
                return

            body = f"You requested your application unlock password.\n\nPassword: {decrypted_pw}"
            msg = MIMEText(body)
            msg["Subject"] = "Unlock Password Reminder"
            msg["From"] = email_cfg["from"]
            msg["To"] = recipient

            with smtplib.SMTP(email_cfg["smtp_server"], email_cfg["smtp_port"]) as server:
                server.starttls()
                server.login(email_cfg["username"], email_cfg["password"])
                server.sendmail(email_cfg["from"], recipient, msg.as_string())

            messagebox.showinfo("Sent", "Reminder email sent.")
            self.start_cooldown()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to send email: {e}")

    def start_cooldown(self):
        self.cooldown_active = True
        self.forgot_btn.config(state="disabled")
        threading.Thread(target=self._cooldown_timer, daemon=True).start()

    def _cooldown_timer(self):
        time.sleep(LOCK_EMAIL_COOLDOWN_SECONDS)
        if self.forgot_btn.winfo_exists():
            self.forgot_btn.config(state="normal")
        self.cooldown_active = False

class SFTPManager:
    def __init__(self, host, port, username, password):
        self.transport = None
        self.client = None
        self.home_path = ""
        self.remote_path = ""
        self.connected = False
        try:
            safe_debug("Creating Transport-based SFTP connection")
            self.transport = paramiko.Transport((host, int(port)))
            safe_debug(f"Connecting to {host}:{port} as {username}")
            self.transport.connect(username=username, password=password)
            self.client = paramiko.SFTPClient.from_transport(self.transport)

            self.client.chdir(".")
            self.home_path = self.client.getcwd()
            self.remote_path = self.home_path
            self.client.listdir(self.home_path)

            self.connected = True
            safe_log("Authentication (Transport-based) successful")
        except Exception as e:
            self.connected = False
            safe_error(f"SFTP connection failed: {e}")
            raise

    def listdir(self, path=None):
        if not self.connected:
            raise RuntimeError("SFTP not connected")
        target_path = path if path else self.home_path
        return self.client.listdir_attr(target_path)

    def upload(self, local_path, remote_path):
        self.client.put(local_path, remote_path)

    def download(self, remote_path, local_path):
        try:
            temp_path = local_path + ".partial"
            self.client.get(remote_path, temp_path)

            remote_stat = self.client.stat(remote_path)
            local_size = os.path.getsize(temp_path)
            remote_size = remote_stat.st_size

            if local_size != remote_size:
                # Retry once
                safe_log(f"Retrying download due to size mismatch: {local_size} != {remote_size}")
                self.client.get(remote_path, temp_path)
                local_size = os.path.getsize(temp_path)
                if local_size != remote_size:
                    raise ValueError(f"size mismatch in get!  {local_size} != {remote_size}")

            os.replace(temp_path, local_path)
            safe_log(f"Downloaded {remote_path} to {local_path}")
        except Exception as e:
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise RuntimeError(f"Failed to download {remote_path}: {e}")

    def remove(self, remote_path):
        self.client.remove(remote_path)

    def upload_throttled(self, local_path, remote_path, max_chunk_size=32768):
        with open(local_path, "rb") as f:
            with self.client.file(remote_path, "wb") as remote:
                remote.set_pipelined(True)
                while True:
                    chunk = f.read(max_chunk_size)
                    if not chunk:
                        break
                    remote.write(chunk)
    def download_throttled(self, remote_path, local_path, max_chunk_size=32768):
        with self.client.file(remote_path, "rb") as remote:
            with open(local_path, "wb") as f:
                while True:
                    chunk = remote.read(max_chunk_size)
                    if not chunk:
                        break
                    f.write(chunk)
    def close(self):
        if self.client:
            self.client.close()
        if self.transport:
            self.transport.close()


class ManualWindow(tk.Toplevel):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.title("User Manual")
        self.geometry("800x600")

        text_frame = ttk.Frame(self)
        text_frame.pack(fill="both", expand=True)

        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side="right", fill="y")

        self.text = tk.Text(text_frame, wrap="word", yscrollcommand=scrollbar.set)
        self.text.pack(fill="both", expand=True)
        scrollbar.config(command=self.text.yview)

        self.insert_manual_text()
        self.toggle_night_mode()

    def insert_manual_text(self):
        content = """
        V1's Setting Doofer Manual

        Overview:
        This tool allows you to upload your serversettings.ini file to a remote server via SFTP. It includes a scheduler, file browser, side-by-side INI viewer, and logging features.

        Features:
        - SFTP Connection with optional throttle mode.
        - Scheduler with calendar view and per-schedule options.
        - Dual-pane file browser (local and remote).
        - serversettings.ini viewer with comparison mode.
        - Night mode and bright color calendar options.
        - Tray minimization and application locking with email recovery.
        - Manual and Credits from the menu.

        Using the App:
        1. Configure SFTP from Settings > SFTP Settings.
        2. Load or create a serversettings.ini file in the local view.
        3. Use the scheduler to automate uploads at set times.
        4. Optionally enable email notifications on success/failure.
        5. Toggle night mode or lock the app for security.

        Tip: Right-click schedules for editing, copying, and property views.

        Enjoy!
        """
        self.text.insert("1.0", content)
        self.text.config(state="disabled")

    def toggle_night_mode(self):
        if self.master and hasattr(self.master, "night_mode_var"):
            self.is_night_mode = self.master.night_mode_var.get()

        if self.is_night_mode:
            self.configure(bg="#2a2a2e")
            self.text.configure(
                bg="#2a2a2e",
                fg="#ffffff",
                insertbackground="#ffffff"
            )
        else:
            self.configure(bg="#f0f0f0")
            self.text.configure(
                bg="#f0f0f0",
                fg="#000000",
                insertbackground="#000000"
            )

class LANSettingsDialog(tk.Toplevel):
    def __init__(self, master, config_manager, is_night_mode=False):
        super().__init__(master)
        self.app = master
        self.config_manager = config_manager
        self.is_night_mode = is_night_mode
        self.fernet = get_fernet()

        self.entries = {}
        self.credential_rows = []
        self.initial_lan_snapshot = copy.deepcopy(self.config_manager.config.get("lan_credentials", []))

        self.title("LAN Settings")
        self.geometry("700x400")
        self.transient(master)
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.protocol("WM_DELETE_WINDOW", self.confirm_on_close)
        self.enable_var = tk.BooleanVar()
        self.create_widgets()
        self.load_settings()

        # if not os.path.exists(KEY_FILE):
        #     with open(KEY_FILE, "wb") as f:
        #         f.write(Fernet.generate_key())

        # with open(KEY_FILE, "rb") as f:
        #     key = f.read()

        # self.fernet = Fernet(key)

        # self.create_widgets()
        # self.load_settings()

    def create_widgets(self):
        container = ttk.Frame(self)
        container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(container, text="LAN Path:").grid(row=0, column=0, sticky="w")
        lan_path_var = tk.StringVar()
        path_entry = ttk.Entry(container, textvariable=lan_path_var)
        path_entry.grid(row=0, column=1, sticky="ew")
        container.columnconfigure(1, weight=1)

        self.entries["lan_path"] = path_entry
        self.lan_path_var = lan_path_var

        self.lan_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(container, text="Enable LAN Mode", variable=self.lan_enabled_var).grid(row=1, column=1, sticky="w")

        ttk.Label(container, text="Saved LAN Credentials", font=("Segoe UI", 10, "bold")).grid(row=2, column=0, columnspan=2, pady=(10, 2), sticky="w")

        self.cred_section = ttk.Frame(container)
        self.cred_section.grid(row=3, column=0, columnspan=2, sticky="ew")

        ttk.Button(container, text="Add Credential", command=lambda: self.add_new_credential_row(self.cred_section)).grid(row=98, column=1, sticky="e", pady=(10, 5))
        ttk.Button(container, text="Save", command=self.save_lan_settings).grid(row=99, column=1, sticky="e", pady=(5, 10))

    def build_lan_credential_row(self, parent, label_text, username, encrypted_pw, default_path="", enabled=True, default=False):
        frame = ttk.Frame(parent)
        frame.grid(sticky="ew", padx=5, pady=5)

        default_var = tk.BooleanVar(value=default)
        enabled_var = tk.BooleanVar(value=enabled)

        ttk.Label(frame, text=label_text).grid(row=0, column=0, padx=2)

        user_entry = ttk.Entry(frame, width=15)
        user_entry.insert(0, username)
        user_entry.grid(row=0, column=1)

        pw_entry = ttk.Entry(frame, show="*", width=15)
        pw_entry.insert(0, "********" if encrypted_pw else "")
        pw_entry.grid(row=0, column=2, padx=2)

        show_btn = ttk.Button(frame, text="Reveal", command=lambda: self.show_password_securely(pw_entry, encrypted_pw))
        show_btn.grid(row=0, column=3, padx=2)

        ttk.Checkbutton(frame, variable=enabled_var, text="Enabled").grid(row=0, column=4, padx=2)

        path_entry = ttk.Entry(frame, width=25)
        path_entry.insert(0, default_path or "")
        path_entry.grid(row=0, column=5, padx=5)

        ttk.Checkbutton(
            frame,
            text="Default",
            variable=default_var,
            command=lambda v=default_var: set_default_lan_credential(
                self.credential_rows,
                v,
                config_manager=self.config_manager,
                log_fn=getattr(self, 'log', None),
                refresh_fn=getattr(self, 'refresh_remote_tree', None)
            )
        ).grid(row=0, column=6, padx=5)

        return {
            "frame": frame,
            "label": label_text,
            "user_entry": user_entry,
            "pw_entry": pw_entry,
            "path_entry": path_entry,
            "enabled_var": enabled_var,
            "default_var": default_var,
            "encrypted_pw": encrypted_pw
        }

    # def set_default_lan_credential(self, selected_var):
    #     for row in self.credential_rows:
    #         if row.get("default_var") and row["default_var"] != selected_var:
    #             row["default_var"].set(False)

    def confirm_on_close(self):
        if self.credential_rows != self.initial_lan_snapshot:
            if messagebox.askyesno("Unsaved Changes", "Save changes before closing?"):
                self.save_lan_settings()
        self.destroy()



    def ensure_master_password(self):
        app_lock = self.config_manager.config.setdefault("app_lock", {})
        if not app_lock.get("password"):
            pw1 = simpledialog.askstring("Set Master Password", "Enter a new master password:", show="*")
            if not pw1:
                messagebox.showwarning("Aborted", "Master password setup cancelled.")
                return False
            pw2 = simpledialog.askstring("Confirm Password", "Re-enter the master password:", show="*")
            if pw1 != pw2:
                messagebox.showerror("Mismatch", "Passwords did not match. Try again.")
                return False
            encrypted = fernet.encrypt(pw1.encode()).decode()
            app_lock["password"] = encrypted
            self.config_manager.save_config()
            logging.info("Master password has been set.")
            if hasattr(self, 'app') and hasattr(self.app, 'log'):
                self.app.log("Master password created.")
            messagebox.showinfo("Set", "Master password saved.")
        return True

    def load_settings(self):
        try:
            lan_path = self.config_manager.config.get("lan_path", "")
            self.lan_path_var.set(lan_path)
            self.enable_var.set(self.config_manager.config.get("lan_enabled", False))

            # Clear previous rows
            self.credential_rows.clear()
            for widget in self.cred_section.winfo_children():
                widget.destroy()

            saved_creds = self.config_manager.config.get("lan_credentials", [])
            default_path = None

            for i, rowdata in enumerate(saved_creds):
                user = rowdata.get("username", "")
                encrypted_pw = rowdata.get("password", "")
                label = rowdata.get("label", f"User {i + 1}")
                enabled = rowdata.get("enabled", True)
                path = rowdata.get("path", "")
                default_checked = rowdata.get("default", False)

                if default_checked and enabled:
                    default_path = path

                row_dict = self.build_lan_credential_row(
                    self.cred_section, label, user, encrypted_pw,
                    enabled=enabled,
                    default=default_checked,
                    default_path=path
                )
                self.credential_rows.append(row_dict)

            if not saved_creds:
                for i in range(2):
                    label = f"User {i + 1}"
                    row_dict = self.build_lan_credential_row(
                        self.cred_section, label, "", "",
                        enabled=True,
                        default=False,
                        default_path=""
                    )
                    self.credential_rows.append(row_dict)

            if default_path:
                self.lan_path_var.set(default_path)

        except Exception as e:
            logging.error(f"Error loading LAN settings: {e}")
            messagebox.showerror("Error", f"Could not load LAN Settings: {e}")


    def save_lan_settings(self):
        try:
            lan_path = self.entries["lan_path"].get().strip()
            self.config_manager.config["lan_path"] = lan_path
            self.config_manager.config["lan_enabled"] = self.lan_enabled_var.get()

            # Backpropagate path to default LAN credential
            backpropagate_to_credentials(
                self.config_manager,
                {"path": lan_path},
                kind="lan"
            )

            new_creds = []
            default_found = False

            for row in self.credential_rows:
                user = row["user_entry"].get().strip()
                pw = row["pw_entry"].get().strip()
                label = row.get("label", "")
                path = row["path_entry"].get().strip()
                enabled = row["enabled_var"].get()
                encrypted_pw = row.get("encrypted_pw", "")

                if not user and not pw and not path:
                    continue

                if pw == "********" and encrypted_pw:
                    encrypted = encrypted_pw
                elif pw:
                    encrypted = self.fernet.encrypt(pw.encode()).decode()
                else:
                    encrypted = ""

                is_default = row["default_var"].get()
                if is_default:
                    if default_found:
                        is_default = False
                    else:
                        default_found = True

                new_creds.append({
                    "label": label,
                    "username": user,
                    "password": encrypted,
                    "path": path,
                    "enabled": enabled,
                    "default": is_default
                })

            self.config_manager.config["lan_credentials"] = deduplicate_lan_credentials(new_creds)
            self.config_manager.save_config()
            messagebox.showinfo("Saved", "LAN settings saved successfully.")
            self.destroy()
        except Exception as e:
            logging.error(f"Error saving LAN settings: {e}")
            messagebox.showerror("Error", f"Failed to save LAN settings: {e}")




    def show_password_securely(self, entry_widget, encrypted_pw):
        try:
            decrypted = self.fernet.decrypt(encrypted_pw.encode()).decode()
            entry_widget.config(show="")
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, decrypted)
        except Exception as e:
            messagebox.showerror("Error", f"Could not reveal password: {e}")

    def hide_password(self, entry_widget):
        entry_widget.config(state='normal')
        entry_widget.delete(0, tk.END)
        entry_widget.insert(0, "********")
        entry_widget.config(show="*")
        logging.debug("Password auto-hidden.")

    def ensure_master_password(self):
        app_lock = self.config_manager.config.setdefault("app_lock", {})
        if not app_lock.get("password"):
            existing_creds = self.config_manager.config.get("lan_credentials", [])
            if existing_creds:
                proceed = messagebox.askyesno(
                    "Security Warning",
                    "You are about to set a master password, but existing LAN credentials are stored without protection.\n\n"
                    "These credentials will need to be reset or manually reviewed.\n\nProceed anyway?"
                )
                if not proceed:
                    return False

            pw1 = simpledialog.askstring("Set Master Password", "Enter a new master password:", show="*")
            if not pw1:
                messagebox.showwarning("Aborted", "Master password setup was cancelled.")
                return False
            pw2 = simpledialog.askstring("Confirm Password", "Re-enter master password:", show="*")
            if pw1 != pw2:
                messagebox.showerror("Mismatch", "Passwords did not match. Try again.")
                return False

            encrypted = fernet.encrypt(pw1.encode()).decode()
            app_lock["password"] = encrypted
            self.config_manager.save_config()

            if hasattr(self, 'app') and hasattr(self.app, 'log'):
                self.app.log("Master password was set for credentials.")
            logging.info("Master password set.")

            messagebox.showinfo("Set", "Master password saved. You'll need it to reveal credentials.")
        return True

    def master_pw_is_verified(self):
        current_pw = self.app.config_manager.config.get("app_lock", {}).get("password", "")

        if not current_pw:
            # Prompt user to set a new master password
            def on_save():
                pw1 = new_pw.get()
                pw2 = confirm_pw.get()
                if not pw1 or not pw2:
                    messagebox.showwarning("Missing", "Please fill out both fields.")
                    return
                if pw1 != pw2:
                    messagebox.showerror("Mismatch", "Passwords do not match.")
                    return
                encrypted = fernet.encrypt(pw1.encode()).decode()
                self.app.config_manager.config["app_lock"] = {"password": encrypted}
                self.app.config_manager.save_config()
                dialog.destroy()
                messagebox.showinfo("Set", "Master password saved.")
                logging.info("Master password set via credentials manager.")
                self.app.log("Master password set.")
        
            dialog = tk.Toplevel(self)
            dialog.title("Set Master Password")
            dialog.geometry("300x160")
            dialog.transient(self)
            dialog.grab_set()

            ttk.Label(dialog, text="Set Master Password").pack(pady=(10, 5))
            new_pw = ttk.Entry(dialog, show="*")
            new_pw.pack(pady=5)
            confirm_pw = ttk.Entry(dialog, show="*")
            confirm_pw.pack(pady=5)
            ttk.Button(dialog, text="Save", command=on_save).pack(pady=10)

            return False

        else:
            # Prompt to enter the existing master password
            result = simpledialog.askstring("Master Password", "Enter master password:", show="*", parent=self)
            if result:
                try:
                    expected = self.fernet.decrypt(current_pw.encode()).decode()
                    return result == expected
                except Exception as e:
                    logging.warning(f"Master password verification failed: {e}")
                    return False
            return False


    def delete_credential_row(self, frame, label_text):
        confirm = messagebox.askyesno("Confirm Delete", f"Delete credential for {label_text}?")
        if confirm:
            frame.destroy()
            self.credential_rows = [row for row in self.credential_rows if row[2] != label_text]
            logging.info(f"Credential deleted for {label_text}.")
            if hasattr(self, 'app') and hasattr(self.app, 'log'):
                self.app.log(f"Credential deleted for {label_text}.")

    def move_credential_row(self, frame, direction):
        idx = next((i for i, row in enumerate(self.credential_rows) if row[4] == frame), None)
        if idx is None:
            return
        if direction == "up" and idx > 0:
            self.credential_rows[idx], self.credential_rows[idx - 1] = self.credential_rows[idx - 1], self.credential_rows[idx]
        elif direction == "down" and idx < len(self.credential_rows) - 1:
            self.credential_rows[idx], self.credential_rows[idx + 1] = self.credential_rows[idx + 1], self.credential_rows[idx]
        else:
            return
        for widget in frame.master.winfo_children():
            widget.pack_forget()
        for row in self.credential_rows:
            row[4].pack(fill=tk.X, padx=5, pady=5)
        if hasattr(self, 'app') and hasattr(self.app, 'log'):
            self.app.log(f"Credential moved {direction}.")
        logging.info(f"Credential moved {direction}.")

    def add_new_credential_row(self, parent):
        label = self.get_next_unique_label()
        row = self.build_lan_credential_row(parent, label, "", "", default_path="", enabled=True, default=False)
        self.credential_rows.append(row)

    def get_next_unique_label(self):
        existing = {row["label"] for row in self.credential_rows if "label" in row}
        for i in range(1, 100):
            label = f"User {i}"
            if label not in existing:
                return label
        return f"User {len(existing)+1}"


class CredentialManagerDialog(tk.Toplevel):
    def __init__(self, master, config_manager):
        super().__init__(master)
        self.app = master
        self.config_manager = config_manager
        self.title("Credential Manager")
        self.geometry("700x500")

        self.tabs = ttk.Notebook(self)
        self.tabs.pack(fill=tk.BOTH, expand=True)

        self.lan_tab = ttk.Frame(self.tabs)
        self.sftp_tab = ttk.Frame(self.tabs)
        self.email_tab = ttk.Frame(self.tabs)

        self.tabs.add(self.lan_tab, text="LAN")
        self.tabs.add(self.sftp_tab, text="SFTP")
        self.tabs.add(self.email_tab, text="Email")

     #  self.lan_rows = []
        self.credential_rows = []
        self.build_lan_tab()
        self.sftp_rows = []
        self.build_sftp_tab()

    def add_lan_credential_row(self, parent):
        label = f"User {len(self.credential_rows) + 1}"
        row = self.build_generic_credential_row(
            parent,
            kind="lan",
            label_text=label,
            fields_dict={"username": "", "path": ""},
            encrypted_pw="",
            enabled=True,
            default=False,
        )
        self.credential_rows.append(row)

    def build_lan_tab(self):
        parent = self.lan_tab
        self.credential_rows = []
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(frame, text="LAN Credentials", font=("Segoe UI", 10, "bold")).grid(row=0, column=0, sticky="w")
        self.lan_cred_section = ttk.Frame(frame)
        self.lan_cred_section.grid(row=1, column=0, columnspan=2, sticky="ew")

        creds = self.config_manager.config.get("lan_credentials", [])
        if creds:
            default_set = False  # <-- This was previously missing, causing crash
            for i, cred in enumerate(creds):
                row = self.build_generic_credential_row(
                    self.lan_cred_section,
                    kind="lan",
                    label_text=cred.get("label", f"User {i+1}"),
                    fields_dict={
                        "username": cred.get("username", ""),
                        "path": cred.get("path", "")
                    },
                    encrypted_pw=cred.get("password", ""),
                    enabled=cred.get("enabled", True),
                    default=cred.get("default", False)
                )
                self.credential_rows.append(row)

                if row.get("default_var") and row["default_var"].get():
                    if not default_set:
                        default_set = True
                    else:
                        row["default_var"].set(False)  # Unset extra defaults
        else:
            for i in range(2):
                label = f"User {i+1}"
                row = self.build_generic_credential_row(
                    self.lan_cred_section,
                    kind="lan",
                    label_text=label,
                    fields_dict={"username": "", "path": ""},
                    encrypted_pw="",
                    enabled=True,
                    default=False
                )
                self.credential_rows.append(row)

        add_btn = ttk.Button(frame, text="Add LAN Credential", command=self.add_lan_credential_row)
        add_btn.grid(row=2, column=0, pady=10, sticky="w")

        save_btn = ttk.Button(frame, text="Save LAN Credentials", command=self.save_lan_credentials)
        save_btn.grid(row=2, column=1, pady=10, sticky="e")

                # Buttons

    def build_generic_credential_row(self, parent, kind, label_text, fields_dict, encrypted_pw=None, default=False, enabled=True):
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=2, padx=5)

        ttk.Label(frame, text=label_text).grid(row=0, column=0, padx=3)

        user_entry = ttk.Entry(frame, width=15)
        user_entry.insert(0, fields_dict.get("username", ""))
        user_entry.grid(row=0, column=1)

        pw_entry = ttk.Entry(frame, show="*", width=15)
        pw_entry.insert(0, "********" if encrypted_pw else "")
        pw_entry.grid(row=0, column=2)

        ttk.Label(frame, text="Path:" if kind == "lan" else "Host:").grid(row=0, column=3)
        path_entry = ttk.Entry(frame, width=25)
        path_entry.insert(0, fields_dict.get("path", ""))
        path_entry.grid(row=0, column=4)

        default_var = tk.BooleanVar(value=default)
        default_cb = ttk.Checkbutton(
            frame,
            text="Default",
            variable=default_var,
            command=lambda: set_default_lan_credential(
                self.credential_rows,
                default_var,
                config_manager=self.config_manager,
                log_fn=getattr(self, 'log', None),
                refresh_fn=getattr(self, 'refresh_remote_tree', None)
            )
        )
        default_cb.grid(row=0, column=5, padx=5)

        enabled_var = tk.BooleanVar(value=enabled)
        enabled_cb = ttk.Checkbutton(frame, variable=enabled_var, text="Enabled")
        enabled_cb.grid(row=0, column=6)

        return {
            "frame": frame,
            "user_entry": user_entry,
            "pw_entry": pw_entry,
            "path_entry": path_entry,
            "default_var": default_var,
            "enabled_var": enabled_var,
            "kind": kind,
            "label": label_text,
            "encrypted_pw": encrypted_pw
        }

    def add_lan_credential(self):
        label = f"User {len(self.lan_rows) + 1}"
        row = self.build_generic_credential_row(
            parent=self.lan_cred_section,
            kind="lan",
            label_text=label,
            fields_dict={"username": "", "path": ""},
            encrypted_pw="",
            default=False,
            enabled=True
        )
        self.lan_rows.append(row)

    def save_lan_credentials(self):
        new_creds = []
        fernet = get_fernet()
        default_set = False
        default_values = None

        for row in self.credential_rows:
            user = row["user_entry"].get().strip()
            pw = row["pw_entry"].get().strip()
            label = row["label"]
            enabled = row["enabled_var"].get()
            path = row["path_entry"].get().strip()
            default = row["default_var"].get()
            encrypted = row.get("encrypted_pw", "")

            if pw != "********":
                encrypted = fernet.encrypt(pw.encode()).decode()

            if not user and not pw and not path:
                continue

            if default and not default_set:
                default_set = True
                default_values = {"username": user, "password": encrypted, "path": path}
            else:
                default = False

            new_creds.append({
                "label": label,
                "username": user,
                "password": encrypted,
                "enabled": enabled,
                "path": path,
                "default": default
            })

        self.config_manager.config["lan_credentials"] = new_creds
        self.config_manager.save_config()

        if default_values:
            backpropagate_to_credentials(self.config_manager, default_values, kind="lan")

        messagebox.showinfo("Saved", "LAN credentials saved.")

    def add_new_credential_row(self, parent):
        MAX_CREDENTIALS = 10
        allow_bypass = self.config_manager.config.get("disable_cred_limit", False)
        if not allow_bypass and len(self.credential_rows) >= MAX_CREDENTIALS:
            messagebox.showwarning(
                "Limit Reached",
                "You’ve reached the maximum number of LAN credentials (10).\n\nThis limit can be raised from the Safety Menu in future builds."
            )
            return

        label_text = self.get_next_unique_label()
        row_dict = self.build_lan_credential_row(parent, label_text, "", "", enabled=True, default_path="")
        self.credential_rows.append(row_dict)  # ✅ Append row to internal list

        if hasattr(self, 'app') and hasattr(self.app, 'log'):
            self.app.log("New LAN credential row added.")
        logging.info("New LAN credential row added.")

    def get_next_unique_label(self):
        existing = {row["label"] for row in self.credential_rows if "label" in row}
        for i in range(1, 100):  # reasonable ceiling
            label = f"User {i}"
            if label not in existing:
                return label
        return f"User {len(existing)+1}"

    def build_sftp_tab(self):
        parent = self.sftp_tab
        label = ttk.Label(self.sftp_tab, text="SFTP Credentials", font=("Segoe UI", 10, "bold"))
        label.pack(anchor="w", padx=10, pady=(10, 2))
        
        


        self.sftp_cred_section = ttk.Frame(self.sftp_tab)
     #  self.sftp_tab = ttk.Frame(self.notebook)
        self.sftp_rows = []  # Initialize list to hold SFTP rows
        self.sftp_cred_section.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        existing_sftp = self.config_manager.config.get("sftp_credentials", [])
        default_set = False
        for i, sftp_cred in enumerate(existing_sftp):
            decrypted_pw = ""
            try:
                decrypted_pw = fernet.decrypt(sftp_cred.get("password", "").encode()).decode()
            except Exception:
                pass

            default_checked = sftp_cred.get("default", False)
            if default_checked and not default_set:
                default_set = True
            else:
                default_checked = False

            row = self.build_sftp_credential_row(
                self.sftp_cred_section,
                label_text=f"SFTP {i+1}",
                username=sftp_cred.get("username", ""),
                password=decrypted_pw,
                host=sftp_cred.get("host", ""),
                port=sftp_cred.get("port", "22"),
                remote_path=sftp_cred.get("remote_path", ""),
                enabled=sftp_cred.get("enabled", True),
                default=default_checked,
            )
            self.sftp_rows.append(row)

        if not existing_sftp and self.config_manager.config.get("sftp"):
            sftp = self.config_manager.config.get("sftp")
            decrypted_pw = ""
            try:
                decrypted_pw = fernet.decrypt(sftp.get("password", "").encode()).decode()
            except Exception:
                pass

            row = self.build_sftp_credential_row(
                self.sftp_cred_section,
                label_text="SFTP 1",
                username=sftp.get("username", ""),
                password=decrypted_pw,
                host=sftp.get("host", ""),
                port=sftp.get("port", "22"),
                remote_path=sftp.get("remote_path", ""),
                enabled=True,
                default=True,
            )
            self.sftp_rows.append(row)

        add_btn = ttk.Button(
            self.sftp_cred_section,
            text="Add SFTP Credential",
            command=lambda: self.add_sftp_credential_row(self.sftp_cred_section)
        )
        add_btn.pack(anchor="w", pady=(10, 0))

        save_btn = ttk.Button(
            self.sftp_cred_section,
            text="Save SFTP Credentials",
            command=self.save_sftp_credentials
        )
        save_btn.pack(anchor="e", pady=(10, 0))

        # ttk.Button(frame, text="Add SFTP Credential", command=lambda: self.add_sftp_credential_row(self.sftp_cred_section)).grid(row=99, column=0, sticky="w", pady=(10, 5))
        # ttk.Button(frame, text="Save SFTP Credentials", command=self.save_sftp_credentials).grid(row=99, column=1, sticky="e", pady=(10, 5))



    def build_sftp_credential_row(self, parent, label_text, username, password, host, port, remote_path, enabled=True, default=False):
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=2, padx=5)

        ttk.Label(frame, text=label_text).grid(row=0, column=0, padx=3)

        user_entry = ttk.Entry(frame, width=15)
        user_entry.insert(0, username)
        user_entry.grid(row=0, column=1)

        pw_entry = ttk.Entry(frame, show="*", width=15)
        pw_entry.insert(0, "********" if password else "")
        pw_entry.grid(row=0, column=2)

        host_entry = ttk.Entry(frame, width=20)
        host_entry.insert(0, host)
        host_entry.grid(row=0, column=3)

        port_entry = ttk.Entry(frame, width=5)
        port_entry.insert(0, str(port) if port else "22")
        port_entry.grid(row=0, column=4)

        remote_entry = ttk.Entry(frame, width=20)
        remote_entry.insert(0, remote_path)
        remote_entry.grid(row=0, column=5)

        default_var = tk.BooleanVar(value=default)
        default_cb = ttk.Checkbutton(
            frame, text="Default", variable=default_var,
            command=lambda: set_default_credential_row(
                self.sftp_rows, default_var,
                config_manager=self.config_manager,
                log_fn=getattr(self, 'log', None),
                refresh_fn=getattr(self, 'refresh_remote_tree', None)
            )
        )
        default_cb.grid(row=0, column=6, padx=5)

        enabled_var = tk.BooleanVar(value=enabled)
        ttk.Checkbutton(frame, text="Enabled", variable=enabled_var).grid(row=0, column=7)

        return {
            "frame": frame,
            "user_entry": user_entry,
            "pw_entry": pw_entry,
            "host_entry": host_entry,
            "port_entry": port_entry,
            "remote_entry": remote_entry,
            "enabled_var": enabled_var,
            "default_var": default_var,
            "label": label_text,
            "encrypted_pw": password
        }


    def add_sftp_credential_row(self, parent):
        MAX_CREDENTIALS = 10
        allow_bypass = self.config_manager.config.get("disable_cred_limit", False)
        if not allow_bypass and len(self.sftp_rows) >= MAX_CREDENTIALS:
            messagebox.showwarning("Limit Reached", "You have reached the maximum number of SFTP credentials (10).")
            return

        label = f"SFTP {len(self.sftp_rows) + 1}"
        row = self.build_sftp_credential_row(parent, label, host="", port="22", username="", password="", remote_path="", default=False, enabled=True)
        self.sftp_rows.append(row)

    def save_sftp_credentials(self):
        new_creds = []
        fernet = get_fernet()

        for row in self.sftp_rows:
            username = row['user_entry'].get().strip()
            password = row['pw_entry'].get().strip()
            host = row['host_entry'].get().strip()
            port = row['port_entry'].get().strip()
            remote = row['remote_entry'].get().strip()
            enabled = row['enabled_var'].get()
            default = row['default_var'].get()

            if not username or not host or not port:
                continue  # skip incomplete

            if password != "********":
                encrypted_pw = fernet.encrypt(password.encode()).decode()
            else:
                encrypted_pw = row.get("encrypted_pw", "")

            new_creds.append({
                "label": username,
                "username": username,
                "password": encrypted_pw,
                "host": host,
                "port": port,
                "remote_path": remote,
                "enabled": enabled,
                "default": default
            })

        # Only one default
        default_seen = False
        for cred in new_creds:
            if cred["default"]:
                if not default_seen:
                    default_seen = True
                else:
                    cred["default"] = False

        self.config_manager.config["sftp_credentials"] = new_creds
        self.config_manager.save_config()
        if hasattr(self, 'log'):
            self.log("[SFTP] Credentials saved.")
        messagebox.showinfo("Saved", "SFTP credentials saved.")

    def load_sftp_credentials(self):
        self.sftp_rows.clear()
        for widget in self.sftp_cred_section.winfo_children():
            widget.destroy()

        fernet = get_fernet()
        creds = self.config_manager.config.get("sftp_credentials", [])
        for i, cred in enumerate(creds):
            decrypted_pw = "********"
            try:
                decrypted_pw = fernet.decrypt(cred.get("password", "").encode()).decode()
            except Exception:
                pass

            row = self.build_sftp_credential_row(
                self.sftp_cred_section,
                label_text=f"SFTP {i+1}",
                username=cred.get("username", ""),
                password=decrypted_pw,
                host=cred.get("host", ""),
                port=cred.get("port", "22"),
                remote_path=cred.get("remote_path", ""),
                enabled=cred.get("enabled", True),
                default=cred.get("default", False),
            )
            self.sftp_rows.append(row)

    def sync_default_sftp_to_config(config_manager, sftp_rows):
        """
        Finds the default SFTP row and saves its values into config['sftp'].
        """
        for row in sftp_rows:
            if row.get("default_var") and row["default_var"].get():
                try:
                    host = row["host_entry"].get().strip()
                    port_str = row["port_entry"].get().strip()
                    port = int(port_str) if port_str.isdigit() else 22
                    user = row["user_entry"].get().strip()
                    pw = row["pw_entry"].get().strip()
                    remote = row["remote_entry"].get().strip()

                    config_manager.config["sftp"] = {
                        "host": host,
                        "port": port,
                        "username": user,
                        "password": pw,
                        "remote_path": remote
                    }
                    config_manager.save_config()
                    return True

                except Exception as e:
                    logging.warning(f"[SFTP] Failed to sync default to config: {e}")
                    return False
        return False




class EmailSettingsDialog(tk.Toplevel):
    def __init__(self, master, is_night_mode=False):
        super().__init__(master)
        self.app = master  # Reference to main app for logging
        self.title("Email Settings")
        self.geometry("500x500")
        self.entries = {}
        self.to_entries = []
        self.is_night_mode = is_night_mode

        try:
            if os.path.exists(EMAIL_CONFIG_FILE):
                self.email_config = load_email_config()
            else:
                self.email_config = {}
        except Exception as e:
            safe_error(f"Failed to read email config: {e}")
            self.email_config = {}

        self.event_vars = {
            "upload_failed": tk.BooleanVar(value=self.email_config.get("upload_failed", True)),
            "upload_success": tk.BooleanVar(value=self.email_config.get("upload_success", False)),
            "scheduler_paused": tk.BooleanVar(value=self.email_config.get("scheduler_paused", True)),
            "upload_skipped": tk.BooleanVar(value=self.email_config.get("upload_skipped", True))
        }

        self.create_widgets()
        self.toggle_night_mode()
        self.load_settings()

        if self.is_night_mode:
            self.toggle_night_mode()

    def create_widgets(self):
        frame = ttk.Frame(self)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.settings_frame = frame

        labels = ["SMTP Server", "SMTP Port", "Username", "Password", "From"]
        self.entries = {}
        for i, label in enumerate(labels):
            ttk.Label(frame, text=label).grid(row=i, column=0, sticky="e")
            entry = ttk.Entry(frame, show="*" if label == "Password" else None)
            entry.grid(row=i, column=1, sticky="ew")
            self.entries[label.lower().replace(" ", "_")] = entry

        ttk.Label(frame, text="To").grid(row=5, column=0, sticky="ne")
        to_frame = ttk.Frame(frame)
        to_frame.grid(row=5, column=1, sticky="ew")
        self.to_entries = []
        for i in range(5):
            entry = ttk.Entry(to_frame)
            entry.pack(fill=tk.X, pady=1)
            self.to_entries.append(entry)

        ttk.Label(frame, text="Email Events").grid(row=6, column=0, sticky="nw")
        event_frame = ttk.Frame(frame)
        event_frame.grid(row=6, column=1, sticky="ew")
        self.event_vars = {
            "upload_failed": tk.BooleanVar(value=True),
            "upload_success": tk.BooleanVar(value=False),
            "upload_skipped": tk.BooleanVar(value=True),
            "scheduler_paused": tk.BooleanVar(value=True)
        }
        ttk.Checkbutton(event_frame, text="On Upload Failed", variable=self.event_vars["upload_failed"]).pack(anchor="w")
        ttk.Checkbutton(event_frame, text="On Upload Success", variable=self.event_vars["upload_success"]).pack(anchor="w")
        ttk.Checkbutton(event_frame, text="On Upload Skipped", variable=self.event_vars["upload_skipped"]).pack(anchor="w")
        ttk.Checkbutton(event_frame, text="If Scheduler Paused Too Long", variable=self.event_vars["scheduler_paused"]).pack(anchor="w")

        ttk.Label(frame, text="Pause Alert Threshold (secs)").grid(row=7, column=0, sticky="e")
        self.entries['scheduler_pause_threshold'] = tk.StringVar()
        ttk.Entry(frame, textvariable=self.entries['scheduler_pause_threshold']).grid(row=7, column=1, sticky="ew")

        frame.columnconfigure(1, weight=1)
        # Bottom button bar
        button_frame = ttk.Frame(self)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X)

        ttk.Button(button_frame, text="Cancel", command=self.destroy).pack(side=tk.RIGHT, padx=10)
        ttk.Button(button_frame, text="Save", command=self.save_email_settings).pack(side=tk.RIGHT, padx=10)

    def toggle_night_mode(self):
        if not self.is_night_mode:
            return  # Do nothing in light mode

        bg = "#2a2a2e"
        fg = "#000000"
        entry_bg = "#343539"

        self.configure(background=bg)
        for child in self.winfo_children():
            try:
                if isinstance(child, (tk.Label, ttk.Label)):
                    child.configure(background=bg, foreground=fg)
                elif isinstance(child, (tk.Entry, ttk.Entry)):
                    child.configure(background=entry_bg, foreground=fg, insertbackground=fg)
                elif isinstance(child, (tk.Button, ttk.Button)):
                    child.configure(background=bg, foreground=fg, activebackground="#343541", activeforeground=fg)
                elif isinstance(child, (tk.Checkbutton, ttk.Checkbutton)):
                    child.configure(background=bg, foreground=fg, selectcolor=bg)
            except Exception:
                pass

    def save_settings(self):
        config = {k: v.get() if isinstance(v, tk.StringVar) else v.get() for k, v in self.entries.items()}
        config['scheduler_pause_threshold'] = int(self.entries.get("scheduler_pause_threshold", tk.StringVar()).get())
        config['upload_failed'] = self.event_vars['upload_failed'].get()
        config['upload_success'] = self.event_vars['upload_success'].get()
        config['upload_skipped'] = self.event_vars['upload_skipped'].get()
        config['scheduler_paused'] = self.event_vars['scheduler_paused'].get()
        to_emails = [e.get().strip() for e in self.to_entries if e.get().strip() != ""]
        config['to'] = to_emails
        try:
            with open(EMAIL_CONFIG_FILE, 'w') as f:
                json.dump(config, f, indent=4)
            messagebox.showinfo("Saved", "Email settings saved successfully.")
            self.destroy()
        except Exception as e:
            messagebox.showerror("Error", str(e))

    def save_email_settings(self):
        config = {k: v.get() if isinstance(v, tk.StringVar) else v.get() for k, v in self.entries.items()}
        config['scheduler_pause_threshold'] = int(self.entries.get("scheduler_pause_threshold", tk.StringVar()).get())
        config['upload_failed'] = self.event_vars['upload_failed'].get()
        config['upload_success'] = self.event_vars['upload_success'].get()
        config['upload_skipped'] = self.event_vars['upload_skipped'].get()
        config['scheduler_paused'] = self.event_vars['scheduler_paused'].get()
        to_emails = [e.get().strip() for e in self.to_entries if e.get().strip()]
        config['to'] = to_emails

        try:
            with open(EMAIL_CONFIG_FILE, 'w') as f:
                json.dump(config, f, indent=4)
            messagebox.showinfo("Saved", "Email settings saved successfully.")
            self.app.log("Email settings saved.")
            safe_info("Email settings saved successfully.")
            self.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save email settings: {e}")
            safe_error(f"Failed to save email settings: {e}")



    def load_settings(self):
        if os.path.exists(EMAIL_CONFIG_FILE):
            try:
                with open(EMAIL_CONFIG_FILE, 'r') as f:
                    config = json.load(f)

                for key, entry in self.entries.items():
                    if isinstance(entry, tk.StringVar):
                        entry.set(config.get(key, ""))
                    else:
                        entry.delete(0, tk.END)
                        entry.insert(0, config.get(key, ""))

                if isinstance(config.get('to'), list):
                    for i, email in enumerate(config['to'][:5]):
                        self.to_entries[i].delete(0, tk.END)
                        self.to_entries[i].insert(0, email)

                for key in self.event_vars:
                    self.event_vars[key].set(config.get(key, self.event_vars[key].get()))

                threshold = config.get('scheduler_pause_threshold', 600)
                self.entries['scheduler_pause_threshold'].set(str(threshold))

            except Exception as e:
                safe_error(f"Failed to load email settings: {e}")


class SFTPSettingsDialog(tk.Toplevel):
    def __init__(self, master, config_manager, is_night_mode=False):
        super().__init__(master)
        self.title("SFTP Settings")
        self.geometry("500x400")
        self.app = master
        self.config_manager = config_manager
        self.entries = {}
        self.is_night_mode = is_night_mode

        self.create_widgets()
        self.load_settings()

        if self.is_night_mode:
            self.toggle_night_mode()

    def create_widgets(self):
        fields = ["Host", "Port", "Username", "Password", "Remote Path"]
        defaults = ["example.com", "22", "user", "", "/"]
        for i, (label, default) in enumerate(zip(fields, defaults)):
            ttk.Label(self, text=label).grid(row=i, column=0, sticky="e", padx=5, pady=5)
            entry = ttk.Entry(self, show="*" if "Password" in label else None)
            entry.grid(row=i, column=1, sticky="ew", padx=5, pady=5)
            self.entries[label.lower().replace(" ", "_")] = entry
            entry.insert(0, default)
        self.columnconfigure(1, weight=1)

        ttk.Button(self, text="Save", command=self.save_sftp_settings).grid(row=len(fields), column=1, sticky="e", pady=10)

    def load_settings(self):
        # Prefer default SFTP credential if available
        default_cred = None
        for cred in self.config_manager.config.get("sftp_credentials", []):
            if cred.get("default"):
                default_cred = cred
                break

        # Fallback to top-level 'sftp' key if needed
        if not default_cred:
            default_cred = self.config_manager.config.get("sftp", {})

        for key, entry in self.entries.items():
            val = default_cred.get(key, "")
            entry.delete(0, tk.END)
            entry.insert(0, str(val))


    def save_sftp_settings(self):
        try:
            sftp_info = {
                key: self.entries[key].get().strip() if key != "port" else int(self.entries[key].get().strip())
                for key in self.entries
            }
            self.config_manager.config["sftp"] = sftp_info
            self.config_manager.save_config()
            messagebox.showinfo("Saved", "SFTP settings saved successfully.")
            if hasattr(self, 'app') and hasattr(self.app, 'log'):
                self.app.log("SFTP settings saved.")
            logging.info("SFTP settings saved successfully.")
            backpropagate_to_credentials(self.config_manager, sftp_info, kind="sftp")
            self.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save SFTP settings: {e}")
            logging.error(f"Failed to save SFTP settings: {e}")

    # def backpropagate_to_credentials(config_manager, values, kind="sftp"):
    #     """
    #     Ensures that any saved changes in the SFTP or LAN settings dialog also
    #     update the corresponding default credential row stored in config.
    #     """
    #     key = f"{kind}_credentials"
    #     if key not in config_manager.config:
    #         return

    #     default_found = False
    #     for row in config_manager.config[key]:
    #         if row.get("default"):
    #             default_found = True
    #             for k in ("username", "password", "host", "port", "remote_path", "path"):
    #                 if k in values and k in row:
    #                     row[k] = values[k]
    #             break

    #     if default_found:
    #         config_manager.save_config()



    def toggle_night_mode(self):
        bg = "#2a2a2e"
        fg = "#ffffff"
        entry_bg = "#343539"

        self.configure(background=bg)
        for child in self.winfo_children():
            try:
                if isinstance(child, (tk.Label, ttk.Label)):
                    child.configure(background=bg, foreground=fg)
                elif isinstance(child, (tk.Entry, ttk.Entry)):
                    child.configure(background=entry_bg, foreground=fg, insertbackground=fg)
                elif isinstance(child, (tk.Button, ttk.Button)):
                    child.configure(background=bg, foreground=fg)
            except Exception:
                pass



class ConfigManager:
    def __init__(self):
        self.config = {
            "sftp": {},
            "schedules": [],
            "clipboard": None,
            "enable_dir_copy": True
        }
        self.schedule_file = SCHEDULE_CONFIG_FILE
        self.schedules = []
        self.load_config_file(CONFIG_FILE)
        self.load_schedules()

    def sftp_upload(self, schedule):
        """
        Performs the SFTP upload for the given schedule.
        Returns True on success, False on failure.
        """
        local_path = schedule.get("filepath")
        remote_path = schedule.get("remote_path", "/upload/serversettings.ini")

        if not local_path or not os.path.exists(local_path):
            safe_log(f"Local file not found: {local_path}")
            return False

        try:
            sftp = self.get_sftp()
            if self.honor_sftp_limits.get():
                sftp.upload_throttled(local_path, remote_path)
            else:
                sftp.upload(local_path, remote_path)
            safe_log(f"Uploaded {local_path} to {remote_path}")
            return True
        except Exception as e:
            safe_log(f"Upload failed: {e}")
            return False

    def load_config_file(self, path):
        try:
            with open(path, 'r') as f:
                config_data = json.load(f)

            if 'sftp' in config_data and 'password' in config_data['sftp']:
                encrypted_pw = config_data['sftp']['password']
                try:
                    config_data['sftp']['password'] = fernet.decrypt(encrypted_pw.encode()).decode()
                except Exception as e:
                    safe_error(f"Password decryption failed: {e}")
                    config_data['sftp']['password'] = ""

            self.config.update(config_data)
            safe_log(f"Loaded configuration from {path}")
        except Exception as e:
            safe_error(f"Failed to load configuration from {path}: {e}")

    def load_schedules(self):
        if os.path.exists(self.schedule_file):
            try:
                with open(self.schedule_file, 'r') as f:
                    self.config['schedules'] = json.load(f)
                safe_log("Schedules loaded from file.")
            except Exception as e:
                safe_error(f"Failed to load schedules: {e}")

    def save_schedules(self):
        try:
            with open(self.schedule_file, 'w') as f:
                json.dump(self.config['schedules'], f, indent=4)
            safe_log("Schedules saved.")
        except Exception as e:
            safe_error(f"Failed to save schedules: {e}")

    def save_config(self):
        cfg = self.config.copy()
        plain_password = cfg['sftp'].get('password', '')

        if plain_password:
            try:
                cfg['sftp']['password'] = fernet.encrypt(plain_password.encode('utf-8')).decode('utf-8')
            except Exception as e:
                safe_error(f"Password encryption failed: {e}")
                cfg['sftp']['password'] = ""

        with open(CONFIG_FILE, 'w') as f:
            json.dump(cfg, f, indent=4)

        self.config['sftp']['password'] = plain_password

    def load_config_and_refresh(self):
        try:
            file_path = filedialog.askopenfilename(
                title="Load Config File",
                filetypes=[("JSON Files", "*.json")]
            )
            if not file_path:
                return

            with open(file_path, 'r') as f:
                loaded = json.load(f)

            if 'password' in loaded.get('sftp', {}):
                try:
                    loaded['sftp']['password'] = fernet.decrypt(loaded['sftp']['password'].encode()).decode('utf-8')
                except Exception as e:
                    safe_error(f"Decryption failed: {e}")
                    loaded['sftp']['password'] = ""

            self.config = loaded
            safe_log(f"Configuration loaded from {file_path}")
        except Exception as e:
            safe_error(f"Failed to load configuration: {e}")


class Scheduler:
    def __init__(self, app):
        self.app = app
        self.running = True
        self.last_run = datetime.now()
        self.pause_time = None
        self.pause_alert_sent = False
        self.last_schedule_run_times = {}

        threading.Thread(target=self.monitor_scheduler_pause, daemon=True).start()
        threading.Thread(target=self.run, daemon=True).start()
        safe_log("Scheduler thread started.")

    def pause(self):
        self.running = False
        self.pause_time = datetime.now()

    def resume(self):
        if not self.running:
            self.running = True
            self.pause_time = None
            self.pause_alert_sent = False

    def run(self):
        while True:
            if self.running:
                self.scheduler_tick()
            time.sleep(60)

    def scheduler_tick(self):
        now = datetime.now()
        safe_debug(f"Scheduler tick at {now}, {len(self.app.config_manager.config['schedules'])} schedules loaded.")

        for sched in self.app.config_manager.config['schedules']:
            safe_debug(f"Evaluating: {sched.get('title', 'Unnamed')}")
            if self.should_run_schedule(sched, now):
                self.app.log(f"Running schedule: {sched.get('title', 'Unnamed')}")

                if not self.app.sftp_enabled.get():
                    self.record_schedule_result(sched, "failed")
                    self.app.schedule_calendar.build_calendar()

                    if sched.get("email_on_fail", False):
                        send_email("SFTP Failed", f"Failed to run schedule: {sched.get('title', 'Unnamed')}")

                else:
                    try:
                        success = self.app.config_manager.sftp_upload(sched)
                        status = "success" if success else "failed"
                        self.record_schedule_result(sched, status)
                        self.app.schedule_calendar.build_calendar()

                        if success and sched.get("email_on_success", False):
                            send_email("SFTP Success", f"Upload succeeded for: {sched.get('title', 'Unnamed')}")
                        elif not success and sched.get("email_on_fail", False):
                            send_email("SFTP Failed", f"Upload failed for: {sched.get('title', 'Unnamed')}")
                    except Exception as e:
                        self.app.log(f"Error during upload: {e}")

    def should_run_schedule(self, schedule, now):
        safe_debug(f"Checking if schedule should run: {schedule}")
        if not schedule.get("enabled", True):
            return False

        if schedule.get("month", "Any") != "Any" and schedule["month"] != now.strftime("%B"):
            return False

        if schedule.get("day", "Any") != "Any" and schedule["day"] != now.strftime("%A"):
            return False

        if schedule.get("day_of_month", "Any") != "Any" and int(schedule["day_of_month"]) != now.day:
            return False

        if schedule["hour"] != now.hour or schedule["minute"] != now.minute:
            return False

        return True

    def get_email_setting(self, key):
        try:
            with open(EMAIL_CONFIG_FILE, 'r') as f:
                config = json.load(f)
            return config.get(key, False)
        except OSError as e:
            print(f"Email config load error: {e}")
        except Exception:
            pass
        return False

    def monitor_scheduler_pause(self):
        safe_debug("Pause monitor thread started.")
        while True:
            try:
                try:
                    with open(EMAIL_CONFIG_FILE, 'r') as f:
                        config = json.load(f)
                except OSError as e:
                    print(f"Email config load error: {e}")
                    time.sleep(60)
                    continue
                except Exception as e:
                    logging.warning(f"Failed to load email config: {e}")
                    time.sleep(60)
                    continue

                pause_threshold = config.get("scheduler_pause_threshold", 600)
                send_alerts = config.get("scheduler_paused", True)
                now = datetime.now()

                if not self.running and self.pause_time:
                    delta = (now - self.pause_time).total_seconds()
                    safe_debug(f"[PauseCheck] Paused for {int(delta)}s (threshold {pause_threshold}s)")

                    if delta > pause_threshold:
                        if send_alerts and not self.pause_alert_sent:
                            send_email("Scheduler Paused", f"Scheduler has been paused for {int(delta)} seconds.")
                            safe_log(f"Scheduler pause alert sent (delta {int(delta)}s)")
                            self.pause_alert_sent = True
                    else:
                        self.pause_alert_sent = False
                else:
                    self.pause_alert_sent = False
            except Exception as e:
                logging.warning(f"Unexpected error in pause monitor: {e}")
            time.sleep(60)

    def record_schedule_result(self, schedule, result_status):
        """
        Appends a new result to the schedule's run_history and saves the updated config.
        result_status should be one of: 'success', 'failed', 'skipped'
        """
        timestamp = datetime.now().replace(second=0, microsecond=0).isoformat(timespec='minutes')
        if "run_history" not in schedule:
            schedule["run_history"] = {}

        schedule["run_history"][timestamp] = result_status
        self.app.config_manager.save_config()
        self.app.log(f"Recorded run at {timestamp}: {result_status}")



class ScheduleList(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent)
        self.app = app
        self.listbox = tk.Listbox(self)
        self.listbox.pack(fill=tk.BOTH, expand=True)
        self.listbox.bind("<Button-3>", self.show_context_menu)
        self.menu = tk.Menu(self, tearoff=0)
        self.menu.add_command(label="Edit", command=self.edit_schedule)
        self.refresh()

    def refresh(self):
        self.listbox.delete(0, tk.END)
        for sched in self.app.config_manager.config['schedules']:
            desc = f"{sched['day']} {sched.get('day_of_month', '')} {sched.get('month', '')} at {sched['hour']:02}:{sched['minute']:02} -> {os.path.basename(sched['filepath'])}"
            self.listbox.insert(tk.END, desc)

    def show_context_menu(self, event):
        try:
            self.listbox.selection_clear(0, tk.END)
            index = self.listbox.nearest(event.y)
            self.listbox.selection_set(index)
            self.menu.post(event.x_root, event.y_root)
        except:
            pass

    def edit_schedule(self):
        index = self.listbox.curselection()
        if index:
            index = index[0]
            sched = self.app.config_manager.config['schedules'][index]
            new_day = simpledialog.askstring("Edit Day", "Day:", initialvalue=sched['day'])
            new_dom = simpledialog.askstring("Edit Day of Month", "Day of Month:", initialvalue=sched.get('day_of_month', 'Any'))
            new_month = simpledialog.askstring("Edit Month", "Month:", initialvalue=sched.get('month', 'Any'))
            new_hour = simpledialog.askinteger("Edit Hour", "Hour:", initialvalue=sched['hour'])
            new_min = simpledialog.askinteger("Edit Minute", "Minute:", initialvalue=sched['minute'])
            sched.update({
                'day': new_day,
                'day_of_month': new_dom,
                'month': new_month,
                'hour': new_hour,
                'minute': new_min
            })
            self.refresh()
            self.app.config_manager.save_config()

    def delegate_edit(self):
        index = self.listbox.curselection()
        if index:
            self.app.edit_schedule(index[0])

class ScheduleCalendar(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent)
        self.app = app
        self.today = datetime.today()
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.day_buttons = {}
        self.pinned_day = None
        self.open_props = {}
        self.build_calendar()

    def build_calendar(self):
        for widget in self.winfo_children():
            widget.destroy()

        self.day_buttons.clear()
        today = datetime.today()
        is_current_month = (self.current_month == today.month and self.current_year == today.year)

        header = ttk.Frame(self)
        header.pack(fill=tk.X, pady=2)
        ttk.Button(header, text="<", width=2, command=self.prev_month).pack(side=tk.LEFT)
        ttk.Label(header, text=f"{calendar.month_name[self.current_month]} {self.current_year}").pack(side=tk.LEFT, expand=True)
        ttk.Button(header, text=">", width=2, command=self.next_month).pack(side=tk.RIGHT)

        days_frame = ttk.Frame(self)
        days_frame.pack()
        for day in ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]:
            ttk.Label(days_frame, text=day, width=6, anchor="center").pack(side=tk.LEFT, padx=1, pady=1)

        month_frame = ttk.Frame(self)
        month_frame.pack()
        status_by_day = self.get_status_by_day()

        for week in calendar.Calendar(firstweekday=0).monthdayscalendar(self.current_year, self.current_month):
            row = ttk.Frame(month_frame)
            row.pack()
            for day in week:
                if day == 0:
                    ttk.Label(row, text="", width=6).pack(side=tk.LEFT, padx=1, pady=1)
                else:
                    btn = ttk.Button(row, text=str(day), width=5)
                    btn.pack(side=tk.LEFT, padx=1, pady=1)

                    status = status_by_day.get(day)
                    bright = self.app.bright_colors_var.get()
                    style_name = f"{get_calendar_tag_color(status, bright_mode=bright)}.TButton"
                    btn.configure(style=style_name)

                    if is_current_month and day == today.day:
                        btn.configure(style="today.TButton")

                    self.day_buttons[day] = btn
                    btn.bind("<Enter>", lambda e, d=day: self.on_hover_day(d))
                    btn.bind("<Leave>", lambda e, d=day: self.on_leave_day(d))
                    btn.bind("<Button-1>", lambda e, d=day: self.select_day(d))

    def select_day(self, day):
        if self.pinned_day == day:
            # Deselect
            self.pinned_day = None
            self.clear_schedule_highlight()

            # Close property windows for this day
            to_close = []
            for idx, sched in enumerate(self.app.config_manager.config['schedules']):
                if self.does_schedule_match_day(sched, day):
                    to_close.append(idx)

            for idx in to_close:
                win = self.open_props.pop(idx, None)
                if win and win.winfo_exists():
                    win.destroy()
            return

        # Select
        self.pinned_day = day
        self.highlight_day(day)

        base_x = self.app.winfo_rootx() + 50
        base_y = self.app.winfo_rooty() + 50
        offset = 30

        matches = []
        for idx, sched in enumerate(self.app.config_manager.config['schedules']):
            if self.does_schedule_match_day(sched, day):
                matches.append(idx)

        for i, idx in enumerate(matches):
            if idx in self.open_props and self.open_props[idx].winfo_exists():
                continue  # Already open

            self.app.view_schedule_properties(idx)
            win = self.app.winfo_children()[-1]
            win.geometry(f"+{base_x + offset*i}+{base_y + offset*i}")
            self.open_props[idx] = win

            # Bring to front
            win.lift()
            win.attributes('-topmost', True)
            win.after(300, lambda w=win: w.attributes('-topmost', False))

            def make_on_close(w, index):
                def _on_close():
                    if index in self.open_props:
                        del self.open_props[index]
                    w.destroy()

                    # Re-lift others
                    for other in self.open_props.values():
                        if other.winfo_exists():
                            other.lift()
                            other.attributes('-topmost', True)
                            other.after(300, lambda win=other: win.attributes('-topmost', False))
                return _on_close

            win.protocol("WM_DELETE_WINDOW", make_on_close(win, idx))


    def does_schedule_match_day(self, sched, day):
        repeat = str(sched.get("repeat", "none")).lower()
        if repeat == "daily":
            return True
        elif repeat == "weekly":
            sched_dow = sched.get("day", "Any")
            return sched_dow == calendar.day_name[datetime(self.current_year, self.current_month, day).weekday()]
        elif repeat == "monthly":
            return str(day) == sched.get("day_of_month")
        elif repeat == "none":
            return (sched.get("day_of_month") == str(day) and
                    sched.get("month") == calendar.month_name[self.current_month])
        return False

    def get_matching_schedule_indices(self, day):
        matches = []
        for idx, sched in enumerate(self.app.config_manager.config['schedules']):
            if self.does_schedule_match_day(sched, day):
                matches.append(idx)
        return matches

    def get_status_by_day(self):
        status_map = {}
        now = datetime.now()
        status_priority = {"failed": 3, "skipped": 2, "success": 1}

        for sched in self.app.config_manager.config['schedules']:
            if not sched.get("enabled", True):
                continue  # Skip disabled schedules

            history = sched.get("run_history", {})

            # 1. Add confirmed results from history (success, failed, skipped)
            for key, result in history.items():
                try:
                    dt = datetime.strptime(key, "%Y-%m-%dT%H:%M")
                    if dt.year == self.current_year and dt.month == self.current_month:
                        existing = status_map.get(dt.day)
                        if not existing or status_priority.get(result, 0) > status_priority.get(existing, 0):
                            status_map[dt.day] = result
                except:
                    continue

        # 2. Predict upcoming scheduled runs (only if no history and enabled)
        for sched in self.app.config_manager.config['schedules']:
            if not sched.get("enabled", True):
                continue

            try:
                hour = int(sched.get("hour", 0))
                minute = int(sched.get("minute", 0))
                repeat = str(sched.get("repeat", "none")).lower()
                dom_raw = sched.get("day_of_month", "Any")
                dow_raw = sched.get("day", "Any")
                month_raw = sched.get("month", "Any")

                for day in range(1, calendar.monthrange(self.current_year, self.current_month)[1] + 1):
                    dt = datetime(self.current_year, self.current_month, day, hour, minute)
                    if dt <= now:
                        continue  # Only consider future runs for prediction

                    match = False
                    if repeat == "daily":
                        match = True
                    elif repeat == "weekly" and calendar.day_name[dt.weekday()] == dow_raw:
                        match = True
                    elif repeat == "monthly" and str(day) == dom_raw:
                        match = True
                    elif repeat == "none":
                        if month_raw != "Any" and calendar.month_name[self.current_month] != month_raw:
                            continue
                        if dom_raw != "Any" and str(day) != dom_raw:
                            continue
                        if dow_raw != "Any" and calendar.day_name[dt.weekday()] != dow_raw:
                            continue
                        match = True

                    if match and day not in status_map:
                        status_map[day] = "upcoming"

            except:
                continue

        return status_map

    def on_hover_day(self, day):
        if self.pinned_day is None:
            self.highlight_day(day)

    def on_leave_day(self, day):
        if self.pinned_day is None:
            self.clear_schedule_highlight()

    def highlight_day(self, day):
        matches = []
        for idx, sched in enumerate(self.app.config_manager.config['schedules']):
            if self.does_schedule_match_day(sched, day):
                matches.append(idx)

        self.app.sched_tree.selection_remove(*self.app.sched_tree.selection())

        for idx in matches:
            # Get sorted list and find position of this unsorted idx
            sorted_scheds = sorted(self.app.config_manager.config['schedules'], key=get_next_run)
            sched = self.app.config_manager.config['schedules'][idx]
            try:
                sorted_index = sorted_scheds.index(sched)
                self.app.sched_tree.selection_add(str(sorted_index))
            except ValueError:
                continue

    def clear_schedule_highlight(self):
        self.app.sched_tree.selection_remove(*self.app.sched_tree.selection())

    def prev_month(self):
        self.current_month -= 1
        if self.current_month < 1:
            self.current_month = 12
            self.current_year -= 1
        self.build_calendar()

    def next_month(self):
        self.current_month += 1
        if self.current_month > 12:
            self.current_month = 1
            self.current_year += 1
        self.build_calendar()



class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.show_lock_banner = True 
        self.is_night_mode = False  # Ensure safe access
        self.title("V1's Setting Doofer v0.7")
        self.geometry("1400x910")

        self.config_manager = ConfigManager()
        self.app_lock_settings = load_app_lock_config()
        self.honor_sftp_limits = tk.BooleanVar(value=self.config_manager.config.get("honor_sftp_limits", True))
        self.sftp_enabled = tk.BooleanVar(value=self.config_manager.config.get("sftp_enabled", True))
        self.enable_dir_copy_var = tk.BooleanVar(value=self.config_manager.config.get("enable_dir_copy", True))
        self.minimize_to_tray_var = tk.BooleanVar(value=True)
        self.open_dialogs = {
            "email": None,
            "sftp": None,
            "manual": None,
        }
        paused = self.config_manager.config.get("scheduler_paused", False)
        self.scheduler_running = tk.BooleanVar(value=not paused)
        self.status_var = tk.StringVar(value="Welcome.")
        self.scheduler = Scheduler(self)
        if paused:
            self.scheduler.pause()
        self.sftp_client = None
        self.local_path = os.getcwd()
        self.remote_path = None
        self.remote_base = None
        self.pause_time = None
        self.bright_colors_var = tk.BooleanVar(value=False)
        self.night_mode_var = tk.BooleanVar(value=self.config_manager.config.get("night_mode", False))
        self.create_menu_bar()
        self.create_widgets()
   #    self.bind_lock_shortcut()
        self.refresh_local_files()
        self.refresh_schedules()
        self.last_scheduler_check = time.time()
        self.sched_tree.bind("<Double-Button-1>", self.on_schedule_double_click)
        self.remote_tree.bind("<Button-3>", self.remote_right_click)

        self.after(100, self.setup_styles)
        self.after(500, lambda: deferred_sftp_setup(self))

    def setup_styles(self):
        self.is_night_mode = False  # default fallback
        style = ttk.Style()
        style.theme_use(style.theme_use())  # force theme setup

        # Check config and auto-apply Night Mode if set
        if self.config_manager.config.get("night_mode", False):
            self.night_mode_var.set(True)
            self.toggle_night_mode()


        style.configure("red.TButton", background="red")
        style.configure("green.TButton", background="green")
        style.configure("blue.TButton", background="blue")
        style.configure("lightpink.TButton", background="lightpink")
        style.configure("lightgreen.TButton", background="lightgreen")
        style.configure("orange.TButton", background="orange")
        style.configure("lightblue.TButton", background="lightblue")
        style.configure("default.TButton", background="SystemButtonFace")
        style.configure("today.TButton", background="#b0c4de")
        style.configure("sftpOn.TButton", background="lightgreen")
        style.configure("sftpOff.TButton", background="red")
        style.map("sftpOn.TButton", background=[("active", "lightgreen")])
        style.map("sftpOff.TButton", background=[("active", "red")])
        style.map("today.TButton", background=[("active", "#b0c4de"), ("!disabled", "#b0c4de")])
        style.configure("remote.TButton", background="magenta", foreground="black")
        style.map("remote.TButton", background=[("active", "hot pink")])

    def create_menu_bar(self):
        menubar = tk.Menu(self)
        self.config(menu=menubar)

        # === Settings Menu ===
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(
            label="Email Settings",
            command=lambda: self.open_single_dialog("email", EmailSettingsDialog, is_night_mode=self.is_night_mode)
        )
        settings_menu.add_command(
            label="SFTP Settings",
            command=lambda: self.open_single_dialog("sftp", SFTPSettingsDialog, self.config_manager, is_night_mode=self.is_night_mode)
        )
        settings_menu.add_command(
            label="LAN Settings",
            command=self.open_lan_settings_dialog
        )

        # === Safety Menu ===
        safety_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Safety", menu=safety_menu)

        self.safety_menu_log_var = tk.BooleanVar(value=notification_config.get("log_enabled", True))
        self.safety_menu_debug_var = tk.BooleanVar(value=notification_config.get("debug_enabled", False))


        safety_menu.add_checkbutton(
            label="Enable Logging",
            variable=self.safety_menu_log_var,
            command=self.toggle_logging_options
        )

        safety_menu.add_checkbutton(
            label="Enable Debug Logging",
            variable=self.safety_menu_debug_var,
            command=self.toggle_logging_options
        )
        safety_menu.add_separator()
        safety_menu.add_checkbutton(
            label="Enable Directory Copy",
            variable=self.enable_dir_copy_var,
            command=self.toggle_dir_copy
        )

        safety_menu.add_checkbutton(
            label="Honor SFTP Host Transfer Limits",
            variable=self.honor_sftp_limits,
            command=self.toggle_sftp_throttle
        )
        safety_menu.add_command(
            label="Lock App",
            command=self.lock_screen_from_shortcut
        )
        self.after(100, self.update_lock_banner)
        safety_menu.add_separator()
        safety_menu.add_command(label="Set Lock Password", command=lambda: SetPasswordDialog(self))
        safety_menu.add_separator()
        # Inside create_menu_bar(self):
        safety_menu.add_command(label="LAN Credential Manager", command=self.open_credential_manager_from_safety)

        # === View Menu ===
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)

        self.bright_colors_var = tk.BooleanVar(value=False)
        self.lock_image_var = tk.BooleanVar(value=False)

        view_menu.add_checkbutton(
            label="Bright Calendar Colors",
            variable=self.bright_colors_var,
            command=self.toggle_bright_colors
        )
        view_menu.add_checkbutton(
            label="Night Mode",
            variable=self.night_mode_var,
            command=self.toggle_night_mode
        )
        view_menu.add_command(
            label="Lock Screen Image",
            command=self.open_lock_image_widget  
        )
        view_menu.add_command(
            label="Manual",
            command=lambda: self.open_single_dialog("manual", ManualWindow)
        )
        view_menu.add_command(
            label="Credits",
            command=lambda: messagebox.showinfo("Credits", "Created by V1nceTD")
        )

        # === Modules Menu ===
        modules_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Modules", menu=modules_menu)
        modules_menu.add_command(
            label="Launch Python Module...",
            command=lambda: messagebox.showinfo("Modules", "Custom module launcher placeholder")
        )
        modules_menu.add_command(
            label="Run External EXE...",
            command=lambda: messagebox.showinfo("Modules", "EXE runner placeholder")
        )

    def open_lan_settings_dialog(self):
        try:
            dialog = LANSettingsDialog(self, self.config_manager, is_night_mode=self.is_night_mode)
            dialog.grab_set()
            logging.info("LAN Settings opened from Settings menu.")
        except Exception as e:
            logging.error(f"Failed to open LAN Settings: {e}")
            messagebox.showerror("Error", f"Could not open LAN Settings: {e}")

    def open_credential_manager_from_safety(self):
        try:
            dialog = CredentialManagerDialog(self, self.config_manager)
            dialog.grab_set()
            logging.info("Credential Manager opened from Safety menu.")
        except Exception as e:
            logging.error(f"Failed to open Credential Manager: {e}")
            messagebox.showerror("Error", f"Could not open Credential Manager: {e}")



    def toggle_bright_colors(self):
        """Toggle between bright and default calendar colors and refresh the calendar."""
        state = "ON" if self.bright_colors_var.get() else "OFF"
        self.log(f"Bright Calendar Colours toggled: {state}")
        self.refresh_calendar_events()

    def toggle_night_mode(self):
        is_night = self.night_mode_var.get()

        # Adjusted dark mode palette with brighter panel areas and treeview backgrounds
        bg = "#2a2a2e" if is_night else "#f0f0f0"
        fg = "#000000" if is_night else "#000000"
        header_bg = "#2a2a2e" if is_night else "#dcdcdc"
        tree_bg = "#343539" if is_night else bg
        selected_bg = "#343541" if is_night else "#cce6ff"
        selected_fg = fg
        disabled_fg = "#7d7d84" if is_night else "#a0a0a0"
        disabled_bg = "#2d2e30" if is_night else "#e0e0e0"
        entry_bg = "#343539" if is_night else "#ffffff"  # slightly brighter for readability
        panel_bg = "#2a2a2e" if is_night else bg  # sync panel color with tree_bg

        style = ttk.Style()
        style.theme_use(style.theme_use())  # force application

        # Normalize background artifacts
        style.configure("TMenubutton", background=bg, foreground=fg)
        style.configure("TCombobox", fieldbackground=bg, background=bg, foreground=fg)
        style.configure("TNotebook", background=bg)
        style.configure("TNotebook.Tab", background=bg, foreground=fg)
        style.configure("TScrollbar", background=bg)
        style.configure("TCheckbutton", background=bg, foreground=fg, indicatorcolor=bg)

        style.configure(".", background=bg, foreground=fg)
        style.configure("TLabel", background=bg, foreground=fg)
        style.configure("TFrame", background=panel_bg)
        style.configure("TButton", background=bg, foreground=fg)
        style.configure("TCheckbutton", background=bg, foreground=fg)
        style.configure("TEntry", fieldbackground=entry_bg, foreground=fg, background=entry_bg)

        # Button state colors
        style.map("TButton",
            foreground=[('disabled', disabled_fg)],
            background=[('disabled', disabled_bg)]
        )

        # Treeview styles (brightened background)
        style.configure("Treeview", background=tree_bg, fieldbackground=tree_bg, foreground=fg)
        style.configure("Treeview.Heading", background=header_bg, foreground=fg, relief="flat", borderwidth=0)
        style.map("Treeview", background=[("selected", selected_bg)], foreground=[("selected", selected_fg)])

        # Apply to main app window and text widgets
        self.configure(background=bg)
        self.log_text.configure(background=panel_bg, foreground=fg, insertbackground=fg)
        self.local_ini_text.configure(background=entry_bg, foreground=fg, insertbackground=fg)
        self.remote_ini_text.configure(background=entry_bg, foreground=fg, insertbackground=fg)

        # Additional buttons and widgets that don't inherit from ttk
        for widget in [
            self.pause_button,
            self.sftp_toggle_btn,
            getattr(self, "compare_btn", None),
            getattr(self, "up_button_local", None),
            getattr(self, "up_button_remote", None),
            getattr(self, "home_button_remote", None),
            getattr(self, "load_config_button", None),
            getattr(self, "save_config_button", None),
            getattr(self, "save_sched_button", None),
            getattr(self, "load_sched_button", None),
            getattr(self, "save_local_ini_button", None),
            getattr(self, "save_as_button", None)
        ]:
            if widget:
                try:
                    widget.configure(bg=bg, fg=fg, activebackground=selected_bg, activeforeground=selected_fg)
                except Exception:
                    pass

        self.is_night_mode = is_night

        # Persist night mode state
        self.config_manager.config["night_mode"] = is_night
        self.config_manager.save_config()

        self.log(f"Night mode {'enabled' if is_night else 'disabled'}")

        for win in self.winfo_children():
            if isinstance(win, ManualWindow):
                win.toggle_night_mode()

    def open_single_dialog(self, key, dialog_class, *args, **kwargs):
        if self.open_dialogs.get(key) and self.open_dialogs[key].winfo_exists():
            self.open_dialogs[key].lift()
            return

        dialog = dialog_class(self, *args, **kwargs)
        self.open_dialogs[key] = dialog

        def on_close():
            self.open_dialogs[key] = None
            dialog.destroy()

        dialog.protocol("WM_DELETE_WINDOW", on_close)

    def get_lockscreen_image_source(self):
        try:
            data = self.config_manager.config.get("lock_screen", {})
            mode = data.get("background_mode", "default")
            if mode == "single":
                return data.get("path")
            elif mode == "folder":
                return {
                    "folder": data.get("folder"),
                    "cycle_minutes": data.get("cycle_minutes", 5)
                }
            else:
                return "__DEFAULT__"
        except Exception as e:
            safe_error(f"[MainApp] Failed to retrieve lock screen image source: {e}")
            return None


    def open_lock_image_widget(self):
        # Create the config window for choosing lock screen background
        win = tk.Toplevel(self)
        win.title("Lock Screen Background Settings")
        win.geometry("400x200")
        win.attributes("-topmost", True)
        win.grab_set()

        if self.is_night_mode:
            win.configure(bg="#2a2a2e")
            fg = "#ffffff"
        else:
            fg = "#000000"

        ttk.Label(win, text="Choose lock screen background type:").pack(pady=10)

        def choose_single_image():
            path = filedialog.askopenfilename(
                title="Choose an image or GIF",
                filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif")],
                parent=win
            )
            if path:
                self.config_manager.config.setdefault("lock_screen", {})["background_mode"] = "single"
                self.config_manager.config["lock_screen"]["path"] = path

        def use_defaults():
            self.config_manager.config.setdefault("lock_screen", {})["background_mode"] = "default"

        def choose_folder():
            folder = filedialog.askdirectory(title="Choose Folder", parent=win)
            if folder:
                self.config_manager.config.setdefault("lock_screen", {})["background_mode"] = "folder"
                self.config_manager.config["lock_screen"]["folder"] = folder
                self.config_manager.config["lock_screen"]["cycle_minutes"] = 5

        ttk.Button(win, text="Choose Image or GIF", command=choose_single_image).pack(pady=5)
        ttk.Button(win, text="Use Default Images", command=use_defaults).pack(pady=5)
        ttk.Button(win, text="Cycle Through Folder", command=choose_folder).pack(pady=5)

        def on_close():
            self.config_manager.save_config()
            win.destroy()

        win.protocol("WM_DELETE_WINDOW", on_close)






    def refresh_calendar_events(self):
        if not hasattr(self, "schedule_calendar"):
            return
        self.schedule_calendar.build_calendar()
        bright = self.bright_colors_var.get()
        for sched in self.config_manager.config.get("schedules", []):
            status = sched.get("status", "upcoming")
            date_str = sched.get("date")
            if not date_str:
                continue
            try:
                date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
                color = get_calendar_tag_color(status, bright_mode=bright)
                self.calendar.tag_day(date_obj, color)
            except Exception as e:
                self.log(f"Failed to tag calendar day: {e}")


    def setup_schedule(self, existing=None, index=None, title_suffix=None):
        sched_win = tk.Toplevel(self)
        window_title = f"Schedule Setup {title_suffix}" if title_suffix else "Schedule Setup"
        sched_win.title(window_title)
        sched_win.geometry("390x470")
        sched_win.attributes('-topmost', True)
        sched_win.transient(self)

        title_var = tk.StringVar(value=existing.get("title", "") if existing else "")
        ttk.Label(sched_win, text="Title (optional):").pack()
        ttk.Entry(sched_win, textvariable=title_var).pack()

        day_var = tk.StringVar(value=existing.get("day", "Any") if existing else "Any")
        hour_var = tk.StringVar(value=str(existing.get("hour", 0)) if existing else "0")
        minute_var = tk.StringVar(value=str(existing.get("minute", 0)) if existing else "0")
        file_var = tk.StringVar(value=existing.get("filepath", "") if existing else "")
        remote_var = tk.StringVar(value=existing.get("remote_path", "/upload/serversettings.ini") if existing else "/upload/serversettings.ini")
        dom_var = tk.StringVar(value=existing.get("day_of_month", "Any") if existing else "Any")
        month_var = tk.StringVar(value=existing.get("month", "Any") if existing else "Any")
        repeat_var = tk.StringVar(value=existing.get("repeat", "none") if existing else "none")
        email_var = tk.BooleanVar(value=existing.get("send_email", True) if existing else True)

        ttk.Label(sched_win, text="Day of Week:").pack()
        ttk.Combobox(sched_win, textvariable=day_var, values=["Any"] + list(calendar.day_name)).pack()

        ttk.Label(sched_win, text="Month:").pack()
        ttk.Combobox(sched_win, textvariable=month_var, values=["Any"] + list(calendar.month_name)[1:]).pack()

        ttk.Label(sched_win, text="Day of Month:").pack()
        ttk.Combobox(sched_win, textvariable=dom_var, values=["Any"] + [str(i) for i in range(1, 32)]).pack()

        ttk.Label(sched_win, text="Hour:").pack()
        ttk.Combobox(sched_win, textvariable=hour_var, values=[str(i) for i in range(24)]).pack()

        ttk.Label(sched_win, text="Minute:").pack()
        ttk.Combobox(sched_win, textvariable=minute_var, values=[str(i) for i in range(60)]).pack()

        ttk.Label(sched_win, text="Repeat:").pack()
        ttk.Combobox(sched_win, textvariable=repeat_var, values=["none", "daily", "weekly", "monthly"]).pack()

        ttk.Label(sched_win, text="Local File:").pack()
        ttk.Entry(sched_win, textvariable=file_var).pack()
        ttk.Button(sched_win, text="Browse", command=lambda: file_var.set(filedialog.askopenfilename(parent=sched_win))).pack()

        ttk.Label(sched_win, text="Remote Path:").pack()
        ttk.Entry(sched_win, textvariable=remote_var).pack()

        ttk.Checkbutton(sched_win, text="Send Email Notification", variable=email_var).pack(pady=5)

        def save():
            try:
                schedule = {
                    "title": title_var.get(),
                    "day": day_var.get(),
                    "hour": int(hour_var.get()),
                    "minute": int(minute_var.get()),
                    "filepath": file_var.get(),
                    "remote_path": remote_var.get(),
                    "day_of_month": dom_var.get(),
                    "month": month_var.get(),
                    "repeat": repeat_var.get(),
                    "send_email": email_var.get()
                }
                if index is not None:
                    existing_run_history = self.config_manager.config['schedules'][index].get('run_history', {})
                    schedule['run_history'] = existing_run_history
                    self.config_manager.config['schedules'][index] = schedule
                else:
                    self.config_manager.config['schedules'].append(schedule)
                    self.config_manager.save_config()
                sched_win.destroy()
            except Exception as e:
                messagebox.showerror("Error", str(e))

        ttk.Button(sched_win, text="Save Schedule", command=save).pack(pady=5)



    def create_widgets(self):
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")

        self.minsize(1024, 720)
        # Ensure app_locked flag is initialized
        self.app_locked = False

        # Bind lock shortcut to trigger the same as "Lock App" menu
        self.bind_all("<Control-Shift-L>", lambda e: self.lock_screen_from_shortcut())

        # Create top bar
        top_frame = ttk.Frame(self)
        top_frame.pack(fill=tk.X)

    # Lock banner string and label (now inside top_frame for consistent layout)
        self.banner_var = tk.StringVar()
        self.banner_label = ttk.Label(top_frame, textvariable=self.banner_var, foreground="red", font=("Segoe UI", 9, "bold"))
        self.banner_label.pack(side=tk.TOP, fill=tk.X, pady=2)

        self.pause_button = ttk.Button(top_frame, text="Pause Scheduler", command=self.toggle_scheduler)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        self.sftp_toggle_btn = ttk.Button(top_frame, text="Disable SFTP", command=self.toggle_sftp)
        self.sftp_toggle_btn.pack(side=tk.LEFT, padx=5)
        self.remote_mode = tk.StringVar(value="sftp")

        ttk.Button(top_frame, text="Save Config", command=self.config_manager.save_config).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Load Config", command=self.load_config_and_refresh).pack(side=tk.RIGHT, padx=5)

        self.remote_toggle_btn = ttk.Button(
            top_frame,
            text="Mode: SFTP",
            style="remote.TButton",
            command=self.toggle_remote_mode
        )
        self.remote_toggle_btn.pack(side=tk.RIGHT, padx=25)

        file_frames = ttk.Frame(self)
        file_frames.pack(fill=tk.BOTH, expand=True)

        # Local Directory
        self.dir_frame = ttk.LabelFrame(file_frames, text="Local Directory")
        self.dir_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        local_inner = ttk.Frame(self.dir_frame)
        local_inner.pack(fill=tk.BOTH, expand=True)

        self.local_path_var = tk.StringVar(value=self.local_path)
        self.local_breadcrumb = PathLabel(local_inner, self.local_path_var, self.refresh_local_files, master=self)
        self.local_breadcrumb.pack(fill=tk.X)
        ttk.Button(local_inner, text="Up", command=self.navigate_up).pack(fill=tk.X)

        self.local_tree = ttk.Treeview(local_inner, columns=("Type",), show="tree headings", height=6)
        self.local_tree.heading("#0", text="Name")
        self.local_tree.heading("Type", text="Type")
        self.local_tree.pack(fill=tk.BOTH, expand=True)
        self.local_tree.bind("<Double-1>", self.on_local_double_click)
        self.local_tree.bind("<Button-3>", self.local_right_click)

        # Remote Directory
        self.remote_frame = ttk.LabelFrame(file_frames, text="Remote Directory")
        self.remote_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        remote_inner = ttk.Frame(self.remote_frame)
        remote_inner.pack(fill=tk.BOTH, expand=True)

        self.remote_breadcrumb = ttk.Label(remote_inner, text=self.remote_path)
        self.remote_breadcrumb.pack(fill=tk.X)
        self.remote_breadcrumb.bind("<Button-1>", self.navigate_remote_up)
        ttk.Button(remote_inner, text="Up", command=self.navigate_remote_up).pack(fill=tk.X)

        self.remote_tree = ttk.Treeview(remote_inner, columns=("Type",), show="tree headings", height=6)
        self.remote_tree.heading("#0", text="Name")
        self.remote_tree.heading("Type", text="Type")
        self.remote_tree.column("#0", anchor="w")
        self.remote_tree.column("Type", anchor="w")
        self.remote_tree.pack(fill=tk.BOTH, expand=True)
        self.remote_tree.bind("<Double-1>", self.on_remote_double_click)
        self.remote_tree.bind("<Button-3>", self.remote_right_click)

        # Home button
        ttk.Button(self.remote_frame, text="Home", command=self.navigate_remote_home).pack(fill=tk.X)

        # serversettings.ini Viewer Frame
        self.ini_frame = ttk.LabelFrame(self, text="serversettings.ini Viewer")
        self.ini_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        ini_inner = ttk.Frame(self.ini_frame)
        ini_inner.pack(fill=tk.BOTH, expand=True)

        self.local_ini_text = tk.Text(ini_inner, height=10, undo=True, maxundo=-1)
        self.local_ini_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))

        shared_scroll = tk.Scrollbar(ini_inner, orient="vertical")
        shared_scroll.pack(side=tk.LEFT, fill=tk.Y)

        self.remote_ini_text = tk.Text(ini_inner, height=10, state="disabled")
        self.remote_ini_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        def scroll_both(*args):
            self.local_ini_text.yview(*args)
            self.remote_ini_text.yview(*args)

        self.local_ini_text.configure(yscrollcommand=shared_scroll.set)
        self.remote_ini_text.configure(yscrollcommand=shared_scroll.set)
        shared_scroll.config(command=scroll_both)

        # Save + Compare Buttons on same line
        combined_frame = ttk.Frame(self.ini_frame)
        combined_frame.pack(anchor='w', pady=5)

        ttk.Button(combined_frame, text="Save to Local Directory", command=self.save_ini).pack(side=tk.LEFT, padx=5)
        ttk.Button(combined_frame, text="Save As...", command=self.save_ini_as).pack(side=tk.LEFT, padx=5)
        ttk.Button(combined_frame, text="Compare", command=self.compare_ini_files).pack(side=tk.LEFT, padx=5)

        self.sched_frame = ttk.LabelFrame(self, text="Schedules")
        self.sched_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        schedule_inner = ttk.Frame(self.sched_frame)
        schedule_inner.pack(fill=tk.BOTH, expand=True)

        left_panel = ttk.Frame(schedule_inner)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.sched_tree = ttk.Treeview(left_panel, columns=("desc", "fail", "success", "skip"), show="headings", selectmode="browse", height=10)
        self.sched_tree.heading("desc", text="Schedule")
        self.sched_tree.heading("fail", text="Fail")
        self.sched_tree.heading("success", text="Success")
        self.sched_tree.heading("skip", text="Skipped")
        self.sched_tree.column("fail", width=50, minwidth=40, stretch=True, anchor="center")
        self.sched_tree.column("success", width=50, minwidth=50, stretch=True, anchor="center")
        self.sched_tree.column("skip", width=50, minwidth=50, stretch=False, anchor="center")
        self.sched_tree.column("desc", width=900, minwidth=300, stretch=True, anchor="w")
        self.sched_tree.pack(fill=tk.BOTH, expand=True)
        self.sched_tree.bind("<ButtonRelease-1>", lambda e: self.on_schedule_click(e))
        self.sched_tree.bind("<Button-3>", self.schedule_right_click)

        # Moved buttons here
        sched_btns_frame = ttk.Frame(left_panel)
        sched_btns_frame.pack(anchor="w", pady=5, padx=5)
        ttk.Button(sched_btns_frame, text="Setup Schedule", command=self.setup_schedule).pack(side=tk.LEFT, padx=5)
        ttk.Button(sched_btns_frame, text="Load Schedules", command=self.load_schedules_from_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(sched_btns_frame, text="Save Schedules", command=self.save_schedules_to_file).pack(side=tk.LEFT, padx=5)

        right_panel = ttk.Frame(schedule_inner)
        right_panel.pack(side=tk.RIGHT, anchor="ne", padx=10)

        self.schedule_calendar = ScheduleCalendar(right_panel, self)
        self.schedule_calendar.pack()

        self.log_frame = ttk.LabelFrame(self, text="Log")
        self.log_frame.pack(fill=tk.X, padx=5, pady=5)

        self.log_text = tk.Text(self.log_frame, height=2)  # smaller height
        self.log_text.pack(fill=tk.BOTH, expand=False)

        self.status_bar = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor="w")
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        if self.scheduler_running.get():
            self.pause_button.config(text="Pause Scheduler", style="sftpOn.TButton")
        else:
            self.pause_button.config(text="Resume Scheduler", style="sftpOff.TButton")

        self.update_sftp_button_style()

        self.update_lock_banner()

    def lock_screen_from_shortcut(self):
        safe_debug("[lock_screen_from_shortcut] Lock triggered via shortcut")
        self.active_lock_window = LockScreen(self, fernet)

        self.update_lock_banner()  # update once when lock opens

        def check_closed():
            if hasattr(self, "active_lock_window") and self.active_lock_window:
                if not self.active_lock_window.winfo_exists():
                    safe_debug("[lock_screen_from_shortcut] Lock window closed")
                    self.active_lock_window = None
                    self.update_lock_banner()  # update once when lock closes
                else:
                    self.after(1000, check_closed)

        self.after(1000, check_closed)

    def update_lock_banner(self):
        # Look for existing LockScreen window
        lock_open = any(isinstance(w, LockScreen) for w in self.winfo_children())
        scheduler_state = self.scheduler_running.get() if hasattr(self, "scheduler_running") else None

        safe_debug(f"[update_lock_banner] Banner refresh: lock_open={lock_open}, scheduler_running={scheduler_state}")

        if lock_open:
            if scheduler_state:
                self.banner_var.set("🔒 App Locked – Schedules will continue running in background")
            else:
                self.banner_var.set("🔒 App Locked – Scheduler is paused")
        else:
            self.banner_var.set("")







    def toggle_scheduler(self):
        if self.scheduler_running.get():
            self.scheduler.pause()
            self.scheduler_running.set(False)
            self.config_manager.config["scheduler_paused"] = True
            self.pause_button.config(text="Resume Scheduler", style="sftpOff.TButton")
            self.log("Scheduler paused")
        else:
            self.scheduler.resume()
            self.scheduler_running.set(True)
            self.config_manager.config["scheduler_paused"] = False
            self.pause_button.config(text="Pause Scheduler", style="sftpOn.TButton")
            self.log("Scheduler resumed")
        
        self.update_lock_banner()
        self.config_manager.save_config()

    def toggle_sftp(self):
        if self.sftp_enabled.get():
            # Disable SFTP
            self.sftp_enabled.set(False)
            self.config_manager.config["sftp_enabled"] = False
            self.config_manager.save_config()
            self.remote_tree.delete(*self.remote_tree.get_children())
            self.remote_breadcrumb.config(text="SFTP is OFF")
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.log("SFTP Disabled")
            return

        # Enable attempt: show orange "Connecting..."
        self.sftp_toggle_btn.config(text="Connecting...", style="orange.TButton")
        self.remote_breadcrumb.config(text="SFTP: checking...")
        self.update_idletasks()

        info = self.config_manager.config.get("sftp", {})
        if not all(info.get(k) for k in ['host', 'port', 'username', 'password']):
            self.remote_breadcrumb.config(text="Missing SFTP config")
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.log("SFTP config is incomplete.")
            return

        try:
            _ = self.get_sftp()  # just ensures connection
            self.sftp_enabled.set(True)
            self.config_manager.config["sftp_enabled"] = True
            self.config_manager.save_config()
            self.sftp_toggle_btn.config(text="Disable SFTP", style="sftpOn.TButton")
            self.remote_breadcrumb.config(text="SFTP is ACTIVE")
            self.refresh_remote_files()
            self.log("SFTP Enabled")
        except Exception as e:
            self.sftp_enabled.set(False)
            self.config_manager.config["sftp_enabled"] = False
            self.config_manager.save_config()
            self.remote_breadcrumb.config(text="SFTP FAILED")
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.log(f"SFTP connect failed: {e}")

    def toggle_remote_mode(self):
        current = self.remote_mode.get()
        new_mode = "lan" if current == "sftp" else "sftp"
        self.remote_mode.set(new_mode)
        self.remote_toggle_btn.config(text=f"Mode: {new_mode.upper()}")

        if new_mode == "lan":
            # Disconnect SFTP if active
            if self.sftp_client:
                try:
                    self.sftp_client.close()
                    self.log("SFTP disconnected due to mode switch.")
                except Exception as e:
                    self.log(f"Error disconnecting SFTP: {e}")
                self.sftp_client = None

            # Disable SFTP button
            self.sftp_toggle_btn.config(state='disabled')

        elif new_mode == "sftp":
            # Re-enable SFTP button
            self.sftp_toggle_btn.config(state='normal')

        self.refresh_remote_tree()

    def update_sftp_button_style(self):
        # Disable remote interaction until confirmed
        self.remote_tree.delete(*self.remote_tree.get_children())
        self.remote_breadcrumb.config(text="SFTP: checking...")

        if not self.sftp_enabled.get():
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.remote_breadcrumb.config(text="SFTP is OFF")
            return

        self.sftp_toggle_btn.config(text="Connecting...", style="orange.TButton")
        self.update_idletasks()

        info = self.config_manager.config.get('sftp', {})
        if not all(info.get(k) for k in ['host', 'port', 'username', 'password']):
            self.sftp_toggle_btn.config(text="Invalid Config", style="sftpOff.TButton")
            self.remote_breadcrumb.config(text="Missing SFTP config")
            return

        try:
            _ = self.get_sftp()
            self.sftp_toggle_btn.config(text="Disable SFTP", style="sftpOn.TButton")
            self.remote_breadcrumb.config(text="SFTP is ACTIVE")
        except Exception:
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.remote_breadcrumb.config(text="SFTP is OFF")


    def enable_lan_browser(self):
        """Populates the remote_tree using the configured LAN path."""
        self.log("Switched to LAN Mode.")

        # Clear remote tree
        for item in self.remote_tree.get_children():
            self.remote_tree.delete(item)

        # Pull from the unified config structure
        path = self.config_manager.config.get("lan_path", "").strip()

        if not path:
            self.log("No LAN path set. Please configure one in LAN Settings.")
            return

        if not os.path.exists(path):
            self.log(f"LAN path not found: {path}")
            return

        try:
            items = os.listdir(path)
            for name in items:
                full_path = os.path.join(path, name)
                self.remote_tree.insert('', 'end', text=name, values=(full_path,))
        except Exception as e:
            self.log(f"Failed to list LAN path: {e}")



    def refresh_remote_tree(self):
        mode = self.remote_mode.get()
        if mode == "lan":
            self.enable_lan_browser()
        elif mode == "sftp":
            self.log("SFTP mode selected - remote tree loading pending implementation.")

    def compare_ini_files(self):
        self.local_ini_text.tag_delete("diff")
        self.local_ini_text.tag_delete("missing")
        self.remote_ini_text.tag_delete("diff")
        self.remote_ini_text.tag_delete("missing")
        if not hasattr(self, 'local_ini_filename') or not hasattr(self, 'remote_ini_filename'):
            messagebox.showinfo("Compare", "Load both local and remote .ini files first.")
            return

        if os.path.basename(self.local_ini_filename).lower() != os.path.basename(self.remote_ini_filename).lower():
            messagebox.showwarning("Compare", "Cannot compare different files.\nFile names must match.")
            return

        self.local_ini_text.tag_delete("diff")
        self.local_ini_text.tag_delete("missing")
        self.remote_ini_text.tag_delete("diff")
        self.remote_ini_text.tag_delete("missing")

        self.local_ini_text.tag_config("diff", background="lightyellow")
        self.local_ini_text.tag_config("missing", background="#f8d0d8")
        self.remote_ini_text.tag_config("diff", background="lightyellow")
        self.remote_ini_text.tag_config("missing", background="#f8d0d8")

        local_lines = self.local_ini_text.get("1.0", tk.END).splitlines()
        remote_lines = self.remote_ini_text.get("1.0", tk.END).splitlines()

        def strip_prefix(line):
            return line[4:] if len(line) > 4 else ""

        local_clean = {strip_prefix(line).strip().split('=')[0]: (i, strip_prefix(line).strip()) for i, line in enumerate(local_lines)}
        remote_clean = {strip_prefix(line).strip().split('=')[0]: (i, strip_prefix(line).strip()) for i, line in enumerate(remote_lines)}

        all_keys = sorted(set(local_clean.keys()).union(set(remote_clean.keys())))

        for key in all_keys:
            l_data = local_clean.get(key)
            r_data = remote_clean.get(key)

            if l_data and r_data:
                l_index, l_line = l_data
                r_index, r_line = r_data
                if l_line.strip().lower() != r_line.strip().lower():
                    l_tag = f"{l_index + 1}.0"
                    r_tag = f"{r_index + 1}.0"
                    self.local_ini_text.tag_add("diff", l_tag, f"{l_index + 1}.end")
                    self.remote_ini_text.tag_add("diff", r_tag, f"{r_index + 1}.end")
            elif l_data:
                l_index, _ = l_data
                l_tag = f"{l_index + 1}.0"
                self.local_ini_text.tag_add("missing", l_tag, f"{l_index + 1}.end")
            elif r_data:
                r_index, _ = r_data
                r_tag = f"{r_index + 1}.0"
                self.remote_ini_text.tag_add("missing", r_tag, f"{r_index + 1}.end")

        self.log("Intelligent .ini comparison completed.")

    def save_ini_as(self):
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".ini",
                filetypes=[("INI Files", "*.ini")],
                title="Save ServerSettings.ini As"
            )
            if file_path:
                with open(file_path, 'w') as f:
                    f.write(self.local_ini_text.get("1.0", tk.END))
                self.log(f"serversettings.ini saved to {file_path}")
        except Exception as e:
            self.log(f"Failed to save serversettings.ini: {e}")

    def toggle_dir_copy(self):
        self.config_manager.config['enable_dir_copy'] = self.enable_dir_copy_var.get()

        if 'sftp' in self.config_manager.config and 'password' in self.config_manager.config['sftp']:
            try:
                raw_password = self.config_manager.config['sftp']['password']
                encrypted_pw = fernet.encrypt(raw_password.encode()).decode()
                self.config_manager.config['sftp']['password'] = encrypted_pw
                with open(CONFIG_FILE, 'w') as f:
                    json.dump(self.config_manager.config, f, indent=4)
                self.config_manager.config['sftp']['password'] = raw_password
            except Exception as e:
                self.log(f"Failed to save encrypted password: {e}")
        else:
            self.config_manager.save_config()

        state = "Enabled" if self.enable_dir_copy_var.get() else "Disabled"
        self.log(f"Directory Copy: {state}")

    def toggle_logging_options(self):
        # Get requested new states
        new_log_enabled = self.safety_menu_log_var.get()
        new_debug_enabled = self.safety_menu_debug_var.get()

        msg = f"Logging {'enabled' if new_log_enabled else 'disabled'}, Debug {'enabled' if new_debug_enabled else 'disabled'}"

        # Use current logger before reconfiguring
        logging.info(msg)
        logging.debug(f"[UI] {datetime.now().strftime('%H:%M:%S')} - {msg}")

        # Now commit config changes
        notification_config['log_enabled'] = new_log_enabled
        notification_config['debug_enabled'] = new_debug_enabled

        with open(NOTIFICATION_CONFIG_FILE, 'w') as f:
            json.dump(notification_config, f, indent=2)

        setup_loggers(
            log_enabled=new_log_enabled,
            debug_enabled=new_debug_enabled,
            log_file=LOG_FILE,
            debug_log_file=DEBUG_LOG_FILE
        )

        self.log(msg)

    def toggle_sftp_throttle(self):
        current = self.honor_sftp_limits.get()
        self.config_manager.config["honor_sftp_limits"] = current
        self.config_manager.save_config()
        state = "enabled" if current else "disabled"
        self.log(f"SFTP throttle mode {state}")

    def navigate_remote_home(self):
        safe_debug("Method called: navigate_remote_home")

        mode = self.remote_mode.get().lower()

        if mode == "lan":
            self.enable_lan_browser()
            return

        if mode == "sftp":
            if not self.sftp_enabled.get():
                self.log("SFTP is disabled - cannot go to remote home.")
                return

            try:
                sftp = self.get_sftp()
                try:
                    self.remote_path = sftp.client.normalize('.')
                except Exception:
                    self.remote_path = self.remote_base or "/"
                self.remote_breadcrumb.config(text=self.remote_path)
                self.refresh_remote_files()
            except Exception as e:
                self.log(f"Failed to go to SFTP home: {e}")
            return

        self.log("Unsupported remote mode.")



    def load_config_and_refresh(self):
        file_path = filedialog.askopenfilename(
            title="Load Config File",
            filetypes=[("JSON Files", "*.json")]
        )
        if file_path:
            self.config_manager.load_config_file(file_path)
            self.refresh_local_files()
            self.refresh_remote_files()
            self.log("Configuration loaded.")

    def navigate_up(self, event=None):
        parent = os.path.dirname(self.local_path)
        if os.path.exists(parent) and parent != self.local_path:
            self.local_path = parent
            self.local_path_var.set(self.local_path)  # Update the path label correctly
            self.refresh_local_files()

    def navigate_remote_up(self, event=None):
        if self.remote_mode.get() != "sftp":
            self.log("Skipped remote up: Not in SFTP mode.")
            return

        if not self.sftp_enabled.get():
            self.log("SFTP is disabled - cannot navigate remote.")
            return

        if not self.remote_path:
            self.log("Remote path is not set.")
            return

        parent = os.path.dirname(self.remote_path.rstrip("/")) or "/"
        self.remote_path = parent
        self.remote_breadcrumb.config(text=self.remote_path)
        self.refresh_remote_files()

    def refresh_local_files(self):
        self.local_tree.delete(*self.local_tree.get_children())
        try:
            for item in sorted(os.listdir(self.local_path)):
                full_path = os.path.join(self.local_path, item)
                if os.path.isdir(full_path):
                    self.local_tree.insert('', 'end', text=item, values=("Folder",))
                elif item.lower() == "serversettings.ini":
                    self.local_tree.insert('', 'end', text=item, values=("INI File",))
        except Exception as e:
            self.log(f"Failed to read local directory: {e}")

    def on_local_double_click(self, event):
        item = self.local_tree.identify('item', event.x, event.y)
        if not item:
            return
        selected = self.local_tree.item(item)['text']
        path = os.path.join(self.local_path, selected)

        if os.path.isdir(path):
            self.local_path = path
            self.local_path_var.set(self.local_path)
            self.refresh_local_files()
        elif selected.lower() == "serversettings.ini":
            try:
                with open(path, 'r') as f:
                    lines = f.readlines()
                self.local_ini_text.tag_remove("diff", "1.0", tk.END)
                self.local_ini_text.tag_remove("missing", "1.0", tk.END)
                self.local_ini_text.delete("1.0", tk.END)
                for i, line in enumerate(lines, 1):
                    self.local_ini_text.insert(tk.END, f"{i:03} {line}")
                self.local_ini_filename = path 
                self.log(f"Loaded local {selected} into viewer.")
            except Exception as e:
                self.log(f"Failed to open serversettings.ini: {e}")

    def on_remote_double_click(self, event):
        item = self.remote_tree.identify('item', event.x, event.y)
        if not item:
            return

        selected = self.remote_tree.item(item)['text']
        if not selected:
            return

        # Handle going up
        if selected == "..":
            self.remote_path = os.path.dirname(self.remote_path.rstrip("/")) or "/"
            self.refresh_remote_files()
            return

        new_path = os.path.join(self.remote_path, selected.rstrip("/")).replace("\\", "/")

        if selected.lower() == "serversettings.ini":
            try:
                lines = self.read_remote_file_lines(new_path)
                self.remote_ini_text.configure(state='normal')
                self.remote_ini_text.tag_remove("diff", "1.0", tk.END)
                self.remote_ini_text.tag_remove("missing", "1.0", tk.END)
                self.remote_ini_text.delete("1.0", tk.END)
                for i, line in enumerate(lines, 1):
                    self.remote_ini_text.insert(tk.END, f"{i:03} {line}")
                self.remote_ini_text.configure(state='disabled')
                self.remote_ini_filename = new_path
                self.log(f"Loaded remote {selected} into viewer.")
            except Exception as e:
                self.log(f"Failed to load remote serversettings.ini: {e}")
        elif self.is_remote_directory(selected):
            self.remote_path = new_path
            self.refresh_remote_files()

    def is_remote_directory(self, name):
        return name.endswith("/")


    def refresh_remote_files(self, skip_get=False):
        safe_debug("Method called: refresh_remote_files")

        self.remote_tree.delete(*self.remote_tree.get_children())

        try:
            if self.remote_mode.get() == "sftp":
                sftp = self.sftp_client if skip_get else self.get_sftp()

                if self.remote_base is None:
                    self.remote_base = sftp.home_path
                    self.remote_path = self.remote_base
                    safe_debug(f"Detected SFTP home path: {self.remote_base}")

                try:
                    entries = sftp.listdir(self.remote_path)
                    safe_debug(f"Raw directory entries from {self.remote_path}: {entries}")
                except IOError as e:
                    logging.warning(f"Directory listing failed for {self.remote_path}: {e}")
                    self.remote_path = self.remote_base
                    entries = sftp.listdir(self.remote_path)
                    safe_debug(f"Fallback directory entries from {self.remote_path}: {entries}")

            else:
                entries = self.list_remote_dir(self.remote_path)

            self.remote_breadcrumb.config(text=self.remote_path)

            # Add parent navigation
            if self.remote_mode.get() == "sftp":
                if self.remote_path != self.remote_base:
                    self.remote_tree.insert('', 'end', text="..", values=("..",))
            else:
                if os.path.dirname(self.remote_path.rstrip("/")) != self.remote_path:
                    self.remote_tree.insert('', 'end', text="..", values=("..",))

            if not entries:
                self.log("Remote directory is empty or not accessible.")

            for attr in entries:
                try:
                    name = attr.filename if self.remote_mode.get() == "sftp" else attr
                    if not name:
                        continue

                    is_dir = self.is_remote_directory(name)
                    file_type = "Folder" if is_dir else "File"
                    display_name = name + ("/" if is_dir else "")
                    self.remote_tree.insert('', 'end', text=display_name, values=(file_type,))
                    safe_debug(f"Listed remote item: {display_name} ({file_type})")
                except Exception as e:
                    logging.warning(f"Error displaying remote entry: {e}")

            self.status_var.set("Browsing: " + self.remote_path)

        except Exception as e:
            self.remote_tree.delete(*self.remote_tree.get_children())
            self.remote_breadcrumb.config(text="Error")
            self.status_var.set("Connection failed")
            self.log(f"Failed to list remote: {e}")




    def log(self, msg):
        timestamped = f"{datetime.now().strftime('%H:%M:%S')} - {msg}"
        self.status_var.set(msg)
        if notification_config.get("log_enabled", True):
            logging.info(msg)
        if notification_config.get("debug_enabled", False):
            logging.debug(f"[UI] {timestamped}")
        self.log_text.insert(tk.END, f"{timestamped}\n")
        self.log_text.see(tk.END)

    def save_ini(self):
        path = os.path.join(self.local_path, "serversettings.ini")
        try:
            with open(path, 'w') as f:
                f.write(self.ini_text.get("1.0", tk.END))
            self.log("serversettings.ini saved.")
        except Exception as e:
            self.log(f"Failed to save serversettings.ini: {e}")



    def get_sftp(self):
        """Returns cached SFTP client or creates one if not yet connected."""
        import traceback
        stack = traceback.format_stack(limit=5)
        caller = stack[-3].strip()
        safe_debug(f"get_sftp() called from: {caller}")

        if hasattr(self, "sftp_client") and self.sftp_client:
            return self.sftp_client

        sftp_conf = self.config_manager.config.get("sftp", {})
        host = sftp_conf.get("host")
        port = int(sftp_conf.get("port", 22))
        user = sftp_conf.get("username")
        pw = sftp_conf.get("password")

        if not all([host, port, user, pw]):
            raise ValueError("SFTP settings incomplete.")

        safe_debug("Creating Transport-based SFTP connection")
        self.log(f"Connecting to SFTP at {host}:{port} as {user}")
        sftp = SFTPManager(host, port, user, pw)

        self.sftp_client = sftp
        self.sftp_session = sftp
        return sftp

    def list_remote_dir(self, path):
        if self.remote_mode.get() == "sftp":
            sftp = self.get_sftp()
            return sftp.listdir(path)
        else:
            try:
                return os.listdir(path)
            except Exception as e:
                self.log(f"LAN listing failed: {e}")
                return []

    def is_remote_directory(self, name_or_path):
        if self.remote_mode.get() == "sftp":
            try:
                sftp = self.get_sftp()
                full_path = os.path.join(self.remote_path, name_or_path).replace("\\", "/")
                safe_debug(f"Checking if SFTP path is a directory: {full_path}")
                attr = sftp.client.stat(full_path)
                return stat.S_ISDIR(attr.st_mode)
            except Exception as e:
                self.log(f"Error checking SFTP entry type: {e}")
        else:
            try:
                return os.path.isdir(os.path.join(self.remote_path, name_or_path))
            except Exception as e:
                self.log(f"Error checking LAN entry type: {e}")
        return False

    def read_remote_file_lines(self, full_path):
        if self.remote_mode.get() == "sftp":
            with self.get_sftp().client.open(full_path, 'r') as f:
                return f.readlines()
        else:
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.readlines()


  # def get_sftp(self):
  #     safe_debug(f"get_sftp() called by: {caller}")
  #     info = self.config_manager.config['sftp']
  #     return SFTPManager(
  #         host=info['host'],
  #         port=info['port'],
  #         username=info['username'],
  #         password=info['password']
  #     )




    def refresh_schedules(self):
        if not hasattr(self, "sched_tree"):
            return

        self.sched_tree.delete(*self.sched_tree.get_children())

        schedules = self.config_manager.config.get('schedules', [])
        try:
            sorted_scheds = sorted(schedules, key=get_next_run)
        except Exception as e:
            logging.warning(f"Failed to sort schedules: {e}")
            sorted_scheds = schedules

        for i, sched in enumerate(sorted_scheds):
            try:
                hour = sched.get('hour')
                minute = sched.get('minute')
                if hour is None or minute is None:
                    raise ValueError("Missing hour or minute")

                repeat = sched.get('repeat', 'none').strip().lower()
                if repeat == "none":
                    repeat_desc = "once only"
                elif repeat == "daily":
                    repeat_desc = "Every day"
                elif repeat == "weekly":
                    repeat_desc = f"Every week on {sched.get('day', 'Any')}"
                elif repeat == "monthly":
                    repeat_desc = f"Every month on day {sched.get('day_of_month', '1')}"
                else:
                    repeat_desc = f"Repeat: {repeat}"

                local_file = os.path.basename(sched.get('filepath', ''))
                local_dir = os.path.dirname(sched.get('filepath', ''))
                title = sched.get("title", "").strip()
                title_text = f"{title} | " if title else ""
                time_str = f"at {hour:02}:{minute:02}"
                desc = f"#{i+1} - {title_text}{repeat_desc} {time_str} | sending '{local_file}' from '{local_dir}'"

                sched.setdefault("enabled", True)
                sched.setdefault("email_on_fail", True)
                sched.setdefault("email_on_success", False)
                sched.setdefault("email_on_skip", True)

                fail = "[x]" if sched["email_on_fail"] else "[ ]"
                succ = "[x]" if sched["email_on_success"] else "[ ]"
                skip = "[x]" if sched["email_on_skip"] else "[ ]"
                tags = ("disabled",) if not sched["enabled"] else ()

                self.sched_tree.insert("", "end", iid=str(i), values=(desc, fail, succ, skip), tags=tags)
            except Exception as e:
                logging.warning(f"Failed to insert schedule {i}: {e}")

        self.sched_tree.tag_configure("disabled", foreground="#888888")


    def on_schedule_click(self, event):
        row_id = self.sched_tree.identify_row(event.y)
        col = self.sched_tree.identify_column(event.x)
        if not row_id or not col:
            return

        item = self.sched_tree.item(row_id)
        desc = item["values"][0]

        import re
        match = re.match(r"#(\d+)", desc)
        if not match:
            return

        sorted_index = int(match.group(1)) - 1
        sorted_scheds = sorted(self.config_manager.config["schedules"], key=get_next_run)
        if 0 <= sorted_index < len(sorted_scheds):
            schedule = sorted_scheds[sorted_index]
        else:
            return

        # Only update the config values based on column
        if col == "#1":  # Toggle enabled
            schedule["enabled"] = not schedule.get("enabled", True)
        elif col == "#2":  # Toggle fail
            schedule["email_on_fail"] = not schedule.get("email_on_fail", True)
        elif col == "#3":  # Toggle success
            schedule["email_on_success"] = not schedule.get("email_on_success", False)
        elif col == "#4":  # Toggle skipped
            schedule["email_on_skip"] = not schedule.get("email_on_skip", True)
        else:
            schedule["enabled"] = not schedule.get("enabled", True)

        self.config_manager.save_config()
        self.refresh_schedules()
        self.schedule_calendar.build_calendar()



    def save_schedules_to_file(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json")],
            title="Save Schedule Configuration"
        )
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    json.dump(self.config_manager.config['schedules'], f, indent=4)
                self.log(f"Schedules saved to {file_path}")

                # Store the last used schedule file path for reference (e.g., reload support)
                self.config_manager.config['last_schedule_file'] = file_path
                self.config_manager.save_config()
            except Exception as e:
                self.log(f"Failed to save schedules: {e}")

    def load_schedules_from_file(self):
        try:
            if os.path.exists(SCHEDULE_CONFIG_FILE):
                with open(SCHEDULE_CONFIG_FILE, "r") as f:
                    self.config_manager.config["schedules"] = json.load(f)
                safe_info("Schedules loaded from file.")
            else:
                self.config_manager.config["schedules"] = []
                safe_info("Schedule config file not found, starting with empty list.")
        except Exception as e:
            self.config_manager.config["schedules"] = []
            safe_error(f"Failed to load schedule config, fallback to empty: {e}")

    def load_schedules(self):
        if os.path.exists(self.schedule_file):
            try:
                with open(self.schedule_file, 'r') as f:
                    self.config['schedules'] = json.load(f)

                # Check if we are in MainApp context to trigger full UI update
                if hasattr(self, "refresh_schedules") and callable(self.refresh_schedules):
                    self.refresh_schedules()

                if hasattr(self, "schedule_calendar") and hasattr(self.schedule_calendar, "build_calendar"):
                    self.schedule_calendar.build_calendar()

                if hasattr(self, "log") and callable(self.log):
                    self.log("Schedules loaded from file.")
                else:
                    safe_log("Schedules loaded from file.")
            except Exception as e:
                safe_error(f"Failed to load schedules: {e}")

    def local_right_click(self, event):
        menu = tk.Menu(self, tearoff=0)
        iid = self.local_tree.identify_row(event.y)
        if iid:
            menu.add_command(label="Copy", command=lambda: self.copy_local(iid))
        menu.add_command(label="Paste", command=self.paste_local)
        menu.post(event.x_root, event.y_root)

    def paste_local(self):
        src = self.config_manager.config['clipboard']
        if not src:
            self.log("Clipboard is empty")
            return
        dst = os.path.join(self.local_path, os.path.basename(src.rstrip("/")))
        try:
            if src.startswith('/') or ':' in src:
                sftp = self.get_sftp()
                try:
                    attrs = sftp.client.stat(src)
                    expected_size = attrs.st_size
                    if stat.S_ISDIR(attrs.st_mode):
                        if not self.enable_dir_copy_var.get():
                            self.log("Directory copy is disabled.")
                            return
                        os.makedirs(dst, exist_ok=True)
                        self.download_remote_dir(sftp, src, dst)
                        self.log(f"Downloaded folder {src} to {dst}")
                    else:
                        tmp_path = dst + ".partial"
                        attempt = 0
                        while attempt < 2:
                            sftp.download(src, tmp_path)
                            actual_size = os.path.getsize(tmp_path)
                            if actual_size == expected_size:
                                os.rename(tmp_path, dst)
                                self.log(f"Downloaded {src} to {dst}")
                                break
                            else:
                                self.log(f"Size mismatch on attempt {attempt+1}: {actual_size} != {expected_size}")
                                os.remove(tmp_path)
                                attempt += 1
                                time.sleep(1)
                        else:
                            raise IOError(f"size mismatch after retries: final {actual_size} != {expected_size}")
                except IOError as e:
                    self.log(f"Failed to download {src}: {e}")
              # finally:
                #   sftp.close()

            elif os.path.exists(src):
                if os.path.isdir(src):
                    if os.path.exists(dst):
                        shutil.rmtree(dst)
                    shutil.copytree(src, dst)
                else:
                    shutil.copy2(src, dst)
                self.log(f"Copied {src} to {dst}")
            else:
                self.log("Clipboard source path does not exist")
            self.refresh_local_files()
        except Exception as e:
            self.log(f"Failed to paste: {e}")


    def download_remote_dir(self, sftp, remote_dir, local_dir):
        for item in sftp.client.listdir_attr(remote_dir):
            remote_path = os.path.join(remote_dir, item.filename).replace("\\", "/")
            local_path = os.path.join(local_dir, item.filename)

            if stat.S_ISDIR(item.st_mode):
                os.makedirs(local_path, exist_ok=True)
                self.download_remote_dir(sftp, remote_path, local_path)
            else:
                tmp_path = local_path + ".partial"
                try:
                    sftp.download(remote_path, tmp_path)
                    actual_size = os.path.getsize(tmp_path)
                    expected_size = item.st_size
                    if actual_size != expected_size:
                        self.log(f"Size mismatch on {remote_path}: expected {expected_size}, got {actual_size}")
                        os.remove(tmp_path)
                        continue
                    os.rename(tmp_path, local_path)
                    self.log(f"Downloaded {remote_path} to {local_path}")
                except Exception as e:
                    self.log(f"Failed to download {remote_path}: {e}")

    def remote_right_click(self, event):
        iid = self.remote_tree.identify_row(event.y)
        if iid:
            menu = tk.Menu(self, tearoff=0)
            menu.add_command(label="Copy File", command=lambda: self.copy_remote(iid))
            menu.add_command(label="Paste File", command=self.paste_remote)
            menu.add_command(label="Copy Path", command=lambda: self.copy_remote_path(iid))
            menu.post(event.x_root, event.y_root)

    def copy_remote_path(self, iid):
        try:
            item = self.remote_tree.item(iid)['text']
            remote_path = os.path.join(self.remote_path, item).replace("\\", "/")
            self.clipboard_clear()
            self.clipboard_append(remote_path)
            self.log(f"Copied remote path to clipboard: {remote_path}")
        except Exception as e:
            self.log(f"Failed to copy remote path: {e}")

    def copy_local(self, iid):
        item = self.local_tree.item(iid)['text']
        path = os.path.join(self.local_path, item)
        if os.path.exists(path):
            self.config_manager.config['clipboard'] = path
            self.log(f"Copied {item} from local")
        else:
            self.log(f"Local item not found: {item}")

    def copy_remote(self, iid):
        item = self.remote_tree.item(iid)['text']
        remote_path = os.path.join(self.remote_path, item).replace("\\", "/")
        self.config_manager.config['clipboard'] = remote_path
        self.log(f"Copied {item} from remote")

    def paste_remote(self):
        src = self.config_manager.config['clipboard']
        if src and os.path.exists(src):
            try:
                sftp = self.get_sftp()
                dst = os.path.join(self.remote_path, os.path.basename(src)).replace("\\", "/")
                if os.path.isdir(src):
                    if not self.enable_dir_copy_var.get():
                        self.log("Directory copy is disabled in settings.")
                        return
                    self.upload_folder(sftp, src, dst)
                else:
                    sftp.upload(src, dst)
              # sftp.close()
                self.refresh_remote_files()
                self.log(f"Uploaded {src} to {dst}")
            except Exception as e:
                self.log(f"Upload failed: {e}")
        else:
            self.log("Clipboard is empty or source path does not exist")


    def upload_folder(self, sftp, local_folder, remote_folder):
        try:
            try:
                sftp.client.mkdir(remote_folder)
            except IOError:
                pass
            for item in os.listdir(local_folder):
                local_path = os.path.join(local_folder, item)
                remote_path = f"{remote_folder}/{item}".replace("\\", "/")
                if os.path.isdir(local_path):
                    self.upload_folder(sftp, local_path, remote_path)
                else:
                    sftp.upload(local_path, remote_path)
        except Exception as e:
            self.log(f"Failed to upload folder {local_folder}: {e}")

    def schedule_right_click(self, event):
        row_id = self.sched_tree.identify_row(event.y)
        if not row_id:
            return

        self.sched_tree.selection_set(row_id)

        index = int(row_id)
        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Edit", command=lambda: self.edit_schedule(index))
        menu.add_command(label="New Schedule", command=self.setup_schedule)
        menu.add_separator()
        menu.add_command(label="Copy", command=lambda: self.copy_schedule(index))
        menu.add_command(label="Delete", command=lambda: self.delete_schedule(index))
        menu.add_separator()
        menu.add_command(label="Move Up", command=lambda: self.move_schedule_up(index))
        menu.add_command(label="Move Down", command=lambda: self.move_schedule_down(index))
        menu.add_separator()
        menu.add_command(label="Properties", command=lambda: self.view_schedule_properties(index))
        menu.tk_popup(event.x_root, event.y_root)

    def edit_schedule(self, index):
        try:
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            target = sorted_scheds[index]
            unsorted_index = self.config_manager.config['schedules'].index(target)
            self.setup_schedule(existing=target, index=unsorted_index, title_suffix=f"#{index + 1}")
        except Exception as e:
            self.log(f"Failed to edit schedule: {e}")

    def delete_selected_schedule(self):
        selected = self.sched_tree.selection()
        if not selected:
            return

        try:
            indices = sorted([int(iid) for iid in selected], reverse=True)
            removed = 0
            for index in indices:
                actual_index = index - removed
                if 0 <= actual_index < len(self.config_manager.config['schedules']):
                    del self.config_manager.config['schedules'][actual_index]
                    self.log(f"Deleted schedule #{actual_index + 1}")
                    removed += 1
                else:
                    self.log(f"Failed to delete schedule: index {actual_index} out of range")

            self.config_manager.save_config()
            self.refresh_schedules()
            self.schedule_calendar.build_calendar()
        except Exception as e:
            self.log(f"Failed to delete schedule: {e}")

    def delete_schedule(self, index):
        try:
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            target = sorted_scheds[index]
            self.config_manager.config['schedules'].remove(target)
            self.config_manager.save_config()
            self.schedule_calendar.build_calendar() if hasattr(self, "schedule_calendar") else None  # Ensure calendar reflects deletion
            self.log(f"Deleted schedule #{index + 1}")
        except Exception as e:
            self.log(f"Failed to delete schedule: {e}")

    def highlight_schedule_calendar(self, schedule):
        """
        Highlights only the selected schedule's history on the calendar based on run_history.
        """
        self.clear_calendar_tags()
        if not schedule or "run_history" not in schedule:
            return

        for timestamp, status in schedule.get("run_history", {}).items():
            try:
                dt = datetime.fromisoformat(timestamp)
                tag = get_calendar_tag_color(status)
                self.calendar.calevent_create(dt, status, tag)
            except Exception:
                continue

    def toggle_calendar_highlight(self, event):
        """
        Triggered by checkbox; shows/hides calendar events based on the selected row.
        """
        item_id = self.schedule_tree.identify_row(event.y)
        if not item_id:
            return

        current = self.schedule_tree.set(item_id, "highlight")
        new_value = "0" if current == "1" else "1"
        self.schedule_tree.set(item_id, "highlight", new_value)

        self.clear_calendar_tags()
        for row_id in self.schedule_tree.get_children():
            if self.schedule_tree.set(row_id, "highlight") == "1":
                title = self.schedule_tree.item(row_id)['values'][0]
                for schedule in self.config_manager.config.get("schedules", []):
                    if schedule.get("title") == title:
                        self.highlight_schedule_calendar(schedule)
                        break

    def copy_schedule(self, index):
        try:
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            original = sorted_scheds[index]
            copied = original.copy()
            self.config_manager.config['schedules'].append(copied)
            self.log(f"Copied schedule #{index + 1}")
        except Exception as e:
            self.log(f"Failed to copy schedule: {e}")

    def move_schedule_up(self, index):
        if index > 0:
            schedules = self.config_manager.config['schedules']
            schedules[index - 1], schedules[index] = schedules[index], schedules[index - 1]
            self.log(f"Moved schedule {index} up")

    def move_schedule_down(self, index):
        schedules = self.config_manager.config['schedules']
        if index < len(schedules) - 1:
            schedules[index + 1], schedules[index] = schedules[index], schedules[index + 1]
            self.log(f"Moved schedule {index} down")

    def view_schedule_properties(self, index):
        try:
            # Match schedule by sorting
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            if index >= len(sorted_scheds):
                self.log(f"Invalid schedule index: {index}")
                return

            sched = sorted_scheds[index]
            info = json.dumps(sched, indent=4)

            window_number = f"#{index + 1}"
            top = tk.Toplevel(self)
            top.title(f"{window_number} - Schedule Properties")
            top.geometry("400x300")

            text = tk.Text(top, wrap="word")
            text.insert("1.0", info)
            text.config(state="disabled")
            text.pack(fill="both", expand=True)

        except Exception as e:
            self.log(f"Failed to view properties: {e}")

    def on_schedule_double_click(self, event):
        row_id = self.sched_tree.identify_row(event.y)
        if not row_id:
            return

        index = int(row_id)
        try:
            sched = self.config_manager.config['schedules'][index]
            filepath = sched.get("filepath")
            if filepath and os.path.exists(filepath):
                with open(filepath, 'r') as f:
                    self.local_ini_text.delete("1.0", tk.END)
                    self.local_ini_text.insert(tk.END, f.read())

                self.local_path = os.path.dirname(filepath)
                self.local_path_var.set(self.local_path)
                self.refresh_local_files()
                self.local_ini_filename = filepath  # Ensure comparison works later
                self.log(f"Loaded schedule file into viewer: {filepath}")
            else:
                self.log(f"File not found: {filepath}")
        except Exception as e:
            self.log(f"Error loading scheduled file: {e}")

    def on_close(self):
        try:
            self.config_manager.save_config()
            self.config_manager.save_schedules()
            self.log("Configuration and schedules saved on exit.")
        except Exception as e:
            self.log(f"Failed to save on exit: {e}")
        self.destroy()

def deferred_sftp_setup(self):
    safe_debug("Method called: deferred_sftp_setup")

    if not self.sftp_enabled.get():
        safe_debug("SFTP is disabled at launch - skipping initial connection.")
        return

    try:
        sftp = self.get_sftp()
        self.sftp_client = sftp  # reuse this session

        # Cache remote SFTP packet limits if available
        try:
            chan = sftp.client.get_channel()
            if hasattr(chan, "get_remote_max_packet_size"):
                self.max_packet_in = chan.get_remote_max_packet_size()
                self.max_packet_out = chan.get_local_max_packet_size()
                self.log(f"Host SFTP max packet in: {self.max_packet_in} bytes")
                self.log(f"Host SFTP max packet out: {self.max_packet_out} bytes")
        except Exception as e:
            self.log(f"Failed to retrieve SFTP packet limits: {e}")

        try:
            sftp.client.chdir(".")
            base = sftp.client.getcwd()
            target_path = os.path.join(base, "Config", "WindowsServer").replace("\\", "/")
            sftp.client.chdir(target_path)
            self.remote_base = sftp.client.getcwd()
            self.remote_path = self.remote_base
            safe_debug(f"Navigated to remote path: {self.remote_base}")
        except Exception as inner_e:
            logging.warning(f"Failed to navigate to Config/WindowsServer: {inner_e}")
            self.remote_base = "/"
            self.remote_path = "/"

        self.refresh_remote_files(skip_get=True)

    except Exception as e:
        safe_error(f"Initial SFTP connection failed: {e}")
        self.remote_base = "/"
        self.remote_path = "/"


def get_calendar_tag_color(status, bright_mode=False):
    if bright_mode:
        color_map = {
            "failed": "red",
            "success": "green",
            "skipped": "orange",
            "upcoming": "blue",
        }
    else:
        color_map = {
            "failed": "lightpink",
            "success": "lightgreen",
            "skipped": "orange",
            "upcoming": "lightblue",
        }
    return color_map.get(status, "default")

def get_next_run(sched):
    now = datetime.now()
    repeat = str(sched.get("repeat", "none")).lower()
    hour = int(sched.get("hour", 0))
    minute = int(sched.get("minute", 0))
    day_of_week = sched.get("day", "Any")
    day_of_month = sched.get("day_of_month", "Any")
    month = sched.get("month", "Any")

    try:
        for i in range(0, 60):  # Look up to 60 days ahead
            future = now + timedelta(days=i)
            if future.hour != hour or future.minute != minute:
                future = future.replace(hour=hour, minute=minute, second=0, microsecond=0)

            if repeat == "daily":
                return future

            elif repeat == "weekly":
                if calendar.day_name[future.weekday()] == day_of_week:
                    return future

            elif repeat == "monthly":
                if str(future.day) == str(day_of_month):
                    return future

            elif repeat == "none":
                if (month == "Any" or month == calendar.month_name[future.month]) and \
                   (day_of_month == "Any" or str(future.day) == str(day_of_month)) and \
                   (day_of_week == "Any" or calendar.day_name[future.weekday()] == day_of_week):
                    return future

    except Exception:
        pass

    return datetime.max  # if invalid or incomplete

if __name__ == "__main__":
    try:
        app = MainApp()
        app.protocol("WM_DELETE_WINDOW", app.on_close)  # Ensures save on exit
        app.mainloop()
    except Exception as e:
        import traceback
        with open("fatal.log", "w") as f:
            f.write(traceback.format_exc())
        raise


    def toggle_sftp_throttle(self):
        current = self.honor_sftp_limits.get()
        self.config_manager.config["honor_sftp_limits"] = current
        self.config_manager.save_config()
        state = "enabled" if current else "disabled"
        self.log(f"SFTP throttle mode {state}")



def record_schedule_result(self, schedule, result_status):
    """
    Appends a new result to the schedule's run_history and saves the updated config.
    result_status should be one of: 'success', 'failed', 'skipped'
    """
    timestamp = datetime.now().replace(second=0, microsecond=0).isoformat(timespec='minutes')
    if "run_history" not in schedule:
        schedule["run_history"] = {}

    schedule["run_history"][timestamp] = result_status

    # Sync back into schedule_config list if needed
    for sched in self.schedule_config:
        if sched.get("title") == schedule.get("title"):
            sched["run_history"] = schedule["run_history"]
            break

    self.config_manager.save_config()
    self.log(f"Recorded run at {timestamp}: {result_status}")
