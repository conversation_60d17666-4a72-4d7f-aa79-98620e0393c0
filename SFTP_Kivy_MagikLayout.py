"""
SFTP Kivy Application - Exact Magik Layout Recreation
====================================================

Recreating the exact layout structure from the working "magik" file:
- Top control bar with scheduler/SFTP controls
- Dual-pane file browsers (Local | Remote)
- serversettings.ini viewer (dual-pane text editors)
- Schedules section with treeview + calendar
- Log area at bottom
- Status bar

Version: Magik Layout Recreation
"""

import os
import json
import logging
import socket
import threading
import time
import stat
from datetime import datetime
from pathlib import Path

# SFTP imports
try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    logging.warning("Paramiko not available - Install with: pip install paramiko")

# Kivy imports
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.uix.progressbar import ProgressBar
from kivy.uix.filechooser import FileChooserListView
from kivy.uix.splitter import Splitter
from kivy.uix.treeview import TreeView, TreeViewLabel, TreeViewNode
from kivy.clock import Clock
from kivy.graphics import Color, Rectangle, Line
from kivy.metrics import dp
from kivy.core.window import Window

# Set window properties to match original
Window.size = (1400, 910)
Window.title = "V1's Setting Doofer - Kivy Edition"

# ============================================================================
# MAGIK THEME (Professional but readable)
# ============================================================================

THEME = {
    "background": (0.94, 0.94, 0.96, 1),      # Light professional background
    "panel": (0.90, 0.90, 0.92, 1),           # Panel background
    "button": (0.96, 0.96, 0.98, 1),          # Light button background
    "button_active": (0.2, 0.6, 0.2, 1),      # Green for active states
    "button_inactive": (0.8, 0.2, 0.2, 1),    # Red for inactive states
    "button_warning": (0.8, 0.6, 0.1, 1),     # Orange for warnings
    "text": (0.05, 0.05, 0.05, 1),            # Very dark text
    "text_light": (0.3, 0.3, 0.3, 1),         # Medium grey text
    "accent": (0.2, 0.4, 0.7, 1),             # Blue accent
    "success": (0.1, 0.5, 0.1, 1),            # Dark green
    "error": (0.7, 0.1, 0.1, 1),              # Dark red
    "warning": (0.7, 0.5, 0.0, 1),            # Dark orange
    "border": (0.6, 0.6, 0.6, 1),             # Border color
}

# ============================================================================
# MAGIK WIDGETS (Matching original styling)
# ============================================================================

class MagikButton(Button):
    """Button matching the original magik styling"""
    
    def __init__(self, button_type="normal", **kwargs):
        super().__init__(**kwargs)
        self.size_hint_y = None
        self.height = dp(30)  # Standard height from original
        self.font_size = '11sp'
        self.bold = True
        
        if button_type == "active":
            self.background_color = THEME["button_active"]
            self.color = (1, 1, 1, 1)  # White text
        elif button_type == "inactive":
            self.background_color = THEME["button_inactive"]
            self.color = (1, 1, 1, 1)  # White text
        elif button_type == "warning":
            self.background_color = THEME["button_warning"]
            self.color = (1, 1, 1, 1)  # White text
        else:
            self.background_color = THEME["button"]
            self.color = THEME["text"]

class MagikLabel(Label):
    """Label matching the original magik styling"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.color = THEME["text"]
        self.font_size = '11sp'
        self.text_size = (None, None)

class MagikInput(TextInput):
    """Input matching the original magik styling"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint_y = None
        self.height = dp(28)
        self.multiline = False
        self.font_size = '11sp'
        self.foreground_color = THEME["text"]
        self.background_color = (1, 1, 1, 1)  # White background
        self.cursor_color = THEME["accent"]

class MagikTreeView(TreeView):
    """TreeView matching the original magik styling"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint_y = None
        self.height = dp(150)  # Fixed height like original

class MagikTextArea(TextInput):
    """Text area for ini viewer"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.multiline = True
        self.font_size = '10sp'
        self.foreground_color = THEME["text"]
        self.background_color = (1, 1, 1, 1)

# ============================================================================
# SFTP MANAGER (From working magik)
# ============================================================================

class SFTPManager:
    """SFTP manager with real functionality from magik"""
    
    def __init__(self):
        self.transport = None
        self.client = None
        self.connected = False
        self.host = None
        self.port = 22
        self.username = None
        self.password = None
    
    def test_connection(self, host, port, timeout=5):
        """Test basic connectivity"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, int(port)))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def connect(self, host, port, username, password):
        """Connect to SFTP server"""
        try:
            if not self.test_connection(host, port):
                return {"success": False, "message": f"Cannot reach {host}:{port}"}
            
            if not PARAMIKO_AVAILABLE:
                # Simulate connection
                self.connected = True
                self.host = host
                return {"success": True, "message": f"Connected to {host} (simulated)"}
            
            # Real connection
            self.transport = paramiko.Transport((host, int(port)))
            self.transport.connect(username=username, password=password)
            self.client = paramiko.SFTPClient.from_transport(self.transport)
            
            # Test basic operation
            self.client.listdir('.')
            
            self.connected = True
            self.host = host
            self.port = port
            self.username = username
            self.password = password
            
            return {"success": True, "message": f"Connected to {host}"}
            
        except Exception as e:
            self.disconnect()
            return {"success": False, "message": str(e)}
    
    def disconnect(self):
        """Disconnect from server"""
        try:
            if self.client:
                self.client.close()
            if self.transport:
                self.transport.close()
        except Exception:
            pass
        finally:
            self.client = None
            self.transport = None
            self.connected = False
    
    def listdir(self, path="."):
        """List directory contents"""
        if not self.connected:
            return self.simulate_files()
        
        try:
            if PARAMIKO_AVAILABLE and self.client:
                return self.client.listdir_attr(path)
            else:
                return self.simulate_files()
        except Exception:
            return []
    
    def simulate_files(self):
        """Simulate remote files for demo"""
        return [
            type('FileAttr', (), {
                'filename': 'serversettings.ini', 
                'st_mode': stat.S_IFREG | 0o644,
                'st_size': 2048,
                'st_mtime': time.time() - 3600
            })(),
            type('FileAttr', (), {
                'filename': 'config', 
                'st_mode': stat.S_IFDIR | 0o755,
                'st_size': 4096,
                'st_mtime': time.time() - 86400
            })(),
            type('FileAttr', (), {
                'filename': 'backup.zip', 
                'st_mode': stat.S_IFREG | 0o644,
                'st_size': 1048576,
                'st_mtime': time.time() - 7200
            })(),
        ]

# ============================================================================
# MAIN APPLICATION (Exact Magik Layout)
# ============================================================================

class MagikLayoutApp(App):
    """Exact recreation of the magik layout"""
    
    def __init__(self):
        super().__init__()
        self.sftp_manager = SFTPManager()
        self.scheduler_running = True
        self.sftp_enabled = False
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - MAGIK - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sftp_magik_layout.log'),
                logging.StreamHandler()
            ]
        )
        
        logging.info("Magik Layout SFTP App initialized")
    
    def build(self):
        """Build the exact magik layout"""
        Window.clearcolor = THEME["background"]
        
        # Main container
        main_layout = BoxLayout(orientation='vertical', padding=dp(5), spacing=dp(5))
        
        # Top control bar (like original)
        top_bar = self.create_top_bar()
        main_layout.add_widget(top_bar)
        
        # File frames (dual-pane browsers)
        file_frames = self.create_file_frames()
        main_layout.add_widget(file_frames)
        
        # serversettings.ini Viewer
        ini_viewer = self.create_ini_viewer()
        main_layout.add_widget(ini_viewer)
        
        # Schedules section
        schedules_section = self.create_schedules_section()
        main_layout.add_widget(schedules_section)
        
        # Log frame
        log_frame = self.create_log_frame()
        main_layout.add_widget(log_frame)
        
        # Status bar
        status_bar = self.create_status_bar()
        main_layout.add_widget(status_bar)
        
        return main_layout

    def create_top_bar(self):
        """Create top control bar exactly like magik"""
        top_frame = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80))

        with top_frame.canvas.before:
            Color(*THEME["panel"])
            top_frame.rect = Rectangle(pos=top_frame.pos, size=top_frame.size)

        # Lock banner (red warning area)
        self.banner_label = MagikLabel(text="", color=THEME["error"],
                                     size_hint_y=None, height=dp(20))
        top_frame.add_widget(self.banner_label)

        # Control buttons row
        controls_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(35))

        # Left side controls
        self.pause_button = MagikButton(text="Pause Scheduler", button_type="active")
        self.pause_button.bind(on_press=self.toggle_scheduler)
        controls_row.add_widget(self.pause_button)

        self.sftp_toggle_btn = MagikButton(text="Activate SFTP", button_type="inactive")
        self.sftp_toggle_btn.bind(on_press=self.toggle_sftp)
        controls_row.add_widget(self.sftp_toggle_btn)

        # Spacer
        controls_row.add_widget(BoxLayout())

        # Right side controls
        self.remote_toggle_btn = MagikButton(text="Mode: SFTP", size_hint_x=None, width=dp(100))
        self.remote_toggle_btn.bind(on_press=self.toggle_remote_mode)
        controls_row.add_widget(self.remote_toggle_btn)

        load_btn = MagikButton(text="Load Config", size_hint_x=None, width=dp(90))
        load_btn.bind(on_press=self.load_user_config)
        controls_row.add_widget(load_btn)

        save_btn = MagikButton(text="Save Config", size_hint_x=None, width=dp(90))
        save_btn.bind(on_press=self.save_user_config)
        controls_row.add_widget(save_btn)

        top_frame.add_widget(controls_row)

        return top_frame

    def create_file_frames(self):
        """Create dual-pane file browsers exactly like magik"""
        file_frames = BoxLayout(orientation='horizontal', size_hint_y=0.3)

        # Local Directory Frame
        local_frame = BoxLayout(orientation='vertical')

        with local_frame.canvas.before:
            Color(*THEME["panel"])
            local_frame.rect = Rectangle(pos=local_frame.pos, size=local_frame.size)

        local_title = MagikLabel(text="Local Directory", font_size='12sp', bold=True,
                               size_hint_y=None, height=dp(25))
        local_frame.add_widget(local_title)

        # Local path breadcrumb
        self.local_path_var = str(Path.home())
        self.local_breadcrumb = MagikInput(text=self.local_path_var, size_hint_y=None, height=dp(25))
        local_frame.add_widget(self.local_breadcrumb)

        # Up button
        up_btn = MagikButton(text="Up", size_hint_y=None, height=dp(25))
        up_btn.bind(on_press=self.navigate_up)
        local_frame.add_widget(up_btn)

        # Local file tree
        self.local_tree = MagikTreeView()
        self.populate_local_tree()
        local_frame.add_widget(self.local_tree)

        file_frames.add_widget(local_frame)

        # Remote Directory Frame
        remote_frame = BoxLayout(orientation='vertical')

        with remote_frame.canvas.before:
            Color(*THEME["panel"])
            remote_frame.rect = Rectangle(pos=remote_frame.pos, size=remote_frame.size)

        remote_title = MagikLabel(text="Remote Directory", font_size='12sp', bold=True,
                                size_hint_y=None, height=dp(25))
        remote_frame.add_widget(remote_title)

        # Remote breadcrumb
        self.remote_breadcrumb = MagikLabel(text="SFTP is OFF", size_hint_y=None, height=dp(25))
        remote_frame.add_widget(self.remote_breadcrumb)

        # Up button
        remote_up_btn = MagikButton(text="Up", size_hint_y=None, height=dp(25))
        remote_up_btn.bind(on_press=self.navigate_remote_up)
        remote_frame.add_widget(remote_up_btn)

        # Remote file tree
        self.remote_tree = MagikTreeView()
        self.populate_remote_tree()
        remote_frame.add_widget(self.remote_tree)

        # Home button
        home_btn = MagikButton(text="Home", size_hint_y=None, height=dp(25))
        home_btn.bind(on_press=self.navigate_remote_home)
        remote_frame.add_widget(home_btn)

        file_frames.add_widget(remote_frame)

        return file_frames

    def create_ini_viewer(self):
        """Create serversettings.ini viewer exactly like magik"""
        ini_frame = BoxLayout(orientation='vertical', size_hint_y=0.25)

        with ini_frame.canvas.before:
            Color(*THEME["panel"])
            ini_frame.rect = Rectangle(pos=ini_frame.pos, size=ini_frame.size)

        # Title
        ini_title = MagikLabel(text="serversettings.ini Viewer", font_size='12sp', bold=True,
                             size_hint_y=None, height=dp(25))
        ini_frame.add_widget(ini_title)

        # Dual-pane text editors
        ini_content = BoxLayout(orientation='horizontal')

        # Local ini text
        self.local_ini_text = MagikTextArea(text="Local serversettings.ini content will appear here...")
        ini_content.add_widget(self.local_ini_text)

        # Shared scrollbar space (placeholder)
        scrollbar_space = BoxLayout(size_hint_x=None, width=dp(20))
        ini_content.add_widget(scrollbar_space)

        # Remote ini text
        self.remote_ini_text = MagikTextArea(text="Remote serversettings.ini content will appear here...",
                                           readonly=True)
        ini_content.add_widget(self.remote_ini_text)

        ini_frame.add_widget(ini_content)

        # Buttons row
        buttons_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(35))

        save_local_btn = MagikButton(text="Save to Local Directory", size_hint_x=None, width=dp(160))
        save_local_btn.bind(on_press=self.save_ini)
        buttons_row.add_widget(save_local_btn)

        save_as_btn = MagikButton(text="Save As...", size_hint_x=None, width=dp(80))
        save_as_btn.bind(on_press=self.save_ini_as)
        buttons_row.add_widget(save_as_btn)

        compare_btn = MagikButton(text="Compare", size_hint_x=None, width=dp(80))
        compare_btn.bind(on_press=self.compare_ini_files)
        buttons_row.add_widget(compare_btn)

        buttons_row.add_widget(BoxLayout())  # Spacer

        ini_frame.add_widget(buttons_row)

        return ini_frame

    def create_schedules_section(self):
        """Create schedules section exactly like magik"""
        sched_frame = BoxLayout(orientation='vertical', size_hint_y=0.3)

        with sched_frame.canvas.before:
            Color(*THEME["panel"])
            sched_frame.rect = Rectangle(pos=sched_frame.pos, size=sched_frame.size)

        # Title
        sched_title = MagikLabel(text="Schedules", font_size='12sp', bold=True,
                               size_hint_y=None, height=dp(25))
        sched_frame.add_widget(sched_title)

        # Schedule content (left panel + right calendar)
        schedule_content = BoxLayout(orientation='horizontal')

        # Left panel - Schedule list
        left_panel = BoxLayout(orientation='vertical', size_hint_x=0.7)

        # Schedule treeview (like original with columns)
        self.sched_tree = MagikTreeView()
        self.sched_tree.height = dp(120)

        # Add sample schedules
        self.populate_schedule_tree()

        left_panel.add_widget(self.sched_tree)

        # Schedule buttons
        sched_buttons = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(35))

        setup_btn = MagikButton(text="Setup Schedule", size_hint_x=None, width=dp(110))
        setup_btn.bind(on_press=self.setup_schedule)
        sched_buttons.add_widget(setup_btn)

        load_sched_btn = MagikButton(text="Load Schedules", size_hint_x=None, width=dp(110))
        load_sched_btn.bind(on_press=self.load_schedules)
        sched_buttons.add_widget(load_sched_btn)

        save_sched_btn = MagikButton(text="Save Schedules", size_hint_x=None, width=dp(110))
        save_sched_btn.bind(on_press=self.save_schedules)
        sched_buttons.add_widget(save_sched_btn)

        sched_buttons.add_widget(BoxLayout())  # Spacer

        left_panel.add_widget(sched_buttons)
        schedule_content.add_widget(left_panel)

        # Right panel - Calendar
        right_panel = BoxLayout(orientation='vertical', size_hint_x=0.3)

        calendar_title = MagikLabel(text="Calendar", font_size='11sp', bold=True,
                                  size_hint_y=None, height=dp(20))
        right_panel.add_widget(calendar_title)

        # Simple calendar grid
        calendar_area = self.create_calendar_widget()
        right_panel.add_widget(calendar_area)

        schedule_content.add_widget(right_panel)
        sched_frame.add_widget(schedule_content)

        return sched_frame

    def create_calendar_widget(self):
        """Create simple calendar widget like magik"""
        calendar_container = BoxLayout(orientation='vertical')

        # Month header
        month_header = MagikLabel(text="January 2025", font_size='10sp', bold=True,
                                size_hint_y=None, height=dp(20))
        calendar_container.add_widget(month_header)

        # Calendar grid
        calendar_grid = GridLayout(cols=7, size_hint_y=None, height=dp(100))

        # Day headers
        days = ['M', 'T', 'W', 'T', 'F', 'S', 'S']
        for day in days:
            day_label = MagikLabel(text=day, font_size='8sp', size_hint_y=None, height=dp(15))
            calendar_grid.add_widget(day_label)

        # Calendar days
        for i in range(1, 32):
            day_btn = MagikButton(text=str(i), font_size='8sp')
            day_btn.size_hint_y = None
            day_btn.height = dp(12)

            # Highlight today (example)
            if i == 15:  # Simulate today
                day_btn.button_type = "active"
                day_btn.background_color = THEME["button_active"]
                day_btn.color = (1, 1, 1, 1)

            calendar_grid.add_widget(day_btn)

        calendar_container.add_widget(calendar_grid)
        return calendar_container

    def create_log_frame(self):
        """Create log frame exactly like magik"""
        log_frame = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(60))

        with log_frame.canvas.before:
            Color(*THEME["panel"])
            log_frame.rect = Rectangle(pos=log_frame.pos, size=log_frame.size)

        log_title = MagikLabel(text="Log", font_size='11sp', bold=True,
                             size_hint_y=None, height=dp(20))
        log_frame.add_widget(log_title)

        # Log text area (small height like original)
        self.log_text = MagikTextArea(text="Application started...\n", font_size='9sp')
        self.log_text.size_hint_y = None
        self.log_text.height = dp(35)
        log_frame.add_widget(self.log_text)

        return log_frame

    def create_status_bar(self):
        """Create status bar exactly like magik"""
        self.status_var = "Ready"
        self.status_bar = MagikLabel(text=self.status_var, size_hint_y=None, height=dp(20))
        self.status_bar.color = THEME["text_light"]

        with self.status_bar.canvas.before:
            Color(0.8, 0.8, 0.8, 1)  # Light grey background
            self.status_bar.rect = Rectangle(pos=self.status_bar.pos, size=self.status_bar.size)

        return self.status_bar

    # ========================================================================
    # TREE POPULATION METHODS
    # ========================================================================

    def populate_local_tree(self):
        """Populate local file tree"""
        try:
            path = Path(self.local_path_var)
            for item in path.iterdir():
                if item.is_dir():
                    node = TreeViewLabel(text=f"📁 {item.name}")
                else:
                    node = TreeViewLabel(text=f"📄 {item.name}")
                self.local_tree.add_node(node)
        except Exception as e:
            node = TreeViewLabel(text=f"Error: {str(e)}")
            self.local_tree.add_node(node)

    def populate_remote_tree(self):
        """Populate remote file tree"""
        if not self.sftp_enabled:
            node = TreeViewLabel(text="SFTP is OFF")
            self.remote_tree.add_node(node)
            return

        try:
            files = self.sftp_manager.listdir()
            for file_attr in files:
                is_dir = stat.S_ISDIR(file_attr.st_mode)
                icon = "📁" if is_dir else "📄"
                node = TreeViewLabel(text=f"{icon} {file_attr.filename}")
                self.remote_tree.add_node(node)
        except Exception as e:
            node = TreeViewLabel(text=f"Error: {str(e)}")
            self.remote_tree.add_node(node)

    def populate_schedule_tree(self):
        """Populate schedule tree with sample data"""
        sample_schedules = [
            "Daily 09:00 → serversettings.ini upload",
            "Weekly Mon 14:30 → config backup",
            "Monthly 1st 18:00 → full system sync"
        ]

        for schedule in sample_schedules:
            node = TreeViewLabel(text=schedule)
            self.sched_tree.add_node(node)

    # ========================================================================
    # EVENT HANDLERS (Matching magik functionality)
    # ========================================================================

    def log(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.text += log_message
        logging.info(message)

    def update_status(self, message):
        """Update status bar"""
        self.status_var = message
        self.status_bar.text = message

    def toggle_scheduler(self, button):
        """Toggle scheduler like magik"""
        if self.scheduler_running:
            self.scheduler_running = False
            self.pause_button.text = "Resume Scheduler"
            self.pause_button.button_type = "inactive"
            self.pause_button.background_color = THEME["button_inactive"]
            self.pause_button.color = (1, 1, 1, 1)
            self.log("Scheduler paused")
            self.update_status("Scheduler paused")
        else:
            self.scheduler_running = True
            self.pause_button.text = "Pause Scheduler"
            self.pause_button.button_type = "active"
            self.pause_button.background_color = THEME["button_active"]
            self.pause_button.color = (1, 1, 1, 1)
            self.log("Scheduler resumed")
            self.update_status("Scheduler running")

    def toggle_sftp(self, button):
        """Toggle SFTP like magik"""
        if self.sftp_enabled:
            # Disable SFTP
            self.sftp_enabled = False
            self.sftp_toggle_btn.text = "Activate SFTP"
            self.sftp_toggle_btn.button_type = "inactive"
            self.sftp_toggle_btn.background_color = THEME["button_inactive"]
            self.sftp_toggle_btn.color = (1, 1, 1, 1)
            self.remote_breadcrumb.text = "SFTP is OFF"
            self.sftp_manager.disconnect()
            self.log("SFTP Disabled")
            self.update_status("SFTP disabled")

            # Clear remote tree
            self.remote_tree.clear_widgets()
            self.populate_remote_tree()
        else:
            # Enable SFTP
            self.sftp_toggle_btn.text = "Connecting..."
            self.sftp_toggle_btn.button_type = "warning"
            self.sftp_toggle_btn.background_color = THEME["button_warning"]
            self.sftp_toggle_btn.color = (1, 1, 1, 1)
            self.remote_breadcrumb.text = "SFTP: checking..."
            self.update_status("Connecting to SFTP...")

            # Simulate connection attempt
            Clock.schedule_once(self._attempt_sftp_connection, 1)

    def _attempt_sftp_connection(self, dt):
        """Attempt SFTP connection"""
        # Simulate connection with demo credentials
        result = self.sftp_manager.connect("sftp.example.com", 22, "user", "pass")

        if result["success"]:
            self.sftp_enabled = True
            self.sftp_toggle_btn.text = "Disable SFTP"
            self.sftp_toggle_btn.button_type = "active"
            self.sftp_toggle_btn.background_color = THEME["button_active"]
            self.sftp_toggle_btn.color = (1, 1, 1, 1)
            self.remote_breadcrumb.text = "SFTP is ACTIVE"
            self.log("SFTP Enabled")
            self.update_status("SFTP connected")

            # Refresh remote tree
            self.remote_tree.clear_widgets()
            self.populate_remote_tree()
        else:
            self.sftp_enabled = False
            self.sftp_toggle_btn.text = "Activate SFTP"
            self.sftp_toggle_btn.button_type = "inactive"
            self.sftp_toggle_btn.background_color = THEME["button_inactive"]
            self.sftp_toggle_btn.color = (1, 1, 1, 1)
            self.remote_breadcrumb.text = "SFTP FAILED"
            self.log(f"SFTP connect failed: {result['message']}")
            self.update_status("SFTP connection failed")

    def toggle_remote_mode(self, button):
        """Toggle remote mode like magik"""
        current_text = button.text
        if "SFTP" in current_text:
            button.text = "Mode: LAN"
            self.log("Switched to LAN mode")
        else:
            button.text = "Mode: SFTP"
            self.log("Switched to SFTP mode")

    def load_user_config(self, button):
        """Load configuration"""
        self.log("Load config requested")
        self.update_status("Configuration loaded")

    def save_user_config(self, button):
        """Save configuration"""
        self.log("Save config requested")
        self.update_status("Configuration saved")

    def navigate_up(self, button):
        """Navigate up in local directory"""
        try:
            current_path = Path(self.local_breadcrumb.text)
            parent_path = current_path.parent
            if parent_path != current_path:
                self.local_breadcrumb.text = str(parent_path)
                self.local_path_var = str(parent_path)
                self.local_tree.clear_widgets()
                self.populate_local_tree()
                self.log(f"Navigated to: {parent_path}")
        except Exception as e:
            self.log(f"Navigation error: {e}")

    def navigate_remote_up(self, button):
        """Navigate up in remote directory"""
        self.log("Remote navigation up requested")

    def navigate_remote_home(self, button):
        """Navigate to remote home"""
        self.log("Remote home navigation requested")

    def save_ini(self, button):
        """Save ini to local directory"""
        self.log("Save ini to local directory requested")
        self.update_status("INI saved to local directory")

    def save_ini_as(self, button):
        """Save ini as..."""
        self.log("Save ini as... requested")
        self.update_status("INI save as requested")

    def compare_ini_files(self, button):
        """Compare ini files"""
        self.log("INI file comparison requested")
        self.update_status("INI files compared")

    def setup_schedule(self, button):
        """Setup new schedule"""
        self.log("Setup schedule requested")
        self.update_status("Schedule setup requested")

    def load_schedules(self, button):
        """Load schedules from file"""
        self.log("Load schedules requested")
        self.update_status("Schedules loaded")

    def save_schedules(self, button):
        """Save schedules to file"""
        self.log("Save schedules requested")
        self.update_status("Schedules saved")

    def on_stop(self):
        """Clean shutdown"""
        self.sftp_manager.disconnect()
        logging.info("Magik Layout SFTP App shutdown")

# ============================================================================
# APPLICATION ENTRY POINT
# ============================================================================

if __name__ == '__main__':
    app = MagikLayoutApp()
    app.run()
