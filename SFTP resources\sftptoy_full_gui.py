
import os
import shutil
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import paramiko

class SFTPClient:
    def __init__(self):
        self.transport = None
        self.sftp = None

    def connect(self, host, port, username, password):
        self.transport = paramiko.Transport((host, port))
        self.transport.connect(username=username, password=password)
        self.sftp = paramiko.SFTPClient.from_transport(self.transport)

    def listdir(self, path="."):
        return self.sftp.listdir_attr(path)

    def isdir(self, path):
        try:
            return paramiko.S_ISDIR(self.sftp.stat(path).st_mode)
        except IOError:
            return False

    def close(self):
        if self.sftp: self.sftp.close()
        if self.transport: self.transport.close()

    def delete(self, path):
        try:
            if self.isdir(path):
                self.sftp.rmdir(path)
            else:
                self.sftp.remove(path)
        except Exception as e:
            raise e

class Application(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("SFTP Toy")
        self.geometry("1000x600")
        self.sftp_client = SFTPClient()
        self.current_dir = os.getcwd()
        self.create_widgets()

    def create_widgets(self):
        # Top input frame
        input_frame = ttk.Frame(self)
        input_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(input_frame, text="Host:").grid(row=0, column=0)
        self.host_entry = ttk.Entry(input_frame)
        self.host_entry.grid(row=0, column=1, padx=5)

        ttk.Label(input_frame, text="Port:").grid(row=0, column=2)
        self.port_entry = ttk.Entry(input_frame)
        self.port_entry.insert(0, "22")
        self.port_entry.grid(row=0, column=3, padx=5)

        ttk.Label(input_frame, text="User:").grid(row=0, column=4)
        self.user_entry = ttk.Entry(input_frame)
        self.user_entry.grid(row=0, column=5, padx=5)

        ttk.Label(input_frame, text="Pass:").grid(row=0, column=6)
        self.pass_entry = ttk.Entry(input_frame, show="*")
        self.pass_entry.grid(row=0, column=7, padx=5)

        # Button frame
        button_frame = ttk.Frame(self)
        button_frame.pack(fill="x", padx=10, pady=5)
        ttk.Button(button_frame, text="Connect SFTP", command=self.connect_sftp).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Refresh Local", command=self.refresh_local).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Test Upload", command=self.test_upload).pack(side="left", padx=5)

        # Folder views
        views_frame = ttk.Frame(self)
        views_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Local tree
        self.local_tree = ttk.Treeview(views_frame)
        self.local_tree.heading("#0", text="Local Files", anchor="w")
        self.local_tree.pack(side="left", fill="both", expand=True)
        self.local_tree.bind("<Button-3>", self.show_context_menu_local)
        self.populate_local_tree()

        # Remote tree
        self.remote_tree = ttk.Treeview(views_frame)
        self.remote_tree.heading("#0", text="Remote SFTP", anchor="w")
        self.remote_tree.pack(side="left", fill="both", expand=True)
        self.remote_tree.bind("<Button-3>", self.show_context_menu_remote)

        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Delete", command=self.delete_selected)

    def populate_local_tree(self, path=None, parent=""):
        if path is None:
            path = self.current_dir
            self.local_tree.delete(*self.local_tree.get_children())
        for entry in os.listdir(path):
            abs_path = os.path.join(path, entry)
            node = self.local_tree.insert(parent, "end", text=entry, open=False)
            if os.path.isdir(abs_path):
                self.local_tree.insert(node, "end")

    def refresh_local(self):
        self.populate_local_tree()

    def connect_sftp(self):
        host = self.host_entry.get()
        port = int(self.port_entry.get())
        user = self.user_entry.get()
        passwd = self.pass_entry.get()
        try:
            self.sftp_client.connect(host, port, user, passwd)
            self.populate_remote_tree()
        except Exception as e:
            messagebox.showerror("SFTP Connection Failed", str(e))

    def populate_remote_tree(self, path="."):
        self.remote_tree.delete(*self.remote_tree.get_children())
        try:
            for attr in self.sftp_client.listdir(path):
                self.remote_tree.insert("", "end", text=attr.filename)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to list directory:
{e}")

    def show_context_menu_local(self, event):
        try:
            item = self.local_tree.identify_row(event.y)
            if item:
                self.local_tree.selection_set(item)
                self.context_menu.post(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def show_context_menu_remote(self, event):
        try:
            item = self.remote_tree.identify_row(event.y)
            if item:
                self.remote_tree.selection_set(item)
                self.context_menu.post(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def delete_selected(self):
        selection = self.local_tree.selection()
        for item in selection:
            path = os.path.join(self.current_dir, self.local_tree.item(item, "text"))
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete {path}?"):
                try:
                    if os.path.isdir(path):
                        shutil.rmtree(path)
                    else:
                        os.remove(path)
                    self.local_tree.delete(item)
                except Exception as e:
                    messagebox.showerror("Error", str(e))

    def test_upload(self):
        messagebox.showinfo("Test", "Upload logic not implemented yet.")

if __name__ == "__main__":
    app = Application()
    app.mainloop()
