
import os
import tempfile
import tkinter as tk
from tkinter import ttk, messagebox
import paramiko

class SettingsManagerApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("SFTP Folder Manager")
        self.geometry("900x600")

        self.host_entry = ttk.Entry(self)
        self.port_entry = ttk.Entry(self)
        self.username_entry = ttk.Entry(self)
        self.password_entry = ttk.Entry(self, show="*")

        self.sftp_connected = False
        self.sftp = None
        self.transport = None

        self.clipboard_path = None

        self.create_widgets()

    def create_widgets(self):
        top_frame = ttk.Frame(self)
        top_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(top_frame, text="Host:").grid(row=0, column=0, sticky="w")
        self.host_entry.grid(row=0, column=1, sticky="ew")
        self.host_entry.insert(0, "localhost")

        ttk.Label(top_frame, text="Port:").grid(row=0, column=2, sticky="w")
        self.port_entry.grid(row=0, column=3, sticky="ew")
        self.port_entry.insert(0, "22")

        ttk.Label(top_frame, text="Username:").grid(row=1, column=0, sticky="w")
        self.username_entry.grid(row=1, column=1, sticky="ew")
        self.username_entry.insert(0, "user")

        ttk.Label(top_frame, text="Password:").grid(row=1, column=2, sticky="w")
        self.password_entry.grid(row=1, column=3, sticky="ew")

        connect_btn = ttk.Button(top_frame, text="Connect SFTP", command=self.open_sftp_frame)
        connect_btn.grid(row=2, column=0, columnspan=4, sticky="ew", pady=10)

        for i in range(4):
            top_frame.columnconfigure(i, weight=1)

        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

    def open_sftp_frame(self):
        self.sftp_frame = SFTPFrame(self, self.host_entry, self.port_entry, self.username_entry, self.password_entry)
        self.sftp_frame.pack(fill="both", expand=True)

class SFTPFrame(ttk.Frame):
    def __init__(self, parent, host_entry, port_entry, username_entry, password_entry):
        super().__init__(parent)
        self.parent = parent
        self.sftp_connected = False
        self.sftp = None
        self.transport = None

        frame = ttk.Frame(self)
        frame.pack(fill="both", expand=True)

        creds_frame = ttk.Frame(frame)
        creds_frame.pack(fill="x", pady=5)

        ttk.Label(creds_frame, text="Host:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.host_entry = ttk.Entry(creds_frame)
        self.host_entry.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        self.host_entry.insert(0, host_entry.get())

        ttk.Label(creds_frame, text="Port:").grid(row=0, column=2, sticky="w", padx=5, pady=5)
        self.port_entry = ttk.Entry(creds_frame)
        self.port_entry.grid(row=0, column=3, sticky="ew", padx=5, pady=5)
        self.port_entry.insert(0, port_entry.get())

        ttk.Label(creds_frame, text="Username:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.username_entry = ttk.Entry(creds_frame)
        self.username_entry.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        self.username_entry.insert(0, username_entry.get())

        ttk.Label(creds_frame, text="Password:").grid(row=1, column=2, sticky="w", padx=5, pady=5)
        self.password_entry = ttk.Entry(creds_frame, show="*")
        self.password_entry.grid(row=1, column=3, sticky="w", padx=5, pady=5)
        self.password_entry.insert(0, password_entry.get())

        creds_frame.columnconfigure(1, weight=1)

        self.connect_button = ttk.Button(frame, text="Connect", command=self.connect_sftp)
        self.connect_button.pack(pady=5)

        self.tree = ttk.Treeview(frame)
        self.tree.pack(fill="both", expand=True)
        self.tree.bind("<Button-3>", self.on_right_click)

        self.status_label = ttk.Label(frame, text="Status: Not connected")
        self.status_label.pack(fill="x", pady=5)

        self.clipboard_path = None

    def connect_sftp(self):
        host = self.host_entry.get()
        port = int(self.port_entry.get())
        username = self.username_entry.get()
        password = self.password_entry.get()

        self.status_label.config(text="Status: Connecting...")
        self.update()

        try:
            transport = paramiko.Transport((host, port))
            transport.connect(username=username, password=password)
            self.sftp = paramiko.SFTPClient.from_transport(transport)
            self.transport = transport
            self.status_label.config(text="Status: Connected")
            self.sftp_connected = True
            self.populate_remote_tree("/")
            self.connect_button.config(state="disabled")
        except Exception as e:
            self.status_label.config(text=f"Status: Connection failed: {e}")
            self.sftp_connected = False

    def populate_remote_tree(self, path, parent=""):
        if not self.sftp_connected:
            return
        self.tree.delete(*self.tree.get_children())
        self._populate_remote_tree(path, parent)

    def _populate_remote_tree(self, path, parent):
        try:
            files = self.sftp.listdir_attr(path)
            for f in files:
                name = f.filename
                mode = f.st_mode
                is_dir = paramiko.S_ISDIR(mode)
                node = self.tree.insert(parent, "end", text=name, open=False)
                full_path = path.rstrip("/") + "/" + name
                self.tree.set(node, "fullpath", full_path)
                if is_dir:
                    self.tree.insert(node, "end", text="dummy")
        except Exception as e:
            print(f"Error listing remote dir: {e}")

    def on_right_click(self, event):
        item = self.tree.identify_row(event.y)
        if not item:
            return
        self.tree.selection_set(item)
        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Copy", command=self.remote_copy)
        menu.add_command(label="Paste", command=self.remote_paste)
        menu.add_command(label="Delete", command=self.remote_delete)
        menu.tk_popup(event.x_root, event.y_root)

    def remote_copy(self):
        item = self.tree.selection()
        if not item:
            return
        item = item[0]
        node_text = self.tree.item(item, "text")
        parent = self.tree.parent(item)
        if parent == "":
            self.clipboard_path = "/" + node_text
        else:
            parent_path = self.get_remote_full_path(parent)
            self.clipboard_path = parent_path.rstrip("/") + "/" + node_text
        self.status_label.config(text=f"Copied {self.clipboard_path}")

    def remote_paste(self):
        if not self.clipboard_path:
            self.status_label.config(text="Clipboard empty")
            return
        item = self.tree.selection()
        if not item:
            self.status_label.config(text="No target selected")
            return
        item = item[0]
        target_path = self.get_remote_full_path(item)
        try:
            if not self.is_dir(target_path):
                self.status_label.config(text="Target must be a directory")
                return
        except Exception:
            self.status_label.config(text="Target must be a directory")
            return

        src_path = self.clipboard_path
        filename = os.path.basename(src_path)
        dest_path = target_path.rstrip("/") + "/" + filename

        try:
            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                self.sftp.get(src_path, tmp_file.name)
                self.sftp.put(tmp_file.name, dest_path)
            self.status_label.config(text=f"Pasted to {dest_path}")
            self.populate_remote_tree("/")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to paste file: {e}")

    def remote_delete(self):
        item = self.tree.selection()
        if not item:
            return
        item = item[0]
        path = self.get_remote_full_path(item)
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{path}'?"):
            try:
                if self.is_dir(path):
                    self.sftp.rmdir(path)
                else:
                    self.sftp.remove(path)
                self.status_label.config(text=f"Deleted {path}")
                self.populate_remote_tree("/")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete: {e}")

    def is_dir(self, path):
        try:
            attr = self.sftp.stat(path)
            import stat
            return stat.S_ISDIR(attr.st_mode)
        except Exception:
            return False

    def get_remote_full_path(self, item):
        parts = []
        while item:
            parts.insert(0, self.tree.item(item, "text"))
            item = self.tree.parent(item)
        path = "/" + "/".join(parts)
        return path


if __name__ == "__main__":
    app = SettingsManagerApp()
    app.mainloop()
