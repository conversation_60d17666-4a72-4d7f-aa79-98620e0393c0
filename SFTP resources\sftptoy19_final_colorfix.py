import tkinter as tk
import traceback
from tkinter import ttk, messagebox, filedialog, simpledialog, Menu
from PIL import Image, ImageTk
from moviepy.editor import VideoFileClip
from moviepy.video.io.bindings import PIL_to_npimage
from moviepy.video.io.bindings import mplfig_to_npimage
from moviepy.video.io.ImageSequenceClip import <PERSON><PERSON><PERSON><PERSON>Clip
from io import BytesIO
import numpy as np
import random
import glob
import os
import json
import threading
import time
from datetime import datetime, timedelta
import calendar
import shutil
import paramiko
import logging
import smtplib
from email.mime.text import MIMEText
from cryptography.fernet import Fernet
import stat
from PIL import Image as PILImage
import pystray
import sys

try:
    from PIL import Image
    RESAMPLE_METHOD = Image.Resampling.LANCZOS
except AttributeError:
    RESAMPLE_METHOD = Image.ANTIALIAS

CONFIG_FILE = "sftp_config.json"
KEY_FILE = "sftp_key.key"
EMAIL_CONFIG_FILE = "email_config.json"
SCHEDULE_CONFIG_FILE = "schedule_config.json"
NOTIFICATION_CONFIG_FILE = "notification_settings.json"
LOCK_EMAIL_COOLDOWN_SECONDS = 900
notification_config = {"log_enabled": True}


if os.path.exists(NOTIFICATION_CONFIG_FILE):
    try:
        with open(NOTIFICATION_CONFIG_FILE, 'r') as f:
            notification_config.update(json.load(f))
    except Exception as e:
        pass 

# Setup logging
if notification_config.get("log_enabled", True):
    logging.basicConfig(
        filename="v1s_setting_doofer.log",
        level=logging.DEBUG,
        format="%(asctime)s - %(levelname)s - %(message)s"
    )

def ensure_lock_config_in_sftp_config():
    if not os.path.exists(CONFIG_FILE):
        return

    try:
        with open(CONFIG_FILE, "r") as f:
            config = json.load(f)
    except Exception as e:
        safe_error(f"Failed to read {CONFIG_FILE}: {e}")
        return

    if "app_lock" not in config:
        config["app_lock"] = {
            "enabled": False,
            "password": ""
        }
        try:
            with open(CONFIG_FILE, "w") as f:
                json.dump(config, f, indent=4)
            safe_log("Inserted app_lock section into sftp_config.json")
        except Exception as e:
            safe_error(f"Failed to write updated config: {e}")

def ensure_default_files_exist():
    defaults = {
        CONFIG_FILE: {
            "sftp": {},
            "schedules": [],
            "clipboard": None,
            "enable_dir_copy": True
        },
        SCHEDULE_CONFIG_FILE: [],
        EMAIL_CONFIG_FILE: {
            "smtp_server": "",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "from": "",
            "to": [],
            "upload_failed": True,
            "upload_success": False,
            "scheduler_paused": True,
            "scheduler_pause_threshold": 600
        },
        NOTIFICATION_CONFIG_FILE: {
            "log_enabled": True,
            "email_enabled": False
        }
    }

    for path, content in defaults.items():
        if not os.path.exists(path):
            try:
                with open(path, 'w') as f:
                    json.dump(content, f, indent=4)
                safe_log(f"Created default config: {path}")
            except Exception as e:
                safe_error(f"Failed to create default config for {path}: {e}")



# Encryption
if not os.path.exists(KEY_FILE):
    key = Fernet.generate_key()
    with open(KEY_FILE, 'wb') as f:
        f.write(key)
with open(KEY_FILE, 'rb') as f:
    fernet = Fernet(f.read())

# Notification helper
notification_config = {"log_enabled": True, "email_enabled": False}
if os.path.exists(NOTIFICATION_CONFIG_FILE):
    try:
        with open(NOTIFICATION_CONFIG_FILE, "r") as f:
            notification_config = json.load(f)
    except:
        pass

    def load_email_config():
        try:
            if os.path.exists(EMAIL_CONFIG_FILE):
                with open(EMAIL_CONFIG_FILE, 'r') as f:
                    return json.load(f)
        except Exception as e:
            safe_error(f"Failed to load email config: {e}")
        return {}

def notify(message):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted = f"{timestamp} - {message}"

    if notification_config.get("log_enabled"):
        safe_log(message)

    if notification_config.get("email_enabled"):
        send_email("V1 Notification", formatted)

def safe_log(message):
    if notification_config.get("log_enabled", True):
        logging.info(message)

def safe_error(message):
    if notification_config.get("log_enabled", True):
        logging.error(message)

def safe_debug(message):
    if notification_config.get("log_enabled", True):
        logging.debug(message)

# Call early during startup
ensure_default_files_exist()

class NotificationSettingsDialog(tk.Toplevel):
    def __init__(self, master):
        super().__init__(master)
        self.title("Notification Settings")
        self.geometry("300x150")

        self.log_var = tk.BooleanVar(value=notification_config.get("log_enabled", True))
        self.email_var = tk.BooleanVar(value=notification_config.get("email_enabled", False))

        ttk.Checkbutton(self, text="Enable Logging", variable=self.log_var).pack(anchor="w", padx=10, pady=5)
        ttk.Checkbutton(self, text="Enable Email Notifications", variable=self.email_var).pack(anchor="w", padx=10, pady=5)
        ttk.Button(self, text="Save", command=self.save).pack(pady=10)

    def save(self):
        notification_config["log_enabled"] = self.log_var.get()
        notification_config["email_enabled"] = self.email_var.get()
        with open(NOTIFICATION_CONFIG_FILE, "w") as f:
            json.dump(notification_config, f, indent=2)
        self.destroy()

def load_app_lock_config():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r") as f:
                config = json.load(f)
                return config.get("app_lock", {})
    except Exception as e:
        safe_error(f"Failed to load app lock from main config: {e}")
    return {}


def save_app_lock_config(lock_data):
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r") as f:
                config = json.load(f)
        else:
            config = {}

        config["app_lock"] = lock_data

        with open(CONFIG_FILE, "w") as f:
            json.dump(config, f, indent=4)
        safe_log("App lock config saved to main config.")
    except Exception as e:
        safe_error(f"Failed to save app lock to main config: {e}")

class PathLabel(ttk.Frame):
    def __init__(self, parent, path_var, refresh_callback, master=None, remote=False):
        super().__init__(parent)
        self.path_var = path_var
        self.refresh_callback = refresh_callback
        self.master = master  # Usually the MainApp instance
        self.remote = remote

        self.label = ttk.Label(self, textvariable=self.path_var, anchor="w")
        self.label.pack(fill=tk.X, expand=True)

        self.label.bind("<Button-1>", self.go_up)
        self.label.bind("<Button-3>", self.show_context_menu)

    def go_up(self, event=None):
        current = self.path_var.get()
        parent = os.path.dirname(current)

        if parent == current:
            drives = self.get_drives()
            if drives:
                selected = self.select_drive(drives)
                if selected:
                    self.path_var.set(selected)
                    if self.master:
                        self.master.local_path = selected
        else:
            self.path_var.set(parent)
            if self.master:
                self.master.local_path = parent

        if not self.remote or (self.master and getattr(self.master, "sftp_enabled", None) and self.master.sftp_enabled.get()):
            self.refresh_callback()

    def show_context_menu(self, event):
        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Copy Path", command=self.copy_path)
        menu.tk_popup(event.x_root, event.y_root)

    def copy_path(self):
        try:
            self.clipboard_clear()
            self.clipboard_append(self.path_var.get())
            self.update()
            messagebox.showinfo("Copied", f"Path copied to clipboard:\n{self.path_var.get()}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy path: {e}")

    def get_drives(self):
        if os.name == 'nt':
            import string
            return [f"{d}:/" for d in string.ascii_uppercase if os.path.exists(f"{d}:/")]
        else:
            return ['/']

    def select_drive(self, drives):
        win = tk.Toplevel(self)
        win.title("Select Drive")
        var = tk.StringVar()

        for d in drives:
            ttk.Radiobutton(win, text=d, value=d, variable=var).pack(anchor="w")

        ttk.Button(win, text="OK", command=win.destroy).pack(pady=5)

        win.grab_set()
        self.wait_window(win)

        return var.get()

def send_email(subject, body):
    if not os.path.exists(EMAIL_CONFIG_FILE):
        safe_error("Email config file not found.")
        messagebox.showerror("Email Error", "Email config file not found.")
        return

    try:
        with open(EMAIL_CONFIG_FILE, 'r') as f:
            config = json.load(f)

        required_keys = ['smtp_server', 'smtp_port', 'username', 'password', 'from', 'to']
        if not all(k in config for k in required_keys):
            safe_error("Incomplete email configuration.")
            messagebox.showerror("Email Error", "Incomplete email configuration.")
            return

        msg = MIMEText(body)
        msg['Subject'] = subject
        msg['From'] = config['from']
        msg['To'] = ", ".join(config['to']) if isinstance(config['to'], list) else config['to']

        try:
            with smtplib.SMTP(config['smtp_server'], config['smtp_port']) as server:
                server.starttls()
                server.login(config['username'], config['password'])
                server.sendmail(config['from'], config['to'], msg.as_string())
            safe_log("Email sent successfully.")
        except Exception as smtp_error:
            safe_error(f"Failed to send email: {smtp_error}")
            messagebox.showerror("Email Error", f"Failed to send email: {smtp_error}")

    except Exception as e:
        safe_error(f"Error reading email configuration: {e}")
        messagebox.showerror("Email Error", f"Error reading email configuration: {e}")

class SetPasswordDialog(tk.Toplevel):
    def __init__(self, master):
        super().__init__(master)
        self.title("Set App Lock Password")
        self.geometry("300x150")
        self.app = master  # Reference to MainApp

        ttk.Label(self, text="Enter Password:").pack(pady=(10, 0))
        self.pw1_var = tk.StringVar()
        ttk.Entry(self, textvariable=self.pw1_var, show="*").pack()

        ttk.Label(self, text="Confirm Password:").pack(pady=(10, 0))
        self.pw2_var = tk.StringVar()
        ttk.Entry(self, textvariable=self.pw2_var, show="*").pack()

        ttk.Button(self, text="Set Password", command=self.save_password).pack(pady=10)

    def save_password(self):
        pw1 = self.pw1_var.get()
        pw2 = self.pw2_var.get()

        if not pw1:
            messagebox.showerror("Error", "Password cannot be empty.")
            return

        if pw1 != pw2:
            messagebox.showerror("Error", "Passwords do not match.")
            return

        try:
            encrypted = fernet.encrypt(pw1.encode()).decode()
            # Save into the expected nested config structure
            self.app.config_manager.config.setdefault('app_lock', {})['password'] = encrypted
            self.app.config_manager.save_config()  # Persist change immediately
            messagebox.showinfo("Saved", "Password set successfully.")
            self.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to set password: {e}")

class LockScreen(tk.Toplevel):
    def __init__(self, master, fernet):
        super().__init__(master)
        self.title("Application Locked")
        self.geometry("500x300")
        self.attributes('-topmost', True)
        self.grab_set()
        self.resizable(False, False)
        self.protocol("WM_DELETE_WINDOW", lambda: None)
        self.after(1000, self.check_minimize_to_tray)
        self.cooldown_active = False
        self.fernet = fernet

        self.bg_frame = tk.Frame(self)
        self.bg_frame.pack(fill="both", expand=True)

        self.bg_label = tk.Label(self.bg_frame)
        self.bg_label.place(x=0, y=0, relwidth=1, relheight=1)

        self.set_background_media()
        self.build_overlay()

    def set_background_media(self):
        supported_images = (".png", ".jpg", ".jpeg", ".bmp")
        supported_gifs = (".gif",)

        candidates = [f for f in os.listdir('.') if f.lower().startswith("lock_image") and f.lower().endswith(supported_images + supported_gifs)]
        if not candidates:
            return

        selected = random.choice(candidates)
        try:
            if selected.lower().endswith(supported_gifs):
                self.play_gif(selected)
            else:
                img = Image.open(selected)
                img = img.resize((500, 300), Image.Resampling.LANCZOS)
                self.bg_img = ImageTk.PhotoImage(img)
                self.bg_label.config(image=self.bg_img)
        except Exception as e:
            safe_error(f"Failed to load background media: {e}")

    def play_gif(self, path):
        def loop():
            try:
                img = Image.open(path)
                frames = []
                try:
                    while True:
                        frame = img.copy()
                        frames.append(ImageTk.PhotoImage(frame.resize((500, 300), Image.Resampling.LANCZOS)))
                        img.seek(len(frames))
                except EOFError:
                    pass

                while self.winfo_exists():
                    for frame in frames:
                        if not self.winfo_exists():
                            return
                        self.bg_label.config(image=frame)
                        self.bg_label.image = frame
                        time.sleep(0.1)
            except Exception as e:
                safe_error(f"Failed to play gif {path}: {e}")

        threading.Thread(target=loop, daemon=True).start()


    def minimize_to_tray(self):
        self.withdraw()
        image = PILImage.new('RGB', (64, 64), color='gray')
        def on_restore(icon, item):
            self.tray_icon_shown = False
            icon.stop()
            self.deiconify()
            self.lift()
            self.attributes('-topmost', True)
            self.after(300, lambda: self.attributes('-topmost', False))
        def on_quit(icon, item):
            icon.stop()
            self.destroy()
            sys.exit()
        menu = pystray.Menu(
            pystray.MenuItem('Restore', on_restore),
            pystray.MenuItem('Exit', on_quit)
        )
        self.tray_icon = pystray.Icon('V1 Lock', image, 'App Locked', menu)
        threading.Thread(target=self.tray_icon.run, daemon=True).start()

    def check_minimize_to_tray(self):
        if self.state() == 'iconic' and not hasattr(self, 'tray_icon_shown') and self.master.minimize_to_tray_var.get():
            self.tray_icon_shown = True
            self.minimize_to_tray()
        self.after(1000, self.check_minimize_to_tray)

    def build_overlay(self):
        font_opts = ("Segoe UI", 10)

        self.label = ttk.Label(self.bg_frame, text="Enter Password to Unlock:", font=font_opts)
        self.label.place(relx=0.5, rely=0.2, anchor="center")

        self.password_var = tk.StringVar()
        self.entry = ttk.Entry(self.bg_frame, textvariable=self.password_var, show="*", font=font_opts)
        self.entry.place(relx=0.5, rely=0.35, anchor="center", width=200)
        self.entry.focus()

        self.unlock_btn = ttk.Button(self.bg_frame, text="Unlock", command=self.attempt_unlock)
        self.unlock_btn.place(relx=0.5, rely=0.5, anchor="center")

        self.forgot_btn = ttk.Button(self.bg_frame, text="Forgot Password", command=self.send_unlock_email)
        self.forgot_btn.place(relx=0.5, rely=0.65, anchor="center")

    def attempt_unlock(self):
        entered = self.password_var.get()
        try:
            with open(CONFIG_FILE) as f:
                config = json.load(f)
            encrypted_pw = config.get("app_lock", {}).get("password", "")

            if not encrypted_pw:
                messagebox.showinfo("Unlocked", "No password is set.")
                self.grab_release()
                self.destroy()
                return

            decrypted_pw = self.fernet.decrypt(encrypted_pw.encode()).decode()

            if entered == decrypted_pw:
                self.grab_release()
                self.destroy()
            else:
                messagebox.showerror("Error", "Incorrect password.")
        except Exception as e:
            messagebox.showerror("Error", f"Password check failed: {e}")

    def send_unlock_email(self):
        if self.cooldown_active:
            messagebox.showinfo("Cooldown", "Please wait before requesting another email.")
            return

        try:
            with open(CONFIG_FILE) as f:
                cfg = json.load(f)
            encrypted_pw = cfg.get("app_lock", {}).get("password")

            if not encrypted_pw:
                messagebox.showerror("Error", "No password is set.")
                return

            decrypted_pw = self.fernet.decrypt(encrypted_pw.encode()).decode()

            with open(EMAIL_CONFIG_FILE) as f:
                email_cfg = json.load(f)

            recipient = email_cfg.get("to")
            if isinstance(recipient, list):
                recipient = recipient[0] if recipient else None

            if not recipient:
                messagebox.showerror("Error", "No recipient configured in email settings.")
                return

            body = f"You requested your application unlock password.\n\nPassword: {decrypted_pw}"
            msg = MIMEText(body)
            msg["Subject"] = "Unlock Password Reminder"
            msg["From"] = email_cfg["from"]
            msg["To"] = recipient

            with smtplib.SMTP(email_cfg["smtp_server"], email_cfg["smtp_port"]) as server:
                server.starttls()
                server.login(email_cfg["username"], email_cfg["password"])
                server.sendmail(email_cfg["from"], recipient, msg.as_string())

            messagebox.showinfo("Sent", "Reminder email sent.")
            self.start_cooldown()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to send email: {e}")

    def start_cooldown(self):
        self.cooldown_active = True
        self.forgot_btn.config(state="disabled")
        threading.Thread(target=self._cooldown_timer, daemon=True).start()

    def _cooldown_timer(self):
        time.sleep(LOCK_EMAIL_COOLDOWN_SECONDS)
        if self.forgot_btn.winfo_exists():
            self.forgot_btn.config(state="normal")
        self.cooldown_active = False

class SFTPManager:
    def __init__(self, host, port, username, password):
        self.transport = None
        self.client = None
        self.home_path = ""
        self.remote_path = ""
        self.connected = False
        try:
            safe_debug("Creating Transport-based SFTP connection")
            self.transport = paramiko.Transport((host, int(port)))
            safe_debug(f"Connecting to {host}:{port} as {username}")
            self.transport.connect(username=username, password=password)
            self.client = paramiko.SFTPClient.from_transport(self.transport)

            self.client.chdir(".")
            self.home_path = self.client.getcwd()
            self.remote_path = self.home_path
            self.client.listdir(self.home_path)

            self.connected = True
            safe_log("Authentication (Transport-based) successful")
        except Exception as e:
            self.connected = False
            safe_error(f"SFTP connection failed: {e}")
            raise

    def listdir(self, path=None):
        if not self.connected:
            raise RuntimeError("SFTP not connected")
        target_path = path if path else self.home_path
        return self.client.listdir_attr(target_path)

    def upload(self, local_path, remote_path):
        self.client.put(local_path, remote_path)

    def download(self, remote_path, local_path):
        self.client.get(remote_path, local_path)

    def remove(self, remote_path):
        self.client.remove(remote_path)

    def upload_throttled(self, local_path, remote_path, max_chunk_size=32768):
        with open(local_path, "rb") as f:
            with self.client.file(remote_path, "wb") as remote:
                remote.set_pipelined(True)
                while True:
                    chunk = f.read(max_chunk_size)
                    if not chunk:
                        break
                    remote.write(chunk)
    def download_throttled(self, remote_path, local_path, max_chunk_size=32768):
        with self.client.file(remote_path, "rb") as remote:
            with open(local_path, "wb") as f:
                while True:
                    chunk = remote.read(max_chunk_size)
                    if not chunk:
                        break
                    f.write(chunk)
    def close(self):
        if self.client:
            self.client.close()
        if self.transport:
            self.transport.close()

class EmailSettingsDialog(tk.Toplevel):
    def __init__(self, master):
        super().__init__(master)
        self.title("Email Settings")
        self.geometry("500x500")
        self.entries = {}
        self.to_entries = []

        try:
            if os.path.exists(EMAIL_CONFIG_FILE):
                with open(EMAIL_CONFIG_FILE, 'r') as f:
                    self.email_config = load_email_config()
            else:
                self.email_config = {}
        except Exception as e:
            safe_error(f"Failed to read email config: {e}")
            self.email_config = {}

        self.event_vars = {
            "upload_failed": tk.BooleanVar(value=self.email_config.get("upload_failed", True)),
            "upload_success": tk.BooleanVar(value=self.email_config.get("upload_success", False)),
            "scheduler_paused": tk.BooleanVar(value=self.email_config.get("scheduler_paused", True)),
            "upload_skipped": tk.BooleanVar(value=self.email_config.get("upload_skipped", True))
        }

        self.create_widgets()
        self.load_settings()

    def create_widgets(self):
        frame = ttk.Frame(self)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.settings_frame = frame

        labels = ["SMTP Server", "SMTP Port", "Username", "Password", "From"]
        for i, label in enumerate(labels):
            ttk.Label(frame, text=label).grid(row=i, column=0, sticky="e")
            entry = ttk.Entry(frame, show="*" if label == "Password" else None)
            entry.grid(row=i, column=1, sticky="ew")
            self.entries[label.lower().replace(" ", "_")] = entry

        ttk.Label(frame, text="To").grid(row=5, column=0, sticky="ne")
        to_frame = ttk.Frame(frame)
        to_frame.grid(row=5, column=1, sticky="ew")
        for i in range(5):
            entry = ttk.Entry(to_frame)
            entry.pack(fill=tk.X, pady=1)
            self.to_entries.append(entry)

        ttk.Label(frame, text="Email Events").grid(row=6, column=0, sticky="nw")
        event_frame = ttk.Frame(frame)
        event_frame.grid(row=6, column=1, sticky="ew")
        ttk.Checkbutton(event_frame, text="On Upload Failed", variable=self.event_vars["upload_failed"]).pack(anchor="w")
        ttk.Checkbutton(event_frame, text="On Upload Success", variable=self.event_vars["upload_success"]).pack(anchor="w")
        ttk.Checkbutton(event_frame, text="On Upload Skipped", variable=self.event_vars["upload_skipped"]).pack(anchor="w")
        ttk.Checkbutton(event_frame, text="If Scheduler Paused Too Long", variable=self.event_vars["scheduler_paused"]).pack(anchor="w")
        ttk.Label(frame, text="Pause Alert Threshold (secs)").grid(row=7, column=0, sticky="e")
        self.entries['scheduler_pause_threshold'] = tk.StringVar()
        ttk.Entry(frame, textvariable=self.entries['scheduler_pause_threshold']).grid(row=7, column=1, sticky="ew")

        frame.columnconfigure(1, weight=1)
        ttk.Button(self, text="Save", command=self.save_settings).pack(pady=10)

    def load_settings(self):
        if os.path.exists(EMAIL_CONFIG_FILE):
            try:
                with open(EMAIL_CONFIG_FILE, 'r') as f:
                    config = json.load(f)

                for key, entry in self.entries.items():
                    if isinstance(entry, tk.StringVar):
                        entry.set(config.get(key, ""))
                    else:
                        entry.delete(0, tk.END)
                        entry.insert(0, config.get(key, ""))

                if isinstance(config.get('to'), list):
                    for i, email in enumerate(config['to'][:5]):
                        self.to_entries[i].insert(0, email)

                for key in self.event_vars:
                    self.event_vars[key].set(config.get(key, self.event_vars[key].get()))

                threshold = config.get('scheduler_pause_threshold', 600)
                self.entries['scheduler_pause_threshold'].set(str(threshold))
            except Exception as e:
                safe_error(f"Failed to load email settings: {e}")

    def save_settings(self):
        config = {k: v.get() for k, v in self.entries.items()}
        config['scheduler_pause_threshold'] = int(self.entries.get("scheduler_pause_threshold", tk.StringVar()).get())
        config['upload_failed'] = self.event_vars['upload_failed'].get()
        config['upload_success'] = self.event_vars['upload_success'].get()
        config['upload_skipped'] = self.event_vars['upload_skipped'].get()
        config['scheduler_paused'] = self.event_vars['scheduler_paused'].get()
        to_emails = [e.get().strip() for e in self.to_entries if e.get().strip() != ""]
        config['to'] = to_emails
        for key, var in self.event_vars.items():
            config[key] = var.get()
        try:
            with open(EMAIL_CONFIG_FILE, 'w') as f:
                json.dump(config, f, indent=4)
            messagebox.showinfo("Saved", "Email settings saved successfully.")
            self.destroy()
        except Exception as e:
            messagebox.showerror("Error", str(e))

class SFTPSettingsDialog(tk.Toplevel):
    def __init__(self, master, config_manager):
        super().__init__(master)
        self.title("SFTP Settings")
        self.geometry("400x250")
        self.config_manager = config_manager
        self.entries = {}
        self.create_widgets()
        self.load_settings()

    def create_widgets(self):
        fields = ["Host", "Port", "Username", "Password"]
        defaults = ["example.com", "22", "user", ""]
        for i, (label, default) in enumerate(zip(fields, defaults)):
            ttk.Label(self, text=label).grid(row=i, column=0, sticky="e", padx=5, pady=5)
            entry = ttk.Entry(self, show="*" if label == "Password" else None)
            entry.grid(row=i, column=1, sticky="ew", padx=5, pady=5)
            self.entries[label.lower()] = entry
            entry.insert(0, default)
        self.columnconfigure(1, weight=1)
        ttk.Button(self, text="Save", command=self.save_settings).grid(row=len(fields), column=1, sticky="e", pady=10)

    def save_settings(self):
        sftp_info = {
            "host": self.entries["host"].get().strip(),
            "port": int(self.entries["port"].get().strip()),
            "username": self.entries["username"].get().strip(),
            "password": self.entries["password"].get().strip(),
        }
        self.config_manager.config['sftp'] = sftp_info
        self.config_manager.save_config()
        self.destroy()
        messagebox.showinfo("Saved", "SFTP settings saved successfully.")

    def load_settings(self):
        sftp = self.config_manager.config.get("sftp", {})
        for key, entry in self.entries.items():
            if key in sftp:
                entry.delete(0, tk.END)
                entry.insert(0, str(sftp[key]))

class ConfigManager:
    def __init__(self):
        self.config = {
            "sftp": {},
            "schedules": [],
            "clipboard": None,
            "enable_dir_copy": True
        }
        self.schedule_file = SCHEDULE_CONFIG_FILE
        self.schedules = []
        self.load_config_file(CONFIG_FILE)
        self.load_schedules()

    def load_config_file(self, path):
        try:
            with open(path, 'r') as f:
                config_data = json.load(f)

            if 'sftp' in config_data and 'password' in config_data['sftp']:
                encrypted_pw = config_data['sftp']['password']
                try:
                    config_data['sftp']['password'] = fernet.decrypt(encrypted_pw.encode()).decode()
                except Exception as e:
                    safe_error(f"Password decryption failed: {e}")
                    config_data['sftp']['password'] = ""

            self.config.update(config_data)
            safe_log(f"Loaded configuration from {path}")
        except Exception as e:
            safe_error(f"Failed to load configuration from {path}: {e}")

    def load_schedules(self):
        if os.path.exists(self.schedule_file):
            try:
                with open(self.schedule_file, 'r') as f:
                    self.config['schedules'] = json.load(f)
                self.refresh_schedules()
                self.update_calendar_colors()  # Ensure calendar reflects loaded history
                self.log("Schedules loaded from file.")
            except Exception as e:
                safe_error(f"Failed to load schedules: {e}")

    def save_schedules(self):
        try:
            with open(self.schedule_file, 'w') as f:
                json.dump(self.config['schedules'], f, indent=4)
            safe_log("Schedules saved.")
        except Exception as e:
            safe_error(f"Failed to save schedules: {e}")

    def save_config(self):
        cfg = self.config.copy()
        plain_password = cfg['sftp'].get('password', '')

        if plain_password:
            try:
                cfg['sftp']['password'] = fernet.encrypt(plain_password.encode('utf-8')).decode('utf-8')
            except Exception as e:
                safe_error(f"Password encryption failed: {e}")
                cfg['sftp']['password'] = ""

        with open(CONFIG_FILE, 'w') as f:
            json.dump(cfg, f, indent=4)

        self.config['sftp']['password'] = plain_password

    def load_config_and_refresh(self):
        try:
            file_path = filedialog.askopenfilename(
                title="Load Config File",
                filetypes=[("JSON Files", "*.json")]
            )
            if not file_path:
                return

            with open(file_path, 'r') as f:
                loaded = json.load(f)

            if 'password' in loaded.get('sftp', {}):
                try:
                    loaded['sftp']['password'] = fernet.decrypt(loaded['sftp']['password'].encode()).decode('utf-8')
                except Exception as e:
                    safe_error(f"Decryption failed: {e}")
                    loaded['sftp']['password'] = ""

            self.config = loaded
            safe_log(f"Configuration loaded from {file_path}")
        except Exception as e:
            safe_error(f"Failed to load configuration: {e}")


class Scheduler:
    def __init__(self, app):
        self.app = app
        self.running = True
        self.last_run = datetime.now()
        self.pause_time = None
        self.pause_alert_sent = False
        self.last_schedule_run_times = {}

        threading.Thread(target=self.monitor_scheduler_pause, daemon=True).start()
        threading.Thread(target=self.run, daemon=True).start()
        safe_log("Scheduler thread started.")

    def pause(self):
        self.running = False
        self.pause_time = datetime.now()
        # Scheduler pause log centralized in toggle_scheduler

    def resume(self):
        if not self.running:
            self.running = True
            self.pause_time = None
            self.pause_alert_sent = False
        # Scheduler resume log centralized in toggle_scheduler

    def run(self):
        while True:
            if not self.running:
                time.sleep(1)
                continue

            now = datetime.now()
            for sched in self.app.config_manager.config['schedules']:
                schedule_id = json.dumps(sched, sort_keys=True)
                last_run = self.last_schedule_run_times.get(schedule_id)

                if self.should_run_schedule(sched, now):
                    if not last_run or last_run.minute != now.minute or (now - last_run).total_seconds() >= 60:

                        if not sched.get("enabled", True):
                            run_key = now.strftime("%Y-%m-%dT%H:%M")
                            sched.setdefault("run_history", {})[run_key] = "skipped"
                            self.app.config_manager.save_config()

                            if sched.get("send_email", True) and self.get_email_setting("upload_skipped"):
                                send_email("Upload Skipped", f"{sched['filepath']} was skipped at {now} (disabled)")
                            continue

                        self.last_schedule_run_times[schedule_id] = now
                        filepath = sched['filepath']
                        try:
                            remote_path = sched.get('remote_path', f"/upload/{os.path.basename(filepath)}")
                            sftp = self.app.get_sftp()
                            sftp.upload(filepath, remote_path)
                            self.app.log(f"Uploaded {filepath} to {remote_path}")
                          # sftp.close()

                            run_key = now.strftime("%Y-%m-%dT%H:%M")
                            sched.setdefault("run_history", {})[run_key] = "success"
                            self.app.config_manager.save_config()

                            if sched.get("send_email", True) and self.get_email_setting("upload_success"):
                                send_email("Upload Success", f"{filepath} uploaded to {remote_path} at {now}")

                        except Exception as e:
                            error_message = f"Upload failed for {filepath} to {remote_path}: {e}"
                            self.app.log(error_message)

                            run_key = now.strftime("%Y-%m-%dT%H:%M")
                            sched.setdefault("run_history", {})[run_key] = "failed"
                            self.app.config_manager.save_config()

                            if sched.get("send_email", True) and self.get_email_setting("upload_failed"):
                                send_email("Upload Failed", error_message)

            self.app.last_scheduler_check = time.time()
            self.last_run = datetime.now()
            self.pause_alert_sent = False
            time.sleep(60)

    def should_run_schedule(self, schedule, now):
        def matches(value, target):
            return str(value).strip().lower() == "any" or str(value) == str(target)

        repeat_raw = schedule.get("repeat", "none")
        repeat = str(repeat_raw).strip().lower()

        if repeat == "Daily":
            return now.hour == int(schedule["hour"]) and now.minute == int(schedule["minute"])
        elif repeat == "Weekly":
            return now.strftime('%A') == schedule.get("day") and now.hour == int(schedule["hour"]) and now.minute == int(schedule["minute"])
        elif repeat == "Monthly":
            return now.day == int(schedule.get("day_of_month", 0)) and now.hour == int(schedule["hour"]) and now.minute == int(schedule["minute"])
        elif repeat == "Hourly":
            return now.minute == int(schedule["minute"])
        elif repeat == "none":
            if not matches(schedule.get("month", "Any"), calendar.month_name[now.month]):
                return False
            if not matches(schedule.get("day_of_month", "Any"), str(now.day)):
                return False
            if not matches(schedule.get("day", "Any"), now.strftime('%A')):
                return False
            return now.hour == int(schedule["hour"]) and now.minute == int(schedule["minute"])

        return False

    def get_email_setting(self, key):
        try:
            with open(EMAIL_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                return config.get(key, False)
        except:
            return False

    def monitor_scheduler_pause(self):
        safe_debug("Pause monitor thread started.")
        while True:
            try:
                with open(EMAIL_CONFIG_FILE, 'r') as f:
                    config = json.load(f)
            except Exception as e:
                logging.warning(f"Failed to load email config: {e}")
                time.sleep(60)
                continue

            pause_threshold = config.get("scheduler_pause_threshold", 600)
            send_alerts = config.get("scheduler_paused", True)
            now = datetime.now()

            if not self.running and self.pause_time:
                delta = (now - self.pause_time).total_seconds()
                safe_debug(f"[PauseCheck] Paused for {int(delta)}s (threshold {pause_threshold}s)")

                if delta > pause_threshold:
                    if send_alerts and not self.pause_alert_sent:
                        send_email("Scheduler Paused", f"Scheduler has been paused for {int(delta)} seconds.")
                        safe_log(f"Scheduler pause alert sent (delta {int(delta)}s)")
                        self.pause_alert_sent = True
                else:
                    self.pause_alert_sent = False
            else:
                self.pause_alert_sent = False
            time.sleep(60)

class ScheduleList(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent)
        self.app = app
        self.listbox = tk.Listbox(self)
        self.listbox.pack(fill=tk.BOTH, expand=True)
        self.listbox.bind("<Button-3>", self.show_context_menu)
        self.menu = tk.Menu(self, tearoff=0)
        self.menu.add_command(label="Edit", command=self.edit_schedule)
        self.refresh()

    def refresh(self):
        self.listbox.delete(0, tk.END)
        for sched in self.app.config_manager.config['schedules']:
            desc = f"{sched['day']} {sched.get('day_of_month', '')} {sched.get('month', '')} at {sched['hour']:02}:{sched['minute']:02} -> {os.path.basename(sched['filepath'])}"
            self.listbox.insert(tk.END, desc)

    def show_context_menu(self, event):
        try:
            self.listbox.selection_clear(0, tk.END)
            index = self.listbox.nearest(event.y)
            self.listbox.selection_set(index)
            self.menu.post(event.x_root, event.y_root)
        except:
            pass

    def edit_schedule(self):
        index = self.listbox.curselection()
        if index:
            index = index[0]
            sched = self.app.config_manager.config['schedules'][index]
            new_day = simpledialog.askstring("Edit Day", "Day:", initialvalue=sched['day'])
            new_dom = simpledialog.askstring("Edit Day of Month", "Day of Month:", initialvalue=sched.get('day_of_month', 'Any'))
            new_month = simpledialog.askstring("Edit Month", "Month:", initialvalue=sched.get('month', 'Any'))
            new_hour = simpledialog.askinteger("Edit Hour", "Hour:", initialvalue=sched['hour'])
            new_min = simpledialog.askinteger("Edit Minute", "Minute:", initialvalue=sched['minute'])
            sched.update({
                'day': new_day,
                'day_of_month': new_dom,
                'month': new_month,
                'hour': new_hour,
                'minute': new_min
            })
            self.refresh()
            self.app.config_manager.save_config()

    def delegate_edit(self):
        index = self.listbox.curselection()
        if index:
            self.app.edit_schedule(index[0])

class ScheduleCalendar(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent)
        self.app = app
        self.today = datetime.today()
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.day_buttons = {}
        self.pinned_day = None
        self.open_props = {}
        self.build_calendar()

    def build_calendar(self):
        for widget in self.winfo_children():
            widget.destroy()

        self.day_buttons.clear()
        today = datetime.today()
        is_current_month = (self.current_month == today.month and self.current_year == today.year)

        header = ttk.Frame(self)
        header.pack(fill=tk.X, pady=2)
        ttk.Button(header, text="<", width=2, command=self.prev_month).pack(side=tk.LEFT)
        ttk.Label(header, text=f"{calendar.month_name[self.current_month]} {self.current_year}").pack(side=tk.LEFT, expand=True)
        ttk.Button(header, text=">", width=2, command=self.next_month).pack(side=tk.RIGHT)

        days_frame = ttk.Frame(self)
        days_frame.pack()
        for day in ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]:
            ttk.Label(days_frame, text=day, width=6, anchor="center").pack(side=tk.LEFT, padx=1, pady=1)

        month_frame = ttk.Frame(self)
        month_frame.pack()
        status_by_day = self.get_status_by_day()

        for week in calendar.Calendar(firstweekday=0).monthdayscalendar(self.current_year, self.current_month):
            row = ttk.Frame(month_frame)
            row.pack()
            for day in week:
                if day == 0:
                    ttk.Label(row, text="", width=6).pack(side=tk.LEFT, padx=1, pady=1)
                else:
                    btn = ttk.Button(row, text=str(day), width=5)
                    btn.pack(side=tk.LEFT, padx=1, pady=1)

                    status = status_by_day.get(day)
                    bright = self.app.bright_colors_var.get()
                    style_name = f"{get_calendar_tag_color(status, bright_mode=bright)}.TButton"
                    btn.configure(style=style_name)

                    if is_current_month and day == today.day:
                        btn.configure(style="today.TButton")

                    self.day_buttons[day] = btn
                    btn.bind("<Enter>", lambda e, d=day: self.on_hover_day(d))
                    btn.bind("<Leave>", lambda e, d=day: self.on_leave_day(d))
                    btn.bind("<Button-1>", lambda e, d=day: self.select_day(d))

    def select_day(self, day):
        if self.pinned_day == day:
            # Deselect
            self.pinned_day = None
            self.clear_schedule_highlight()

            # Close property windows for this day
            to_close = []
            for idx, sched in enumerate(self.app.config_manager.config['schedules']):
                if self.does_schedule_match_day(sched, day):
                    to_close.append(idx)

            for idx in to_close:
                win = self.open_props.pop(idx, None)
                if win and win.winfo_exists():
                    win.destroy()
            return

        # Select
        self.pinned_day = day
        self.highlight_day(day)

        base_x = self.app.winfo_rootx() + 50
        base_y = self.app.winfo_rooty() + 50
        offset = 30

        matches = []
        for idx, sched in enumerate(self.app.config_manager.config['schedules']):
            if self.does_schedule_match_day(sched, day):
                matches.append(idx)

        for i, idx in enumerate(matches):
            if idx in self.open_props and self.open_props[idx].winfo_exists():
                continue  # Already open

            self.app.view_schedule_properties(idx)
            win = self.app.winfo_children()[-1]
            win.geometry(f"+{base_x + offset*i}+{base_y + offset*i}")
            self.open_props[idx] = win

            # Bring to front
            win.lift()
            win.attributes('-topmost', True)
            win.after(300, lambda w=win: w.attributes('-topmost', False))

            def make_on_close(w, index):
                def _on_close():
                    if index in self.open_props:
                        del self.open_props[index]
                    w.destroy()

                    # Re-lift others
                    for other in self.open_props.values():
                        if other.winfo_exists():
                            other.lift()
                            other.attributes('-topmost', True)
                            other.after(300, lambda win=other: win.attributes('-topmost', False))
                return _on_close

            win.protocol("WM_DELETE_WINDOW", make_on_close(win, idx))


    def does_schedule_match_day(self, sched, day):
        repeat = str(sched.get("repeat", "none")).lower()
        if repeat == "daily":
            return True
        elif repeat == "weekly":
            sched_dow = sched.get("day", "Any")
            return sched_dow == calendar.day_name[datetime(self.current_year, self.current_month, day).weekday()]
        elif repeat == "monthly":
            return str(day) == sched.get("day_of_month")
        elif repeat == "none":
            return (sched.get("day_of_month") == str(day) and
                    sched.get("month") == calendar.month_name[self.current_month])
        return False

    def get_matching_schedule_indices(self, day):
        matches = []
        for idx, sched in enumerate(self.app.config_manager.config['schedules']):
            if self.does_schedule_match_day(sched, day):
                matches.append(idx)
        return matches

    def get_status_by_day(self):
        status_map = {}
        now = datetime.now()
        status_priority = {"failed": 3, "skipped": 2, "success": 1}

        for sched in self.app.config_manager.config['schedules']:
            if not sched.get("enabled", True):
                continue  # Skip disabled schedules

            history = sched.get("run_history", {})

            # 1. Add confirmed results from history (success, failed, skipped)
            for key, result in history.items():
                try:
                    dt = datetime.strptime(key, "%Y-%m-%dT%H:%M")
                    if dt.year == self.current_year and dt.month == self.current_month:
                        existing = status_map.get(dt.day)
                        if not existing or status_priority.get(result, 0) > status_priority.get(existing, 0):
                            status_map[dt.day] = result
                except:
                    continue

        # 2. Predict upcoming scheduled runs (only if no history and enabled)
        for sched in self.app.config_manager.config['schedules']:
            if not sched.get("enabled", True):
                continue

            try:
                hour = int(sched.get("hour", 0))
                minute = int(sched.get("minute", 0))
                repeat = str(sched.get("repeat", "none")).lower()
                dom_raw = sched.get("day_of_month", "Any")
                dow_raw = sched.get("day", "Any")
                month_raw = sched.get("month", "Any")

                for day in range(1, calendar.monthrange(self.current_year, self.current_month)[1] + 1):
                    dt = datetime(self.current_year, self.current_month, day, hour, minute)
                    if dt <= now:
                        continue  # Only consider future runs for prediction

                    match = False
                    if repeat == "daily":
                        match = True
                    elif repeat == "weekly" and calendar.day_name[dt.weekday()] == dow_raw:
                        match = True
                    elif repeat == "monthly" and str(day) == dom_raw:
                        match = True
                    elif repeat == "none":
                        if month_raw != "Any" and calendar.month_name[self.current_month] != month_raw:
                            continue
                        if dom_raw != "Any" and str(day) != dom_raw:
                            continue
                        if dow_raw != "Any" and calendar.day_name[dt.weekday()] != dow_raw:
                            continue
                        match = True

                    if match and day not in status_map:
                        status_map[day] = "upcoming"

            except:
                continue

        return status_map

    def on_hover_day(self, day):
        if self.pinned_day is None:
            self.highlight_day(day)

    def on_leave_day(self, day):
        if self.pinned_day is None:
            self.clear_schedule_highlight()

    def highlight_day(self, day):
        matches = []
        for idx, sched in enumerate(self.app.config_manager.config['schedules']):
            if self.does_schedule_match_day(sched, day):
                matches.append(idx)

        self.app.sched_tree.selection_remove(*self.app.sched_tree.selection())

        for idx in matches:
            # Get sorted list and find position of this unsorted idx
            sorted_scheds = sorted(self.app.config_manager.config['schedules'], key=get_next_run)
            sched = self.app.config_manager.config['schedules'][idx]
            try:
                sorted_index = sorted_scheds.index(sched)
                self.app.sched_tree.selection_add(str(sorted_index))
            except ValueError:
                continue

    def clear_schedule_highlight(self):
        self.app.sched_tree.selection_remove(*self.app.sched_tree.selection())

    def prev_month(self):
        self.current_month -= 1
        if self.current_month < 1:
            self.current_month = 12
            self.current_year -= 1
        self.build_calendar()

    def next_month(self):
        self.current_month += 1
        if self.current_month > 12:
            self.current_month = 1
            self.current_year += 1
        self.build_calendar()



class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("V1's Setting Doofer v0.7")
        self.geometry("1400x910")

        self.config_manager = ConfigManager()
        self.honor_sftp_limits = tk.BooleanVar(value=self.config_manager.config.get("honor_sftp_limits", True))
        self.sftp_enabled = tk.BooleanVar(value=self.config_manager.config.get("sftp_enabled", True))
        self.enable_dir_copy_var = tk.BooleanVar(value=self.config_manager.config.get("enable_dir_copy", True))
        self.minimize_to_tray_var = tk.BooleanVar(value=True)
        paused = self.config_manager.config.get("scheduler_paused", False)
        self.scheduler_running = tk.BooleanVar(value=not paused)
        self.scheduler = Scheduler(self)
        if paused:
            self.scheduler.pause()
        if paused:
            self.scheduler.pause()
        self.sftp_client = None
        self.local_path = os.getcwd()
        self.remote_path = None
        self.remote_base = None
        self.pause_time = None
        self.bright_colors_var = tk.BooleanVar(value=False)
        self.create_menu_bar()
        self.create_widgets()
        self.refresh_local_files()
        self.refresh_schedules()
        self.last_scheduler_check = time.time()
        self.sched_tree.bind("<Double-Button-1>", self.on_schedule_double_click)
        self.remote_tree.bind("<Button-3>", self.remote_right_click)

        self.after(100, self.setup_styles)
        self.after(500, lambda: deferred_sftp_setup(self))

    def setup_styles(self):
        style = ttk.Style()
        style.configure("red.TButton", background="red")
        style.configure("green.TButton", background="green")
        style.configure("blue.TButton", background="blue")
        style.configure("lightpink.TButton", background="lightpink")
        style.configure("lightgreen.TButton", background="lightgreen")
        style.configure("orange.TButton", background="orange")
        style.configure("lightblue.TButton", background="lightblue")
        style.configure("default.TButton", background="SystemButtonFace")
        style.configure("today.TButton", background="#b0c4de")
        style.configure("sftpOn.TButton", background="lightgreen")
        style.configure("sftpOff.TButton", background="red")
        style.map("sftpOn.TButton", background=[("active", "lightgreen")])
        style.map("sftpOff.TButton", background=[("active", "red")])
        style.map("today.TButton", background=[("active", "#b0c4de"), ("!disabled", "#b0c4de")])


    def create_menu_bar(self):
        menubar = tk.Menu(self)
        self.config(menu=menubar)

        # === Settings Menu ===
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="Email Settings", command=lambda: EmailSettingsDialog(self))
        settings_menu.add_command(label="SFTP Settings", command=lambda: SFTPSettingsDialog(self, self.config_manager))
        settings_menu.add_separator()
        settings_menu.add_checkbutton(
            label="Minimize to Tray",
            variable=self.minimize_to_tray_var
        )

        # === Safety Menu ===
        safety_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Safety", menu=safety_menu)

        self.safety_menu_var = tk.BooleanVar(value=notification_config.get("log_enabled", True))
        safety_menu.add_checkbutton(
            label="Enable Logging",
            variable=self.safety_menu_var,
            command=self.toggle_logging
        )

        safety_menu.add_checkbutton(
            label="Enable Directory Copy",
            variable=self.enable_dir_copy_var,
            command=self.toggle_dir_copy
        )

        safety_menu.add_checkbutton(
            label="Honor SFTP Host Transfer Limits",
            variable=self.honor_sftp_limits,
            command=self.toggle_sftp_throttle
        )

        safety_menu.add_command(label="Lock App", command=lambda: LockScreen(self, fernet))
        safety_menu.add_separator()
        safety_menu.add_command(label="Set Lock Password", command=lambda: SetPasswordDialog(self))

        # === View Menu ===
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)

        self.bright_colors_var = tk.BooleanVar(value=False)
        self.night_mode_var = tk.BooleanVar(value=False)
        self.lock_image_var = tk.BooleanVar(value=False)

        view_menu.add_checkbutton(
            label="Bright Calendar Colors",
            variable=self.bright_colors_var,
            command=self.toggle_bright_colors
        )
        view_menu.add_checkbutton(
            label="Night Mode",
            variable=self.night_mode_var,
            command=lambda: self.log("Night Mode toggled (future feature)")
        )
        view_menu.add_checkbutton(
            label="Lock Screen Image",
            variable=self.lock_image_var,
            command=lambda: self.log("Lock screen image toggle (future feature)")
        )
        view_menu.add_command(
            label="Manual",
            command=lambda: messagebox.showinfo("Manual", "User manual placeholder")
        )
        view_menu.add_command(
            label="Credits",
            command=lambda: messagebox.showinfo("Credits", "Created by V1nceTD")
        )

        # === Modules Menu ===
        modules_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Modules", menu=modules_menu)
        modules_menu.add_command(
            label="Launch Python Module...",
            command=lambda: messagebox.showinfo("Modules", "Custom module launcher placeholder")
        )
        modules_menu.add_command(
            label="Run External EXE...",
            command=lambda: messagebox.showinfo("Modules", "EXE runner placeholder")
        )

    def toggle_bright_colors(self):
        """Toggle between bright and default calendar colors and refresh the calendar."""
        state = "ON" if self.bright_colors_var.get() else "OFF"
        self.log(f"Bright Calendar Colours toggled: {state}")
        self.refresh_calendar_events()



    def refresh_calendar_events(self):
        if not hasattr(self, "schedule_calendar"):
            return
        self.schedule_calendar.build_calendar()
        bright = self.bright_colors_var.get()
        for sched in self.schedule_data:
            status = sched.get("status", "upcoming")
            date_str = sched.get("date")
            if not date_str:
                continue
            try:
                date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
                color = get_calendar_tag_color(status, bright_mode=bright)
                self.calendar.tag_day(date_obj, color)
            except Exception as e:
                self.log(f"Failed to tag calendar day: {e}")


    def setup_schedule(self, existing=None, index=None, title_suffix=None):
        sched_win = tk.Toplevel(self)
        window_title = f"Schedule Setup {title_suffix}" if title_suffix else "Schedule Setup"
        sched_win.title(window_title)
        sched_win.geometry("390x470")
        sched_win.attributes('-topmost', True)
        sched_win.transient(self)

        title_var = tk.StringVar(value=existing.get("title", "") if existing else "")
        ttk.Label(sched_win, text="Title (optional):").pack()
        ttk.Entry(sched_win, textvariable=title_var).pack()

        day_var = tk.StringVar(value=existing.get("day", "Any") if existing else "Any")
        hour_var = tk.StringVar(value=str(existing.get("hour", 0)) if existing else "0")
        minute_var = tk.StringVar(value=str(existing.get("minute", 0)) if existing else "0")
        file_var = tk.StringVar(value=existing.get("filepath", "") if existing else "")
        remote_var = tk.StringVar(value=existing.get("remote_path", "/upload/serversettings.ini") if existing else "/upload/serversettings.ini")
        dom_var = tk.StringVar(value=existing.get("day_of_month", "Any") if existing else "Any")
        month_var = tk.StringVar(value=existing.get("month", "Any") if existing else "Any")
        repeat_var = tk.StringVar(value=existing.get("repeat", "none") if existing else "none")
        email_var = tk.BooleanVar(value=existing.get("send_email", True) if existing else True)

        ttk.Label(sched_win, text="Day of Week:").pack()
        ttk.Combobox(sched_win, textvariable=day_var, values=["Any"] + list(calendar.day_name)).pack()

        ttk.Label(sched_win, text="Month:").pack()
        ttk.Combobox(sched_win, textvariable=month_var, values=["Any"] + list(calendar.month_name)[1:]).pack()

        ttk.Label(sched_win, text="Day of Month:").pack()
        ttk.Combobox(sched_win, textvariable=dom_var, values=["Any"] + [str(i) for i in range(1, 32)]).pack()

        ttk.Label(sched_win, text="Hour:").pack()
        ttk.Combobox(sched_win, textvariable=hour_var, values=[str(i) for i in range(24)]).pack()

        ttk.Label(sched_win, text="Minute:").pack()
        ttk.Combobox(sched_win, textvariable=minute_var, values=[str(i) for i in range(60)]).pack()

        ttk.Label(sched_win, text="Repeat:").pack()
        ttk.Combobox(sched_win, textvariable=repeat_var, values=["none", "daily", "weekly", "monthly"]).pack()

        ttk.Label(sched_win, text="Local File:").pack()
        ttk.Entry(sched_win, textvariable=file_var).pack()
        ttk.Button(sched_win, text="Browse", command=lambda: file_var.set(filedialog.askopenfilename(parent=sched_win))).pack()

        ttk.Label(sched_win, text="Remote Path:").pack()
        ttk.Entry(sched_win, textvariable=remote_var).pack()

        ttk.Checkbutton(sched_win, text="Send Email Notification", variable=email_var).pack(pady=5)

        def save():
            try:
                schedule = {
                    "title": title_var.get(),
                    "day": day_var.get(),
                    "hour": int(hour_var.get()),
                    "minute": int(minute_var.get()),
                    "filepath": file_var.get(),
                    "remote_path": remote_var.get(),
                    "day_of_month": dom_var.get(),
                    "month": month_var.get(),
                    "repeat": repeat_var.get(),
                    "send_email": email_var.get()
                }
                if index is not None:
                    self.config_manager.config['schedules'][index] = schedule
                else:
                    self.config_manager.config['schedules'].append(schedule)
                self.refresh_schedules()
                self.config_manager.save_config()
                sched_win.destroy()
            except Exception as e:
                messagebox.showerror("Error", str(e))

        ttk.Button(sched_win, text="Save Schedule", command=save).pack(pady=5)

    def create_widgets(self):
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")

        top_frame = ttk.Frame(self)
        top_frame.pack(fill=tk.X)

        ttk.Button(top_frame, text="Setup Schedule", command=self.setup_schedule).pack(side=tk.LEFT, padx=5)
        self.pause_button = ttk.Button(top_frame, text="Pause Scheduler", command=self.toggle_scheduler)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        self.sftp_toggle_btn = ttk.Button(top_frame, text="Disable SFTP", command=self.toggle_sftp)
        self.sftp_toggle_btn.pack(side=tk.LEFT, padx=5)
        ttk.Button(top_frame, text="Save Config", command=self.config_manager.save_config).pack(side=tk.RIGHT, padx=5)
        ttk.Button(top_frame, text="Load Config", command=self.load_config_and_refresh).pack(side=tk.RIGHT, padx=5)

        file_frames = ttk.Frame(self)
        file_frames.pack(fill=tk.BOTH, expand=True)

        # Local Directory
        self.dir_frame = ttk.LabelFrame(file_frames, text="Local Directory")
        self.dir_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        local_inner = ttk.Frame(self.dir_frame)
        local_inner.pack(fill=tk.BOTH, expand=True)

        self.local_path_var = tk.StringVar(value=self.local_path)
        self.local_breadcrumb = PathLabel(local_inner, self.local_path_var, self.refresh_local_files, master=self)
        self.local_breadcrumb.pack(fill=tk.X)
        ttk.Button(local_inner, text="Up", command=self.navigate_up).pack(fill=tk.X)

        self.local_tree = ttk.Treeview(local_inner, columns=("Type",), show="tree headings", height=6)
        self.local_tree.heading("#0", text="Name")
        self.local_tree.heading("Type", text="Type")
        self.local_tree.pack(fill=tk.BOTH, expand=True)
        self.local_tree.bind("<Double-1>", self.on_local_double_click)
        self.local_tree.bind("<Button-3>", self.local_right_click)

        # Remote Directory
        self.remote_frame = ttk.LabelFrame(file_frames, text="Remote Directory")
        self.remote_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        remote_inner = ttk.Frame(self.remote_frame)
        remote_inner.pack(fill=tk.BOTH, expand=True)

        self.remote_breadcrumb = ttk.Label(remote_inner, text=self.remote_path)
        self.remote_breadcrumb.pack(fill=tk.X)
        self.remote_breadcrumb.bind("<Button-1>", self.navigate_remote_up)
        ttk.Button(remote_inner, text="Up", command=self.navigate_remote_up).pack(fill=tk.X)

        self.remote_tree = ttk.Treeview(remote_inner, columns=("Type",), show="tree headings", height=6)
        self.remote_tree.heading("#0", text="Name")
        self.remote_tree.heading("Type", text="Type")
        self.remote_tree.column("#0", anchor="w")
        self.remote_tree.column("Type", anchor="w")
        self.remote_tree.pack(fill=tk.BOTH, expand=True)
        self.remote_tree.bind("<Double-1>", self.on_remote_double_click)
        self.remote_tree.bind("<Button-3>", self.remote_right_click)


        # serversettings.ini Viewer Frame
        self.ini_frame = ttk.LabelFrame(self, text="serversettings.ini Viewer")
        self.ini_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        ini_inner = ttk.Frame(self.ini_frame)
        ini_inner.pack(fill=tk.BOTH, expand=True)

        # Local INI Text (editable)
        self.local_ini_text = tk.Text(ini_inner, height=10, undo=True, maxundo=-1)
        self.local_ini_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))

        # Shared Scrollbar in the middle
        shared_scroll = tk.Scrollbar(ini_inner, orient="vertical")
        shared_scroll.pack(side=tk.LEFT, fill=tk.Y)

        # Remote INI Text (read-only)
        self.remote_ini_text = tk.Text(ini_inner, height=10, state="disabled")
        self.remote_ini_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scroll both simultaneously
        def scroll_both(*args):
            self.local_ini_text.yview(*args)
            self.remote_ini_text.yview(*args)

        self.local_ini_text.configure(yscrollcommand=shared_scroll.set)
        self.remote_ini_text.configure(yscrollcommand=shared_scroll.set)
        shared_scroll.config(command=scroll_both)

        # Save Buttons (left-aligned)
        save_buttons = ttk.Frame(self.ini_frame)
        save_buttons.pack(anchor='w', pady=5)

        ttk.Button(save_buttons, text="Save to Local Directory", command=self.save_ini).pack(side=tk.LEFT, padx=5)
        ttk.Button(save_buttons, text="Save As...", command=self.save_ini_as).pack(side=tk.LEFT, padx=5)

        # Compare Button (centered)
        compare_frame = ttk.Frame(self.ini_frame)
        compare_frame.pack(pady=5)

        compare_btn = ttk.Button(compare_frame, text="Compare", command=self.compare_ini_files)
        compare_btn.pack()

        self.sched_frame = ttk.LabelFrame(self, text="Schedules")
        self.sched_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        schedule_inner = ttk.Frame(self.sched_frame)
        schedule_inner.pack(fill=tk.BOTH, expand=True)

        left_panel = ttk.Frame(schedule_inner)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.sched_tree = ttk.Treeview(left_panel, columns=("desc", "fail", "success", "skip"), show="headings", selectmode="browse", height=10)
        self.sched_tree.heading("desc", text="Schedule")
        self.sched_tree.heading("fail", text="Fail")
        self.sched_tree.heading("success", text="Success")
        self.sched_tree.heading("skip", text="Skipped")
        self.sched_tree.column("fail", width=50, minwidth=40, stretch=True, anchor="center")
        self.sched_tree.column("success", width=50, minwidth=50, stretch=True, anchor="center")
        self.sched_tree.column("skip", width=50, minwidth=50, stretch=False, anchor="center")
        self.sched_tree.column("desc", width=900, minwidth=300, stretch=True, anchor="w")
        self.sched_tree.pack(fill=tk.BOTH, expand=True)
        self.sched_tree.bind("<ButtonRelease-1>", lambda e: self.on_schedule_click(e))

       
        self.sched_tree.bind("<Button-3>", self.schedule_right_click)

        ttk.Button(left_panel, text="Save Schedules", command=self.save_schedules_to_file).pack(pady=2)
        ttk.Button(left_panel, text="Load Schedules", command=self.load_schedules_from_file).pack(pady=2)


        # Right side: Calendar
        right_panel = ttk.Frame(schedule_inner)
        right_panel.pack(side=tk.RIGHT, anchor="ne", padx=10)

        self.schedule_calendar = ScheduleCalendar(right_panel, self)
        self.schedule_calendar.pack()
        self.log_frame = ttk.LabelFrame(self, text="Log")
        self.log_frame.pack(fill=tk.X, padx=5, pady=5)

        self.log_text = tk.Text(self.log_frame, height=6)
        self.log_text.pack(fill=tk.BOTH, expand=False)

        self.status_bar = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor="w")
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        ttk.Button(self.remote_frame, text="Home", command=self.navigate_remote_home).pack(fill=tk.X)

        # Reflect correct scheduler button state
        if self.scheduler_running.get():
            self.pause_button.config(text="Pause Scheduler", style="sftpOn.TButton")
        else:
            self.pause_button.config(text="Resume Scheduler", style="sftpOff.TButton")
        self.update_sftp_button_style()


    def toggle_scheduler(self):
        if self.scheduler_running.get():
            self.scheduler.pause()
            self.scheduler_running.set(False)
            self.config_manager.config["scheduler_paused"] = True
            self.pause_button.config(text="Resume Scheduler", style="sftpOff.TButton")
            self.log("Scheduler paused")
        else:
            self.scheduler.resume()
            self.scheduler_running.set(True)
            self.config_manager.config["scheduler_paused"] = False
            self.pause_button.config(text="Pause Scheduler", style="sftpOn.TButton")
            self.log("Scheduler resumed")

        self.config_manager.save_config()

    def toggle_sftp(self):
        if self.sftp_enabled.get():
            # Disable SFTP
            self.sftp_enabled.set(False)
            self.config_manager.config["sftp_enabled"] = False
            self.config_manager.save_config()
            self.remote_tree.delete(*self.remote_tree.get_children())
            self.remote_breadcrumb.config(text="SFTP is OFF")
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.log("SFTP Disabled")
            return

        # Enable attempt: show orange "Connecting..."
        self.sftp_toggle_btn.config(text="Connecting...", style="orange.TButton")
        self.remote_breadcrumb.config(text="SFTP: checking...")
        self.update_idletasks()

        info = self.config_manager.config.get("sftp", {})
        if not all(info.get(k) for k in ['host', 'port', 'username', 'password']):
            self.remote_breadcrumb.config(text="Missing SFTP config")
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.log("SFTP config is incomplete.")
            return

        try:
            _ = self.get_sftp()  # just ensures connection
            self.sftp_enabled.set(True)
            self.config_manager.config["sftp_enabled"] = True
            self.config_manager.save_config()
            self.sftp_toggle_btn.config(text="Disable SFTP", style="sftpOn.TButton")
            self.remote_breadcrumb.config(text="SFTP is ACTIVE")
            self.refresh_remote_files()
            self.log("SFTP Enabled")
        except Exception as e:
            self.sftp_enabled.set(False)
            self.config_manager.config["sftp_enabled"] = False
            self.config_manager.save_config()
            self.remote_breadcrumb.config(text="SFTP FAILED")
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.log(f"SFTP connect failed: {e}")

    def update_sftp_button_style(self):
        # Disable remote interaction until confirmed
        self.remote_tree.delete(*self.remote_tree.get_children())
        self.remote_breadcrumb.config(text="SFTP: checking...")

        if not self.sftp_enabled.get():
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.remote_breadcrumb.config(text="SFTP is OFF")
            return

        self.sftp_toggle_btn.config(text="Connecting...", style="orange.TButton")
        self.update_idletasks()

        info = self.config_manager.config.get('sftp', {})
        if not all(info.get(k) for k in ['host', 'port', 'username', 'password']):
            self.sftp_toggle_btn.config(text="Invalid Config", style="sftpOff.TButton")
            self.remote_breadcrumb.config(text="Missing SFTP config")
            return

        try:
            _ = self.get_sftp()
            self.sftp_toggle_btn.config(text="Disable SFTP", style="sftpOn.TButton")
            self.remote_breadcrumb.config(text="SFTP is ACTIVE")
        except Exception:
            self.sftp_toggle_btn.config(text="Activate SFTP", style="sftpOff.TButton")
            self.remote_breadcrumb.config(text="SFTP is OFF")


  # def get_sftp(self):
  #     """Returns cached SFTP client or creates one if not yet connected."""
  #     # Enhanced debug to trace call origins
  #     stack = traceback.format_stack(limit=5)
  #     caller = stack[-3].strip()  # Get the method 2 levels up from this
  #     safe_debug(f"get_sftp() called by: {caller}")

  #     if self.sftp_client:
  #         return self.sftp_client

  #     if not self.sftp_enabled.get():
  #         raise RuntimeError("SFTP is disabled by user")

  #     safe_debug("Creating new Transport-based SFTP connection")

  #     try:
  #         transport = paramiko.Transport((self.sftp_host, self.sftp_port))
  #         transport.connect(username=self.sftp_user, password=self.sftp_password)
  #         sftp = paramiko.SFTPClient.from_transport(transport)
  #         sftp.home_path = sftp.normalize(".")
  #         self.sftp_client = sftp
  #         self.transport = transport
  #         return sftp
  #     except Exception as e:
  #         safe_error(f"SFTP connection failed: {e}")
  #         raise

    def compare_ini_files(self):
        self.local_ini_text.tag_delete("diff")
        self.local_ini_text.tag_delete("missing")
        self.remote_ini_text.tag_delete("diff")
        self.remote_ini_text.tag_delete("missing")
        if not hasattr(self, 'local_ini_filename') or not hasattr(self, 'remote_ini_filename'):
            messagebox.showinfo("Compare", "Load both local and remote .ini files first.")
            return

        if os.path.basename(self.local_ini_filename).lower() != os.path.basename(self.remote_ini_filename).lower():
            messagebox.showwarning("Compare", "Cannot compare different files.\nFile names must match.")
            return

        self.local_ini_text.tag_delete("diff")
        self.local_ini_text.tag_delete("missing")
        self.remote_ini_text.tag_delete("diff")
        self.remote_ini_text.tag_delete("missing")

        self.local_ini_text.tag_config("diff", background="lightyellow")
        self.local_ini_text.tag_config("missing", background="#f8d0d8")
        self.remote_ini_text.tag_config("diff", background="lightyellow")
        self.remote_ini_text.tag_config("missing", background="#f8d0d8")

        local_lines = self.local_ini_text.get("1.0", tk.END).splitlines()
        remote_lines = self.remote_ini_text.get("1.0", tk.END).splitlines()

        def strip_prefix(line):
            return line[4:] if len(line) > 4 else ""

        local_clean = {strip_prefix(line).strip().split('=')[0]: (i, strip_prefix(line).strip()) for i, line in enumerate(local_lines)}
        remote_clean = {strip_prefix(line).strip().split('=')[0]: (i, strip_prefix(line).strip()) for i, line in enumerate(remote_lines)}

        all_keys = sorted(set(local_clean.keys()).union(set(remote_clean.keys())))

        for key in all_keys:
            l_data = local_clean.get(key)
            r_data = remote_clean.get(key)

            if l_data and r_data:
                l_index, l_line = l_data
                r_index, r_line = r_data
                if l_line.strip().lower() != r_line.strip().lower():
                    l_tag = f"{l_index + 1}.0"
                    r_tag = f"{r_index + 1}.0"
                    self.local_ini_text.tag_add("diff", l_tag, f"{l_index + 1}.end")
                    self.remote_ini_text.tag_add("diff", r_tag, f"{r_index + 1}.end")
            elif l_data:
                l_index, _ = l_data
                l_tag = f"{l_index + 1}.0"
                self.local_ini_text.tag_add("missing", l_tag, f"{l_index + 1}.end")
            elif r_data:
                r_index, _ = r_data
                r_tag = f"{r_index + 1}.0"
                self.remote_ini_text.tag_add("missing", r_tag, f"{r_index + 1}.end")

        self.log("Intelligent .ini comparison completed.")

    def save_ini_as(self):
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".ini",
                filetypes=[("INI Files", "*.ini")],
                title="Save ServerSettings.ini As"
            )
            if file_path:
                with open(file_path, 'w') as f:
                    f.write(self.local_ini_text.get("1.0", tk.END))
                self.log(f"serversettings.ini saved to {file_path}")
        except Exception as e:
            self.log(f"Failed to save serversettings.ini: {e}")

    def toggle_dir_copy(self):
        self.config_manager.config['enable_dir_copy'] = self.enable_dir_copy_var.get()

        if 'sftp' in self.config_manager.config and 'password' in self.config_manager.config['sftp']:
            try:
                raw_password = self.config_manager.config['sftp']['password']
                encrypted_pw = fernet.encrypt(raw_password.encode()).decode()
                self.config_manager.config['sftp']['password'] = encrypted_pw
                with open(CONFIG_FILE, 'w') as f:
                    json.dump(self.config_manager.config, f, indent=4)
                self.config_manager.config['sftp']['password'] = raw_password
            except Exception as e:
                self.log(f"Failed to save encrypted password: {e}")
        else:
            self.config_manager.save_config()

        state = "Enabled" if self.enable_dir_copy_var.get() else "Disabled"
        self.log(f"Directory Copy: {state}")

    def toggle_logging(self):
        new_state = not notification_config.get('log_enabled', True)
        notification_config['log_enabled'] = new_state
        with open(NOTIFICATION_CONFIG_FILE, "w") as f:
            json.dump(notification_config, f, indent=2)
        state = "enabled" if new_state else "disabled"
        self.log(f"Logging {state}")
        if hasattr(self, 'safety_menu_var'):
            self.safety_menu_var.set(new_state)

    def toggle_sftp_throttle(self):
        current = self.honor_sftp_limits.get()
        self.config_manager.config["honor_sftp_limits"] = current
        self.config_manager.save_config()
        state = "enabled" if current else "disabled"
        self.log(f"SFTP throttle mode {state}")

    def navigate_remote_home(self):
        safe_debug("Method called: navigate_remote_home")
        if not self.sftp_enabled.get():
            self.log("SFTP is disabled - cannot go to remote home.")
            return

        try:
            sftp = self.get_sftp()
            try:
                self.remote_path = sftp.client.normalize('.')
            except Exception:
                if self.remote_base:
                    self.remote_path = self.remote_base
                else:
                    self.remote_path = "/"
            self.remote_breadcrumb.config(text=self.remote_path)
            self.refresh_remote_files()
          # sftp.close()
        except Exception as e:
            self.log(f"Failed to go to home: {e}")

    def load_config_and_refresh(self):
        file_path = filedialog.askopenfilename(
            title="Load Config File",
            filetypes=[("JSON Files", "*.json")]
        )
        if file_path:
            self.config_manager.load_config_file(file_path)
            self.refresh_schedules()
            self.refresh_local_files()
            self.refresh_remote_files()
            self.log("Configuration loaded.")

    def navigate_up(self, event=None):
        parent = os.path.dirname(self.local_path)
        if os.path.exists(parent) and parent != self.local_path:
            self.local_path = parent
            self.local_path_var.set(self.local_path)  # Update the path label correctly
            self.refresh_local_files()

    def navigate_remote_up(self, event=None):
        if not self.sftp_enabled.get():
            self.log("SFTP is disabled - cannot navigate remote.")
            return

        if not self.remote_path:
            self.log("Remote path is not set.")
            return

        parent = os.path.dirname(self.remote_path.rstrip("/")) or "/"
        self.remote_path = parent
        self.remote_breadcrumb.config(text=self.remote_path)
        self.refresh_remote_files()

    def refresh_local_files(self):
        self.local_tree.delete(*self.local_tree.get_children())
        try:
            for item in sorted(os.listdir(self.local_path)):
                full_path = os.path.join(self.local_path, item)
                if os.path.isdir(full_path):
                    self.local_tree.insert('', 'end', text=item, values=("Folder",))
                elif item.lower() == "serversettings.ini":
                    self.local_tree.insert('', 'end', text=item, values=("INI File",))
        except Exception as e:
            self.log(f"Failed to read local directory: {e}")

    def on_local_double_click(self, event):
        item = self.local_tree.identify('item', event.x, event.y)
        if not item:
            return
        selected = self.local_tree.item(item)['text']
        path = os.path.join(self.local_path, selected)

        if os.path.isdir(path):
            self.local_path = path
            self.local_path_var.set(self.local_path)
            self.refresh_local_files()
        elif selected.lower() == "serversettings.ini":
            try:
                with open(path, 'r') as f:
                    lines = f.readlines()
                self.local_ini_text.tag_remove("diff", "1.0", tk.END)
                self.local_ini_text.tag_remove("missing", "1.0", tk.END)
                self.local_ini_text.delete("1.0", tk.END)
                for i, line in enumerate(lines, 1):
                    self.local_ini_text.insert(tk.END, f"{i:03} {line}")
                self.local_ini_filename = path 
                self.log(f"Loaded local {selected} into viewer.")
            except Exception as e:
                self.log(f"Failed to open serversettings.ini: {e}")

    def on_remote_double_click(self, event):
        item = self.remote_tree.identify('item', event.x, event.y)
        if not item:
            return
        selected = self.remote_tree.item(item)['text']

        if selected == "..":
            self.remote_path = os.path.dirname(self.remote_path.rstrip("/")) or "/"
            self.refresh_remote_files()
        else:
            new_path = os.path.join(self.remote_path, selected).replace("\\", "/")
            if selected.lower() == "serversettings.ini":
                try:
                    sftp = self.get_sftp()
                    with sftp.client.open(new_path, 'r') as f:
                        lines = f.readlines()
                  # sftp.close()

                    self.remote_ini_text.configure(state='normal')
                    self.remote_ini_text.tag_remove("diff", "1.0", tk.END)
                    self.remote_ini_text.tag_remove("missing", "1.0", tk.END)
                    self.remote_ini_text.delete("1.0", tk.END)
                    for i, line in enumerate(lines, 1):
                        self.remote_ini_text.insert(tk.END, f"{i:03} {line}")
                    self.remote_ini_text.configure(state='disabled')

                    self.remote_ini_filename = new_path
                    self.log(f"Loaded remote {selected} into viewer.")
                except Exception as e:
                    self.log(f"Failed to load remote serversettings.ini: {e}")
            elif self.is_remote_directory(selected):
                self.remote_path = new_path
                self.refresh_remote_files()

    
    def is_remote_directory(self, name):
        return name.endswith("/")


    def refresh_remote_files(self, skip_get=False):
        safe_debug("Method called: refresh_remote_files")

        if not self.sftp_enabled.get():
            self.remote_tree.delete(*self.remote_tree.get_children())
            self.remote_breadcrumb.config(text="SFTP is OFF")
            return

        try:
            sftp = self.sftp_client if skip_get else self.get_sftp()

            if self.remote_base is None:
                self.remote_base = sftp.home_path
                self.remote_path = self.remote_base
                safe_debug(f"Detected SFTP home path: {self.remote_base}")

            self.remote_tree.delete(*self.remote_tree.get_children())

            try:
                entries = sftp.listdir(self.remote_path)
                safe_debug(f"Raw directory entries from {self.remote_path}: {entries}")
            except IOError as e:
                logging.warning(f"Directory listing failed for {self.remote_path}: {e}")
                self.remote_path = self.remote_base
                entries = sftp.listdir(self.remote_path)
                safe_debug(f"Fallback directory entries from {self.remote_path}: {entries}")

            self.remote_breadcrumb.config(text=self.remote_path)

            if self.remote_path != self.remote_base:
                self.remote_tree.insert('', 'end', text="..", values=("..",))

            if not entries:
                self.log("Remote directory is empty or not accessible.")

            for attr in entries:
                try:
                    name = getattr(attr, 'filename', None)
                    if not name:
                        continue
                    is_dir = hasattr(attr, 'st_mode') and stat.S_ISDIR(attr.st_mode)
                    file_type = "Folder" if is_dir else "File"
                    display_name = name + ("/" if is_dir else "")
                    self.remote_tree.insert('', 'end', text=display_name, values=(file_type,))
                    safe_debug(f"Listed remote item: {display_name} ({file_type})")
                except Exception as e:
                    logging.warning(f"Error displaying remote entry: {e}")

            self.status_var.set("SFTP connected to " + self.remote_path)

        except Exception as e:
            self.remote_tree.delete(*self.remote_tree.get_children())
            self.remote_breadcrumb.config(text="SFTP error")
            self.status_var.set("SFTP connection failed")
            self.log(f"Failed to list remote: {e}")


    def save_ini(self):
        path = os.path.join(self.local_path, "serversettings.ini")
        try:
            with open(path, 'w') as f:
                f.write(self.ini_text.get("1.0", tk.END))
            self.log("serversettings.ini saved.")
        except Exception as e:
            self.log(f"Failed to save serversettings.ini: {e}")

    def log(self, msg):
        self.status_var.set(msg)
        safe_log(msg)
        self.log_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {msg}\n")
        self.log_text.see(tk.END)

    def get_sftp(self):
        """Returns cached SFTP client or creates one if not yet connected."""
        import traceback
        stack = traceback.format_stack(limit=5)
        caller = stack[-3].strip()
        safe_debug(f"get_sftp() called from: {caller}")

        if hasattr(self, "sftp_client") and self.sftp_client:
            return self.sftp_client

        sftp_conf = self.config_manager.config.get("sftp", {})
        host = sftp_conf.get("host")
        port = int(sftp_conf.get("port", 22))
        user = sftp_conf.get("username")
        pw = sftp_conf.get("password")

        if not all([host, port, user, pw]):
            raise ValueError("SFTP settings incomplete.")

        safe_debug("Creating Transport-based SFTP connection")
        self.log(f"Connecting to SFTP at {host}:{port} as {user}")
        sftp = SFTPManager(host, port, user, pw)

        self.sftp_client = sftp
        self.sftp_session = sftp
        return sftp



  # def get_sftp(self):
  #     safe_debug(f"get_sftp() called by: {caller}")
  #     info = self.config_manager.config['sftp']
  #     return SFTPManager(
  #         host=info['host'],
  #         port=info['port'],
  #         username=info['username'],
  #         password=info['password']
  #     )




    def refresh_schedules(self):
        self.sched_tree.delete(*self.sched_tree.get_children())

        schedules = self.config_manager.config['schedules']
        sorted_scheds = sorted(schedules, key=get_next_run)

        for i, sched in enumerate(sorted_scheds):
            desc_parts = []
            hour, minute = sched['hour'], sched['minute']
            time_str = f"at {hour:02}:{minute:02}"
            repeat = sched.get('repeat', 'none').strip().lower()
            if repeat == "none":
                repeat_desc = "once only"
            elif repeat == "daily":
                repeat_desc = "Every day"
            elif repeat == "weekly":
                repeat_desc = f"Every week on {sched.get('day', 'Any')}"
            elif repeat == "monthly":
                repeat_desc = f"Every month on day {sched.get('day_of_month', '1')}"
            else:
                repeat_desc = f"Repeat: {repeat}"

            local_file = os.path.basename(sched.get('filepath', ''))
            local_dir = os.path.dirname(sched.get('filepath', ''))
            title = sched.get("title", "").strip()
            title_text = f"{title} | " if title else ""
            desc = f"#{i+1} - {title_text}{repeat_desc} {time_str} | sending '{local_file}' from '{local_dir}'"

            # Normalized config defaults
            sched.setdefault("enabled", True)
            sched.setdefault("email_on_fail", True)
            sched.setdefault("email_on_success", False)
            sched.setdefault("email_on_skip", True)

            fail = "[x]" if sched["email_on_fail"] else "[ ]"
            succ = "[x]" if sched["email_on_success"] else "[ ]"
            skip = "[x]" if sched["email_on_skip"] else "[ ]"

            tags = ("disabled",) if not sched["enabled"] else ()

            self.sched_tree.insert("", "end", iid=str(i), values=(desc, fail, succ, skip), tags=tags)

        self.sched_tree.tag_configure("disabled", foreground="#888888")

    def on_schedule_click(self, event):
        row_id = self.sched_tree.identify_row(event.y)
        col = self.sched_tree.identify_column(event.x)
        if not row_id or not col:
            return

        item = self.sched_tree.item(row_id)
        desc = item["values"][0]

        import re
        match = re.match(r"#(\d+)", desc)
        if not match:
            return

        sorted_index = int(match.group(1)) - 1
        sorted_scheds = sorted(self.config_manager.config["schedules"], key=get_next_run)
        if 0 <= sorted_index < len(sorted_scheds):
            schedule = sorted_scheds[sorted_index]
        else:
            return

        # Only update the config values based on column
        if col == "#1":  # Toggle enabled
            schedule["enabled"] = not schedule.get("enabled", True)
        elif col == "#2":  # Toggle fail
            schedule["email_on_fail"] = not schedule.get("email_on_fail", True)
        elif col == "#3":  # Toggle success
            schedule["email_on_success"] = not schedule.get("email_on_success", False)
        elif col == "#4":  # Toggle skipped
            schedule["email_on_skip"] = not schedule.get("email_on_skip", True)
        else:
            schedule["enabled"] = not schedule.get("enabled", True)

        self.config_manager.save_config()
        self.refresh_schedules()
        self.schedule_calendar.build_calendar()



    def save_schedules_to_file(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json")],
            title="Save Schedule Configuration"
        )
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    json.dump(self.config_manager.config['schedules'], f, indent=4)
                self.log(f"Schedules saved to {file_path}")

                # Store the last used schedule file path for reference (e.g., reload support)
                self.config_manager.config['last_schedule_file'] = file_path
                self.config_manager.save_config()
            except Exception as e:
                self.log(f"Failed to save schedules: {e}")

    def load_schedules_from_file(self):
        try:
            file_path = filedialog.askopenfilename(
                title="Load Schedule Config File",
                filetypes=[("JSON Files", "*.json")]
            )
            if file_path:
                with open(file_path, 'r') as f:
                    schedules = json.load(f)
                    if isinstance(schedules, list):
                        self.config_manager.config['schedules'] = schedules
                        self.refresh_schedules()
                        self.log(f"Schedules loaded from {file_path}")
                    else:
                        self.log("Invalid schedule file format.")
        except Exception as e:
            self.log(f"Failed to load schedules: {e}")

    def local_right_click(self, event):
        menu = tk.Menu(self, tearoff=0)
        iid = self.local_tree.identify_row(event.y)
        if iid:
            menu.add_command(label="Copy", command=lambda: self.copy_local(iid))
        menu.add_command(label="Paste", command=self.paste_local)
        menu.post(event.x_root, event.y_root)

    def paste_local(self):
        src = self.config_manager.config['clipboard']
        if not src:
            self.log("Clipboard is empty")
            return
        dst = os.path.join(self.local_path, os.path.basename(src.rstrip("/")))
        try:
            if src.startswith('/') or ':' in src:
                sftp = self.get_sftp()
                try:
                    attrs = sftp.client.stat(src)
                    expected_size = attrs.st_size
                    if stat.S_ISDIR(attrs.st_mode):
                        if not self.enable_dir_copy_var.get():
                            self.log("Directory copy is disabled.")
                            return
                        os.makedirs(dst, exist_ok=True)
                        self.download_remote_dir(sftp, src, dst)
                        self.log(f"Downloaded folder {src} to {dst}")
                    else:
                        tmp_path = dst + ".partial"
                        attempt = 0
                        while attempt < 2:
                            sftp.download(src, tmp_path)
                            actual_size = os.path.getsize(tmp_path)
                            if actual_size == expected_size:
                                os.rename(tmp_path, dst)
                                self.log(f"Downloaded {src} to {dst}")
                                break
                            else:
                                self.log(f"Size mismatch on attempt {attempt+1}: {actual_size} != {expected_size}")
                                os.remove(tmp_path)
                                attempt += 1
                                time.sleep(1)
                        else:
                            raise IOError(f"size mismatch after retries: final {actual_size} != {expected_size}")
                except IOError as e:
                    self.log(f"Failed to download {src}: {e}")
              # finally:
                #   sftp.close()

            elif os.path.exists(src):
                if os.path.isdir(src):
                    if os.path.exists(dst):
                        shutil.rmtree(dst)
                    shutil.copytree(src, dst)
                else:
                    shutil.copy2(src, dst)
                self.log(f"Copied {src} to {dst}")
            else:
                self.log("Clipboard source path does not exist")
            self.refresh_local_files()
        except Exception as e:
            self.log(f"Failed to paste: {e}")


    def download_remote_dir(self, sftp, remote_dir, local_dir):
        for item in sftp.client.listdir_attr(remote_dir):
            remote_path = os.path.join(remote_dir, item.filename).replace("\\", "/")
            local_path = os.path.join(local_dir, item.filename)

            if stat.S_ISDIR(item.st_mode):
                os.makedirs(local_path, exist_ok=True)
                self.download_remote_dir(sftp, remote_path, local_path)
            else:
                tmp_path = local_path + ".partial"
                try:
                    sftp.download(remote_path, tmp_path)
                    actual_size = os.path.getsize(tmp_path)
                    expected_size = item.st_size
                    if actual_size != expected_size:
                        self.log(f"Size mismatch on {remote_path}: expected {expected_size}, got {actual_size}")
                        os.remove(tmp_path)
                        continue
                    os.rename(tmp_path, local_path)
                    self.log(f"Downloaded {remote_path} to {local_path}")
                except Exception as e:
                    self.log(f"Failed to download {remote_path}: {e}")

    def remote_right_click(self, event):
        iid = self.remote_tree.identify_row(event.y)
        if iid:
            menu = tk.Menu(self, tearoff=0)
            menu.add_command(label="Copy File", command=lambda: self.copy_remote(iid))
            menu.add_command(label="Paste File", command=self.paste_remote)
            menu.add_command(label="Copy Path", command=lambda: self.copy_remote_path(iid))
            menu.post(event.x_root, event.y_root)

    def copy_remote_path(self, iid):
        try:
            item = self.remote_tree.item(iid)['text']
            remote_path = os.path.join(self.remote_path, item).replace("\\", "/")
            self.clipboard_clear()
            self.clipboard_append(remote_path)
            self.log(f"Copied remote path to clipboard: {remote_path}")
        except Exception as e:
            self.log(f"Failed to copy remote path: {e}")

    def copy_local(self, iid):
        item = self.local_tree.item(iid)['text']
        path = os.path.join(self.local_path, item)
        if os.path.exists(path):
            self.config_manager.config['clipboard'] = path
            self.log(f"Copied {item} from local")
        else:
            self.log(f"Local item not found: {item}")

    def copy_remote(self, iid):
        item = self.remote_tree.item(iid)['text']
        remote_path = os.path.join(self.remote_path, item).replace("\\", "/")
        self.config_manager.config['clipboard'] = remote_path
        self.log(f"Copied {item} from remote")

    def paste_remote(self):
        src = self.config_manager.config['clipboard']
        if src and os.path.exists(src):
            try:
                sftp = self.get_sftp()
                dst = os.path.join(self.remote_path, os.path.basename(src)).replace("\\", "/")
                if os.path.isdir(src):
                    if not self.enable_dir_copy_var.get():
                        self.log("Directory copy is disabled in settings.")
                        return
                    self.upload_folder(sftp, src, dst)
                else:
                    sftp.upload(src, dst)
              # sftp.close()
                self.refresh_remote_files()
                self.log(f"Uploaded {src} to {dst}")
            except Exception as e:
                self.log(f"Upload failed: {e}")
        else:
            self.log("Clipboard is empty or source path does not exist")


    def upload_folder(self, sftp, local_folder, remote_folder):
        try:
            try:
                sftp.client.mkdir(remote_folder)
            except IOError:
                pass
            for item in os.listdir(local_folder):
                local_path = os.path.join(local_folder, item)
                remote_path = f"{remote_folder}/{item}".replace("\\", "/")
                if os.path.isdir(local_path):
                    self.upload_folder(sftp, local_path, remote_path)
                else:
                    sftp.upload(local_path, remote_path)
        except Exception as e:
            self.log(f"Failed to upload folder {local_folder}: {e}")

    def schedule_right_click(self, event):
        row_id = self.sched_tree.identify_row(event.y)
        if not row_id:
            return

        self.sched_tree.selection_set(row_id)

        index = int(row_id)
        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Edit", command=lambda: self.edit_schedule(index))
        menu.add_command(label="New Schedule", command=self.setup_schedule)
        menu.add_separator()
        menu.add_command(label="Copy", command=lambda: self.copy_schedule(index))
        menu.add_command(label="Delete", command=lambda: self.delete_schedule(index))
        menu.add_separator()
        menu.add_command(label="Move Up", command=lambda: self.move_schedule_up(index))
        menu.add_command(label="Move Down", command=lambda: self.move_schedule_down(index))
        menu.add_separator()
        menu.add_command(label="Properties", command=lambda: self.view_schedule_properties(index))
        menu.tk_popup(event.x_root, event.y_root)

    def edit_schedule(self, index):
        try:
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            target = sorted_scheds[index]
            unsorted_index = self.config_manager.config['schedules'].index(target)
            self.setup_schedule(existing=target, index=unsorted_index, title_suffix=f"#{index + 1}")
        except Exception as e:
            self.log(f"Failed to edit schedule: {e}")

    def delete_selected_schedule(self):
        selected = self.schedule_tree.selection()
        if not selected:
            messagebox.showinfo("Delete Schedule", "Please select a schedule to delete.")
            return

        for item_id in selected:
            item = self.schedule_tree.item(item_id)
            title = item['values'][0]
            self.schedule_config = [s for s in self.schedule_config if s.get("title") != title]

        self.config_manager.config['schedules'] = self.schedule_config
        self.config_manager.save_config()
        self.refresh_schedules()
        self.update_calendar_colors()  #  Force calendar refresh after delete
        self.log("Selected schedules deleted.")

    def delete_schedule(self, index):
        try:
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            target = sorted_scheds[index]
            self.config_manager.config['schedules'].remove(target)
            self.refresh_schedules()
            self.config_manager.save_config()
            self.update_calendar_colors()  # Ensure calendar reflects deletion
            self.log(f"Deleted schedule #{index + 1}")
        except Exception as e:
            self.log(f"Failed to delete schedule: {e}")

    def highlight_schedule_calendar(self, schedule):
        """
        Highlights only the selected schedule's history on the calendar based on run_history.
        """
        self.clear_calendar_tags()
        if not schedule or "run_history" not in schedule:
            return

        for timestamp, status in schedule.get("run_history", {}).items():
            try:
                dt = datetime.fromisoformat(timestamp)
                tag = get_calendar_tag_color(status)
                self.calendar.calevent_create(dt, status, tag)
            except Exception:
                continue

    def toggle_calendar_highlight(self, event):
        """
        Triggered by checkbox; shows/hides calendar events based on the selected row.
        """
        item_id = self.schedule_tree.identify_row(event.y)
        if not item_id:
            return

        current = self.schedule_tree.set(item_id, "highlight")
        new_value = "0" if current == "1" else "1"
        self.schedule_tree.set(item_id, "highlight", new_value)

        self.clear_calendar_tags()
        for row_id in self.schedule_tree.get_children():
            if self.schedule_tree.set(row_id, "highlight") == "1":
                title = self.schedule_tree.item(row_id)['values'][0]
                for schedule in self.config_manager.config.get("schedules", []):
                    if schedule.get("title") == title:
                        self.highlight_schedule_calendar(schedule)
                        break

    def copy_schedule(self, index):
        try:
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            original = sorted_scheds[index]
            copied = original.copy()
            self.config_manager.config['schedules'].append(copied)
            self.refresh_schedules()
            self.log(f"Copied schedule #{index + 1}")
        except Exception as e:
            self.log(f"Failed to copy schedule: {e}")

    def move_schedule_up(self, index):
        if index > 0:
            schedules = self.config_manager.config['schedules']
            schedules[index - 1], schedules[index] = schedules[index], schedules[index - 1]
            self.refresh_schedules()
            self.log(f"Moved schedule {index} up")

    def move_schedule_down(self, index):
        schedules = self.config_manager.config['schedules']
        if index < len(schedules) - 1:
            schedules[index + 1], schedules[index] = schedules[index], schedules[index + 1]
            self.refresh_schedules()
            self.log(f"Moved schedule {index} down")

    def view_schedule_properties(self, index):
        try:
            # Match schedule by sorting
            sorted_scheds = sorted(self.config_manager.config['schedules'], key=get_next_run)
            if index >= len(sorted_scheds):
                self.log(f"Invalid schedule index: {index}")
                return

            sched = sorted_scheds[index]
            info = json.dumps(sched, indent=4)

            window_number = f"#{index + 1}"
            top = tk.Toplevel(self)
            top.title(f"{window_number} - Schedule Properties")
            top.geometry("400x300")

            text = tk.Text(top, wrap="word")
            text.insert("1.0", info)
            text.config(state="disabled")
            text.pack(fill="both", expand=True)

        except Exception as e:
            self.log(f"Failed to view properties: {e}")

    def on_schedule_double_click(self, event):
        row_id = self.sched_tree.identify_row(event.y)
        if not row_id:
            return

        index = int(row_id)
        try:
            sched = self.config_manager.config['schedules'][index]
            filepath = sched.get("filepath")
            if filepath and os.path.exists(filepath):
                with open(filepath, 'r') as f:
                    self.local_ini_text.delete("1.0", tk.END)
                    self.local_ini_text.insert(tk.END, f.read())

                self.local_path = os.path.dirname(filepath)
                self.local_path_var.set(self.local_path)
                self.refresh_local_files()
                self.local_ini_filename = filepath  # Ensure comparison works later
                self.log(f"Loaded schedule file into viewer: {filepath}")
            else:
                self.log(f"File not found: {filepath}")
        except Exception as e:
            self.log(f"Error loading scheduled file: {e}")

    def on_close(self):
        try:
            self.config_manager.save_config()
            self.config_manager.save_schedules()
            self.log("Configuration and schedules saved on exit.")
        except Exception as e:
            self.log(f"Failed to save on exit: {e}")
        self.destroy()

def deferred_sftp_setup(self):
    safe_debug("Method called: deferred_sftp_setup")

    if not self.sftp_enabled.get():
        safe_debug("SFTP is disabled at launch - skipping initial connection.")
        return

    try:
        sftp = self.get_sftp()
        self.sftp_client = sftp  # reuse this session

        # Cache remote SFTP packet limits if available
        try:
            chan = sftp.client.get_channel()
            self.max_packet_in = chan.get_remote_max_packet_size()
            self.max_packet_out = chan.get_local_max_packet_size()
            self.log(f"Host SFTP max packet in: {self.max_packet_in} bytes")
            self.log(f"Host SFTP max packet out: {self.max_packet_out} bytes")
        except Exception as e:
            self.log(f"Failed to retrieve SFTP packet limits: {e}")

        try:
            sftp.client.chdir(".")
            base = sftp.client.getcwd()
            target_path = os.path.join(base, "Config", "WindowsServer").replace("\\", "/")
            sftp.client.chdir(target_path)
            self.remote_base = sftp.client.getcwd()
            self.remote_path = self.remote_base
            safe_debug(f"Navigated to remote path: {self.remote_base}")
        except Exception as inner_e:
            logging.warning(f"Failed to navigate to Config/WindowsServer: {inner_e}")
            self.remote_base = "/"
            self.remote_path = "/"

        self.refresh_remote_files(skip_get=True)

    except Exception as e:
        safe_error(f"Initial SFTP connection failed: {e}")
        self.remote_base = "/"
        self.remote_path = "/"

def get_calendar_tag_color(status, bright_mode=False):
    if bright_mode:
        color_map = {
            "failed": "red",
            "success": "green",
            "skipped": "orange",
            "upcoming": "blue",
        }
    else:
        color_map = {
            "failed": "lightpink",
            "success": "lightgreen",
            "skipped": "orange",
            "upcoming": "lightblue",
        }
    return color_map.get(status, "default")

def get_next_run(sched):
    now = datetime.now()
    repeat = str(sched.get("repeat", "none")).lower()
    hour = int(sched.get("hour", 0))
    minute = int(sched.get("minute", 0))
    day_of_week = sched.get("day", "Any")
    day_of_month = sched.get("day_of_month", "Any")
    month = sched.get("month", "Any")

    try:
        for i in range(0, 60):  # Look up to 60 days ahead
            future = now + timedelta(days=i)
            if future.hour != hour or future.minute != minute:
                future = future.replace(hour=hour, minute=minute, second=0, microsecond=0)

            if repeat == "daily":
                return future

            elif repeat == "weekly":
                if calendar.day_name[future.weekday()] == day_of_week:
                    return future

            elif repeat == "monthly":
                if str(future.day) == str(day_of_month):
                    return future

            elif repeat == "none":
                if (month == "Any" or month == calendar.month_name[future.month]) and \
                   (day_of_month == "Any" or str(future.day) == str(day_of_month)) and \
                   (day_of_week == "Any" or calendar.day_name[future.weekday()] == day_of_week):
                    return future

    except Exception:
        pass

    return datetime.max  # if invalid or incomplete

if __name__ == "__main__":
    try:
        app = MainApp()
        app.protocol("WM_DELETE_WINDOW", app.on_close)  # Ensures save on exit
        app.mainloop()
    except Exception as e:
        import traceback
        with open("fatal.log", "w") as f:
            f.write(traceback.format_exc())
        raise


    def toggle_sftp_throttle(self):
        current = self.honor_sftp_limits.get()
        self.config_manager.config["honor_sftp_limits"] = current
        self.config_manager.save_config()
        state = "enabled" if current else "disabled"
        self.log(f"SFTP throttle mode {state}")



def record_schedule_result(self, schedule, result_status):
    """
    Appends a new result to the schedule's run_history and saves the updated config.
    result_status should be one of: 'success', 'failed', 'skipped'
    """
    timestamp = datetime.now().replace(second=0, microsecond=0).isoformat(timespec='minutes')
    if "run_history" not in schedule:
        schedule["run_history"] = {}

    schedule["run_history"][timestamp] = result_status

    # Sync back into schedule_config list if needed
    for sched in self.schedule_config:
        if sched.get("title") == schedule.get("title"):
            sched["run_history"] = schedule["run_history"]
            break

    self.config_manager.save_config()
    self.log(f"Recorded run at {timestamp}: {result_status}")
