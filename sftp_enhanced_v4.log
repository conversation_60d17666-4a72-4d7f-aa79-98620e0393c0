2025-06-10 05:02:13,003 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:03:00,837 - <PERSON><PERSON>HANCED_v4 - INFO - Local: Entered directory SFTP resources
2025-06-10 05:03:06,013 - <PERSON><PERSON>HANCED_v4 - INFO - === STARTING WIRING CHECK ===
2025-06-10 05:03:06,014 - ENHANCED_v4 - INFO - Testing button functionality...
2025-06-10 05:03:06,015 - ENHANCED_v4 - INFO - Scheduler paused
2025-06-10 05:03:06,015 - E<PERSON><PERSON>NCED_v4 - INFO - Testing mode switching...
2025-06-10 05:03:06,016 - <PERSON><PERSON><PERSON>NCED_v4 - INFO - Switched to LAN mode
2025-06-10 05:03:06,016 - ENHANCED_v4 - INFO - Testing file navigation...
2025-06-10 05:03:06,075 - <PERSON><PERSON><PERSON>NCED_v4 - INFO - Local: Navigated up to G:\My Drive\Python\MyPyStuff\Jay_Py\Jay Py\Augmentor!\SFTP 4
2025-06-10 05:03:06,075 - <PERSON><PERSON><PERSON>NCED_v4 - INFO - Testing SFTP connection...
2025-06-10 05:03:06,075 - ENHANCED_v4 - INFO - Testing schedule tree...
2025-06-10 05:03:06,517 - ENHANCED_v4 - INFO - Scheduler resumed
2025-06-10 05:03:07,018 - ENHANCED_v4 - INFO - Switched to SFTP mode
2025-06-10 05:03:09,076 - ENHANCED_v4 - INFO - === WIRING CHECK COMPLETE ===
2025-06-10 05:03:09,165 - ENHANCED_v4 - INFO - SFTP connect failed: Cannot reach sftp.example.com:22
2025-06-10 05:03:30,155 - ENHANCED_v4 - INFO - SFTP connect failed: Cannot reach sftp.example.com:22
2025-06-10 05:03:31,432 - ENHANCED_v4 - INFO - === STARTING WIRING CHECK ===
2025-06-10 05:03:31,433 - ENHANCED_v4 - INFO - Testing button functionality...
2025-06-10 05:03:31,433 - ENHANCED_v4 - INFO - Scheduler paused
2025-06-10 05:03:31,434 - ENHANCED_v4 - INFO - Testing mode switching...
2025-06-10 05:03:31,434 - ENHANCED_v4 - INFO - Switched to LAN mode
2025-06-10 05:03:31,434 - ENHANCED_v4 - INFO - Testing file navigation...
2025-06-10 05:03:31,489 - ENHANCED_v4 - INFO - Local: Navigated up to G:\My Drive\Python\MyPyStuff\Jay_Py\Jay Py\Augmentor!\SFTP 4
2025-06-10 05:03:31,489 - ENHANCED_v4 - INFO - Testing SFTP connection...
2025-06-10 05:03:31,490 - ENHANCED_v4 - INFO - Testing schedule tree...
2025-06-10 05:03:31,936 - ENHANCED_v4 - INFO - Scheduler resumed
2025-06-10 05:03:32,439 - ENHANCED_v4 - INFO - Switched to SFTP mode
2025-06-10 05:03:32,647 - ENHANCED_v4 - INFO - Scheduler paused
2025-06-10 05:03:34,201 - ENHANCED_v4 - INFO - Switched to LAN mode
2025-06-10 05:03:34,491 - ENHANCED_v4 - INFO - === WIRING CHECK COMPLETE ===
2025-06-10 05:03:34,494 - ENHANCED_v4 - INFO - SFTP connect failed: Cannot reach sftp.example.com:22
2025-06-10 05:03:35,534 - ENHANCED_v4 - INFO - Configuration loaded
2025-06-10 05:03:36,676 - ENHANCED_v4 - INFO - Configuration saved
2025-06-10 05:03:52,014 - ENHANCED_v4 - INFO - CustomTkinter mode enabled (restart required)
2025-06-10 05:03:56,342 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 shutdown
2025-06-10 05:04:37,940 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:04:42,824 - ENHANCED_v4 - INFO - CustomTkinter mode disabled
2025-06-10 05:04:44,707 - ENHANCED_v4 - INFO - CustomTkinter mode enabled (restart required)
2025-06-10 05:04:48,737 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 shutdown
2025-06-10 05:06:22,973 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:06:34,033 - ENHANCED_v4 - INFO - === TESTING ALL CONNECTIONS ===
2025-06-10 05:06:34,070 - ENHANCED_v4 - INFO - === CONNECTION TEST COMPLETE ===
2025-06-10 05:06:35,698 - ENHANCED_v4 - INFO - === STARTING WIRING CHECK ===
2025-06-10 05:06:35,701 - ENHANCED_v4 - INFO - Testing button functionality...
2025-06-10 05:06:35,705 - ENHANCED_v4 - INFO - Scheduler paused
2025-06-10 05:06:35,707 - ENHANCED_v4 - INFO - Testing mode switching...
2025-06-10 05:06:35,711 - ENHANCED_v4 - INFO - Switched to LAN mode
2025-06-10 05:06:35,713 - ENHANCED_v4 - INFO - Testing file navigation...
2025-06-10 05:06:35,734 - ENHANCED_v4 - INFO - Local: Navigated up to G:\My Drive\Python\MyPyStuff\Jay_Py\Jay Py\Augmentor!
2025-06-10 05:06:35,735 - ENHANCED_v4 - INFO - Testing SFTP connection...
2025-06-10 05:06:35,735 - ENHANCED_v4 - INFO - Testing schedule tree...
2025-06-10 05:06:36,215 - ENHANCED_v4 - INFO - Scheduler resumed
2025-06-10 05:06:36,724 - ENHANCED_v4 - INFO - Switched to SFTP mode
2025-06-10 05:06:38,737 - ENHANCED_v4 - INFO - === WIRING CHECK COMPLETE ===
2025-06-10 05:06:38,836 - ENHANCED_v4 - INFO - SFTP connect failed: Cannot reach sftp.example.com:22
2025-06-10 05:06:41,821 - ENHANCED_v4 - INFO - Credential Manager opened
2025-06-10 05:06:45,194 - ENHANCED_v4 - INFO - LAN credentials saved
2025-06-10 05:06:52,756 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 shutdown
2025-06-10 05:07:57,902 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:08:01,781 - ENHANCED_v4 - INFO - CustomTkinter mode disabled
2025-06-10 05:08:03,372 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 shutdown
2025-06-10 05:08:05,288 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:08:08,450 - ENHANCED_v4 - INFO - CustomTkinter mode enabled (restart required)
2025-06-10 05:08:11,235 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 shutdown
2025-06-10 05:09:56,368 - ENHANCED_v4 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:10:56,661 - ENHANCED_v4 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:10:56,669 - ENHANCED_v4 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:10:56,677 - ENHANCED_v4 - WARNING - Email settings incomplete - cannot send notification
