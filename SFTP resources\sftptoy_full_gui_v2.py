
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import shutil
import paramiko

class SettingsManagerApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("SFTP Toy")
        self.geometry("1000x600")
        self.current_local_dir = os.getcwd()
        self.clipboard_path = None
        self.sftp = None
        self.sftp_client = None
        self.sftp_connected = False

        self.create_widgets()

    def create_widgets(self):
        self.main_frame = tk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        top_frame = tk.Frame(self.main_frame)
        top_frame.pack(fill=tk.X)

        ttk.Label(top_frame, text="Host:").pack(side=tk.LEFT)
        self.host_entry = ttk.Entry(top_frame)
        self.host_entry.pack(side=tk.LEFT)
        ttk.Label(top_frame, text="Port:").pack(side=tk.LEFT)
        self.port_entry = ttk.Entry(top_frame, width=5)
        self.port_entry.insert(0, "22")
        self.port_entry.pack(side=tk.LEFT)
        ttk.Label(top_frame, text="Username:").pack(side=tk.LEFT)
        self.username_entry = ttk.Entry(top_frame)
        self.username_entry.pack(side=tk.LEFT)
        ttk.Label(top_frame, text="Password:").pack(side=tk.LEFT)
        self.password_entry = ttk.Entry(top_frame, show="*")
        self.password_entry.pack(side=tk.LEFT)

        connect_button = ttk.Button(top_frame, text="Connect SFTP", command=self.open_sftp_frame)
        connect_button.pack(side=tk.LEFT, padx=5)

        local_frame = tk.Frame(self.main_frame)
        local_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        ttk.Label(local_frame, text="Local Folder Contents").pack()
        self.local_tree = ttk.Treeview(local_frame, columns=("name",), show="tree")
        self.local_tree.pack(fill=tk.BOTH, expand=True)
        self.local_tree.bind("<Button-3>", self.show_context_menu)

        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Copy", command=self.copy_file)
        self.context_menu.add_command(label="Paste", command=self.paste_file)
        self.context_menu.add_command(label="Delete", command=self.delete_file)
        self.context_menu.add_command(label="Open in Notepad", command=self.open_in_notepad)

        self.refresh_local_tree()

    def refresh_local_tree(self):
        for item in self.local_tree.get_children():
            self.local_tree.delete(item)
        for file in os.listdir(self.current_local_dir):
            self.local_tree.insert("", "end", iid=file, text=file)

    def show_context_menu(self, event):
        item = self.local_tree.identify_row(event.y)
        if item:
            self.local_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def copy_file(self):
        selected = self.local_tree.selection()
        if selected:
            self.clipboard_path = os.path.join(self.current_local_dir, selected[0])

    def paste_file(self):
        if self.clipboard_path and os.path.exists(self.clipboard_path):
            dest = os.path.join(self.current_local_dir, os.path.basename(self.clipboard_path))
            if os.path.isdir(self.clipboard_path):
                shutil.copytree(self.clipboard_path, dest, dirs_exist_ok=True)
            else:
                shutil.copy2(self.clipboard_path, dest)
            self.refresh_local_tree()

    def delete_file(self):
        selected = self.local_tree.selection()
        if selected:
            filepath = os.path.join(self.current_local_dir, selected[0])
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete {filepath}?"):
                try:
                    if os.path.isdir(filepath):
                        shutil.rmtree(filepath)
                    else:
                        os.remove(filepath)
                    self.refresh_local_tree()
                except Exception as e:
                    messagebox.showerror("Error", str(e))

    def open_in_notepad(self):
        selected = self.local_tree.selection()
        if selected:
            filepath = os.path.join(self.current_local_dir, selected[0])
            if os.path.isfile(filepath):
                subprocess.Popen(["notepad.exe", filepath])

    def open_sftp_frame(self):
        if not self.sftp_connected:
            try:
                host = self.host_entry.get()
                port = int(self.port_entry.get())
                username = self.username_entry.get()
                password = self.password_entry.get()

                transport = paramiko.Transport((host, port))
                transport.connect(username=username, password=password)
                self.sftp_client = paramiko.SFTPClient.from_transport(transport)
                self.sftp_connected = True
            except Exception as e:
                messagebox.showerror("Connection Failed", str(e))
                return

        sftp_window = tk.Toplevel(self)
        sftp_window.title("Remote SFTP Browser")
        sftp_window.geometry("600x400")

        ttk.Label(sftp_window, text="Remote Folder Contents").pack()
        self.remote_tree = ttk.Treeview(sftp_window, columns=("name",), show="tree")
        self.remote_tree.pack(fill=tk.BOTH, expand=True)

        self.refresh_remote_tree("/")

    def refresh_remote_tree(self, path):
        for item in self.remote_tree.get_children():
            self.remote_tree.delete(item)
        try:
            for filename in self.sftp_client.listdir(path):
                self.remote_tree.insert("", "end", text=filename)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to list directory:
{str(e)}")

if __name__ == "__main__":
    app = SettingsManagerApp()
    app.mainloop()
