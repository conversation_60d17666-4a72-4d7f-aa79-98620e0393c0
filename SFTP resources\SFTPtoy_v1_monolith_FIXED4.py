
import os
import json
import threading
import time
import tkinter as tk
from datetime import datetime, timedelta
import customtkinter as ctk
from tkinter import filedialog, messagebox
from tkcalendar import Calendar
import shutil

# --- Context and Encryption Stubs ---
class AppContext:
    def __init__(self):
        self.encryption_key = "dummy"

# --- Upload Tab ---
class UploadTab(ctk.CTkFrame):
    def __init__(self, master, context):
        super().__init__(master)
        self.context = context
        ctk.CTkLabel(self, text="Upload Zone").pack(pady=10)
        self.upload_button = ctk.CTkButton(self, text="📤 Upload File", command=self.simulate_upload)
        self.upload_button.pack()

    def simulate_upload(self):
        print("[Upload] Pretend uploading...")

# --- Credential Manager ---
class CredentialManagerTab(ctk.CTkFrame):
    def __init__(self, master, context):
        super().__init__(master)
        self.context = context
        ctk.CTkLabel(self, text="Credential Manager").pack(pady=10)
        self.listbox = ctk.CTkTextbox(self, height=100)
        self.listbox.insert("end", "Example credentials")
        self.listbox.pack(pady=5)

# --- SoundFX Tab ---
class SoundFXTab(ctk.CTkFrame):
    def __init__(self, master, context):
        super().__init__(master)
        self.context = context
        ctk.CTkLabel(self, text="Sound FX Zone").pack(pady=10)
        ctk.CTkButton(self, text="Play Ding", command=self.play_fx).pack(pady=5)

    def play_fx(self):
        print("[SFX] Ding!")

# --- Settings Tab ---
class SettingsTab(ctk.CTkFrame):
    def __init__(self, master, context):
        super().__init__(master)
        self.context = context
        self.night_mode_var = tk.BooleanVar(value=False)
        self.status_label = ctk.CTkLabel(self, text="Night Mode is OFF")
        ctk.CTkLabel(self, text="Settings", font=("Arial", 18)).pack(pady=10)
        switch = ctk.CTkSwitch(self, text="Toggle Night Mode", variable=self.night_mode_var, command=self.toggle_night)
        switch.pack(pady=5)
        self.status_label.pack(pady=5)

    def toggle_night(self):
        if self.night_mode_var.get():
            ctk.set_appearance_mode("dark")
            self.status_label.configure(text="Night Mode is ON")
        else:
            ctk.set_appearance_mode("light")
            self.status_label.configure(text="Night Mode is OFF")

# --- Clock Tab ---
class ClockTab(ctk.CTkFrame):
    def __init__(self, master, context):
        super().__init__(master)
        self.context = context
        self.local_time = ctk.CTkLabel(self, text="", font=("Arial", 32))
        self.offset_time = ctk.CTkLabel(self, text="", font=("Arial", 20))
        self.offset = 0
        self.local_time.pack(pady=5)
        self.offset_time.pack(pady=5)
        self.update_clock()

    def update_clock(self):
        now = datetime.now()
        self.local_time.configure(text="Local: " + now.strftime("%H:%M:%S"))
        offset_now = now.hour + self.offset
        self.offset_time.configure(text=f"Offset ({self.offset:+}h): {offset_now % 24:02}:{now.minute:02}:{now.second:02}")
        self.after(1000, self.update_clock)

# --- Status Bar ---
class StatusBar(ctk.CTkFrame):
    def __init__(self, master):
        super().__init__(master, height=20)
        self.label = ctk.CTkLabel(self, text="Ready", anchor="w")
        self.label.pack(side="left", padx=8)

    def update_status(self, msg):
        self.label.configure(text=msg)

# --- Dev Tools Tab ---
class DevToolsTab(ctk.CTkFrame):
    def __init__(self, master, context):
        super().__init__(master)
        ctk.CTkLabel(self, text="Dev Mode Tools").pack(pady=10)
        ctk.CTkButton(self, text="Enable Debug", command=self.debug).pack()

    def debug(self):
        print("[Dev] Debug clicked")

# --- Scheduler Core ---
class SmartScheduler:
    def __init__(self, context, event_list):
        self.context = context
        self.event_list = event_list
        self.thread = None
        self.stop_flag = False

    def start(self):
        self.stop_flag = False
        if self.thread and self.thread.is_alive():
            return
        self.thread = threading.Thread(target=self._run, daemon=True)
        self.thread.start()

    def stop(self):
        self.stop_flag = True

    def _run(self):
        while not self.stop_flag:
            now = datetime.now()
            upcoming = [e for e in self.event_list if e.get("enabled", True) and self._parse_time(e["time"]) > now]
            if not upcoming:
                time.sleep(60)
                continue
            upcoming.sort(key=lambda e: self._parse_time(e["time"]))
            next_event = upcoming[0]
            next_time = self._parse_time(next_event["time"])
            wait_seconds = (next_time - now).total_seconds()
            if wait_seconds > 5:
                time.sleep(min(wait_seconds, 300))
            else:
                self._execute_event(next_event)
                time.sleep(1)

    def _parse_time(self, time_str):
        try:
            return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            return datetime.now()

    def _execute_event(self, event):
        print(f"[Scheduler] Running: {event['name']}")

# --- Scheduler Tab ---
class SchedulerTab(ctk.CTkFrame):
    def __init__(self, master, context):
        super().__init__(master)
        self.context = context
        self.events = [
            {"name": "Night Upload", "time": "2099-12-31 23:55:00", "enabled": True},
            {"name": "Backup Config", "time": "2099-12-31 00:10:00", "enabled": True}
        ]
        ctk.CTkLabel(self, text="Scheduler").pack(pady=6)
        self.run_now_btn = ctk.CTkButton(self, text="▶️ Run Now", command=self.run_now)
        self.run_now_btn.pack(pady=4)
        self.statusbox = ctk.CTkTextbox(self, height=160)
        self.statusbox.pack(pady=5, fill="both", expand=True)
        self.scheduler = SmartScheduler(context, self.events)
        self.scheduler.start()

    def run_now(self):
        self.statusbox.insert("end", "[Manual] Triggering scheduler...")
        for event in self.events:
            if event["enabled"]:
                self.scheduler._execute_event(event)
        self.statusbox.see("end")

# --- Calendar + Plugin + Export ---
class CalendarTab(ctk.CTkFrame):
    def __init__(self, master, context, scheduler_ref):
        super().__init__(master)
        self.context = context
        self.scheduler = scheduler_ref
        self.cal = Calendar(self, selectmode='day')
        self.cal.pack(pady=10)
        ctk.CTkButton(self, text="🔄 Highlight", command=self.highlight).pack()
        self.output = ctk.CTkTextbox(self, height=100)
        self.output.pack(pady=5, fill="both", expand=True)

    def highlight(self):
        self.output.delete("1.0", "end")
        for evt in self.scheduler.event_list:
            dt = self.scheduler._parse_time(evt['time'])
            tag = dt.strftime("%Y-%m-%d")
            self.output.insert("end", f"• {evt['name']} on {tag}")

class PluginSchedulerBridge:
    def __init__(self, context):
        self.context = context
        self.plugins = []

    def register_plugin(self, plugin_func, label="Unnamed"):
        self.plugins.append({"label": label, "func": plugin_func})

    def run_plugins(self):
        for p in self.plugins:
            try:
                print(f"[Plugin] Running: {p['label']}")
                p["func"](self.context)
            except Exception as e:
                print(f"[Plugin] ❌ {e}")

# --- App Shell ---
class SFTP3App(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("SFTP3 - Full Monolith")
        self.geometry("1000x720")
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        self.context = AppContext()
        self.tabview = ctk.CTkTabview(self)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=10)
        self.status_bar = StatusBar(self)
        self.status_bar.pack(fill="x", side="bottom")

        self.tabs = {
            "Upload": UploadTab(self.tabview, self.context),
            "Credentials": CredentialManagerTab(self.tabview, self.context),
            "SoundFX": SoundFXTab(self.tabview, self.context),
            "Scheduler": SchedulerTab(self.tabview, self.context),
            "Settings": SettingsTab(self.tabview, self.context),
            "Clock": ClockTab(self.tabview, self.context),
            "DevTools": DevToolsTab(self.tabview, self.context),
        }

        for name, widget in self.tabs.items():
            self.tabview.add(name)
            widget.pack(fill="both", expand=True)

if __name__ == "__main__":
    app = SFTP3App()
    app.mainloop()