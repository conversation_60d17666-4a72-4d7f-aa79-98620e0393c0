"""
SFTP Application - Clean Implementation v2.0
===========================================

Based on the magical features from sftptoy35_46_main_cmLS_refactored.py
but with clean architecture and NO FLASHING themes.
"""

import os
import json
import threading
import time
import socket
from datetime import datetime, timedelta
import customtkinter as ctk
from tkinter import filedialog, messagebox
import logging
from cryptography.fernet import Fernet
import hashlib
import base64
import calendar

# ============================================================================
# STARTUP THEME CONFIGURATION (SET ONCE, NEVER CHANGE)
# ============================================================================

def load_startup_theme():
    """Load theme preference and set it ONCE at startup"""
    try:
        if os.path.exists("sftp_config.json"):
            with open("sftp_config.json", 'r') as f:
                config = json.load(f)
            night_mode = config.get("ui", {}).get("night_mode", False)
        else:
            night_mode = False
        
        if night_mode:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")
        
        return night_mode
    except Exception:
        ctk.set_appearance_mode("light")
        return False

# Apply theme at import time - NEVER CHANGE AGAIN
STARTUP_NIGHT_MODE = load_startup_theme()
ctk.set_default_color_theme("blue")

# ============================================================================
# CORE COMPONENTS
# ============================================================================

class ConfigManager:
    """Centralized configuration with encryption"""
    
    def __init__(self):
        self.config_file = "sftp_config.json"
        self.key_file = "sftp_key.key"
        self.config = {}
        self.fernet = self._get_fernet()
        self.load_config()
    
    def _get_fernet(self):
        if not os.path.exists(self.key_file):
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
        with open(self.key_file, 'rb') as f:
            return Fernet(f.read())
    
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
            except Exception as e:
                logging.error(f"Config load error: {e}")
                self.config = self._default_config()
        else:
            self.config = self._default_config()
            self.save_config()
    
    def save_config(self):
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            logging.error(f"Config save error: {e}")
    
    def _default_config(self):
        return {
            "credentials": {"sftp": [], "lan": [], "email": []},
            "ui": {"night_mode": STARTUP_NIGHT_MODE, "window_geometry": "1400x910"},
            "schedules": [],
            "user_labels": ["Label 1", "Label 2", "Label 3", "Label 4", "Label 5"]
        }
    
    def encrypt_password(self, password):
        return self.fernet.encrypt(password.encode()).decode()
    
    def decrypt_password(self, encrypted_password):
        try:
            return self.fernet.decrypt(encrypted_password.encode()).decode()
        except Exception:
            return encrypted_password

class SFTPManager:
    """Advanced SFTP with connection testing"""
    
    def __init__(self):
        self.connected = False
        self.client = None
        self.transport = None
    
    def test_connection(self, host, port=22, timeout=5):
        """Test basic connectivity"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def test_fake_endpoint(self, host):
        """Test fake endpoints for demo"""
        fake_hosts = ["sftp.example.com", "backup.example.com", "*************", "*********"]
        if host in fake_hosts:
            time.sleep(2)  # Simulate connection delay
            if "example.com" in host:
                return {"success": True, "message": "Connected to fake SFTP server"}
            elif "*************" in host:
                return {"success": False, "message": "Connection timeout (fake)"}
            else:
                return {"success": False, "message": "Connection refused (fake)"}
        return None
    
    def connect_test(self, host, port=22, username="", password=""):
        """Test connection with detailed results"""
        # Check if fake endpoint
        fake_result = self.test_fake_endpoint(host)
        if fake_result:
            return fake_result
        
        # Test real endpoint
        if self.test_connection(host, port):
            return {"success": True, "message": f"Port {port} accessible on {host}"}
        else:
            return {"success": False, "message": f"Cannot connect to {host}:{port}"}

class ThemeManager:
    """SAFE theme management - NO DYNAMIC SWITCHING"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.current_theme = "dark" if STARTUP_NIGHT_MODE else "light"
    
    def save_theme_preference(self, night_mode):
        """Save preference for next restart - DO NOT APPLY NOW"""
        if "ui" not in self.config_manager.config:
            self.config_manager.config["ui"] = {}
        self.config_manager.config["ui"]["night_mode"] = night_mode
        self.config_manager.save_config()
        return f"Theme preference saved: {'dark' if night_mode else 'light'} (restart required)"

# ============================================================================
# MAIN APPLICATION
# ============================================================================

class SFTPApp(ctk.CTk):
    """Clean SFTP Application"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.config_manager = ConfigManager()
        self.theme_manager = ThemeManager(self.config_manager)
        self.sftp_manager = SFTPManager()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sftp_clean.log'),
                logging.StreamHandler()
            ]
        )
        
        # Setup window
        self.title("V1's Setting Doofer - Clean v2.0")
        geometry = self.config_manager.config.get("ui", {}).get("window_geometry", "1400x910")
        self.geometry(geometry)
        
        # UI state
        self.status_var = ctk.StringVar(value="Ready - Clean Architecture Loaded")
        self.night_mode_var = ctk.BooleanVar(value=STARTUP_NIGHT_MODE)

        # Track scheduled callbacks for cleanup
        self.scheduled_callbacks = []
        self.is_closing = False

        # Create UI
        self.create_ui()

        # Setup proper cleanup on window close
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        logging.info(f"SFTP Clean App started - Theme: {self.theme_manager.current_theme}")
    
    def create_ui(self):
        """Create the user interface"""
        # Main container
        self.main_container = ctk.CTkFrame(self)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Left panel
        self.create_left_panel()
        
        # Right panel with tabs
        self.create_right_panel()
        
        # Status bar
        self.create_status_bar()
    
    def create_left_panel(self):
        """Create left panel with file browser and labels"""
        self.left_panel = ctk.CTkFrame(self.main_container)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        
        # Quick labels section
        labels_frame = ctk.CTkFrame(self.left_panel)
        labels_frame.pack(fill="x", padx=5, pady=5)
        
        ctk.CTkLabel(labels_frame, text="Quick Labels (Double-click to edit):", 
                    font=ctk.CTkFont(size=12, weight="bold")).pack(anchor="w", padx=5, pady=2)
        
        # Create editable labels
        self.create_editable_labels(labels_frame)
        
        # Local files section
        local_frame = ctk.CTkFrame(self.left_panel)
        local_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        ctk.CTkLabel(local_frame, text="Local Files", 
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        # File browser placeholder
        self.local_files_text = ctk.CTkTextbox(local_frame, height=200)
        self.local_files_text.pack(fill="both", expand=True, padx=5, pady=5)
        self.local_files_text.insert("1.0", "Local file browser will be implemented here...")
    
    def create_editable_labels(self, parent):
        """Create editable labels"""
        saved_labels = self.config_manager.config.get("user_labels", 
                                                     ["Label 1", "Label 2", "Label 3", "Label 4", "Label 5"])
        
        self.label_widgets = []
        
        for i, label_text in enumerate(saved_labels):
            label_frame = ctk.CTkFrame(parent)
            label_frame.pack(fill="x", padx=5, pady=2)
            
            label_widget = ctk.CTkLabel(label_frame, text=label_text, 
                                       font=ctk.CTkFont(size=11),
                                       cursor="hand2")
            label_widget.pack(side="left", padx=5, pady=2)
            
            # Bind double-click for editing
            label_widget.bind("<Double-Button-1>", lambda e, idx=i: self.edit_label(idx))
            
            self.label_widgets.append(label_widget)
            
            # Arrow indicator
            ctk.CTkLabel(label_frame, text="→", 
                        font=ctk.CTkFont(size=10),
                        text_color="gray").pack(side="right", padx=5)
    
    def edit_label(self, label_index):
        """Edit a label"""
        current_text = self.label_widgets[label_index].cget("text")
        
        dialog = ctk.CTkInputDialog(text=f"Edit Label {label_index + 1}:", title="Edit Label")
        dialog._entry.insert(0, current_text)
        dialog._entry.select_range(0, 'end')
        
        new_text = dialog.get_input()
        
        if new_text and new_text.strip():
            self.label_widgets[label_index].configure(text=new_text.strip())
            
            # Save to config
            current_labels = [widget.cget("text") for widget in self.label_widgets]
            self.config_manager.config["user_labels"] = current_labels
            self.config_manager.save_config()
            
            self.update_status(f"Label updated: {new_text.strip()}")
    
    def create_right_panel(self):
        """Create right panel with tabs"""
        self.right_panel = ctk.CTkFrame(self.main_container)
        self.right_panel.pack(side="right", fill="both", expand=True, padx=5, pady=5)
        
        # Tabview
        self.tabview = ctk.CTkTabview(self.right_panel)
        self.tabview.pack(fill="both", expand=True)
        
        # Create tabs
        self.create_sftp_tab()
        self.create_scheduler_tab()
        self.create_settings_tab()
        self.create_credentials_tab()
    
    def create_sftp_tab(self):
        """Create SFTP tab with connection testing"""
        sftp_tab = self.tabview.add("SFTP")
        
        # Connection section
        conn_frame = ctk.CTkFrame(sftp_tab)
        conn_frame.pack(fill="x", padx=5, pady=5)
        
        ctk.CTkLabel(conn_frame, text="SFTP Connection Testing", 
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        # Test buttons
        button_frame = ctk.CTkFrame(conn_frame)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        ctk.CTkButton(button_frame, text="Test SFTP Connection", 
                     command=self.test_sftp_connection).pack(side="left", padx=5, pady=5)
        
        ctk.CTkButton(button_frame, text="Test LAN Connection", 
                     command=self.test_lan_connection).pack(side="left", padx=5, pady=5)
        
        ctk.CTkButton(button_frame, text="Test All Endpoints", 
                     command=self.test_all_endpoints).pack(side="left", padx=5, pady=5)
        
        # Results area
        self.sftp_results = ctk.CTkTextbox(sftp_tab, height=300)
        self.sftp_results.pack(fill="both", expand=True, padx=5, pady=5)
        self.sftp_results.insert("1.0", "Connection test results will appear here...\n\n")
        self.sftp_results.insert("end", "Available test endpoints:\n")
        self.sftp_results.insert("end", "• sftp.example.com (fake - success)\n")
        self.sftp_results.insert("end", "• backup.example.com (fake - success)\n")
        self.sftp_results.insert("end", "• ************* (fake - timeout)\n")
        self.sftp_results.insert("end", "• ********* (fake - refused)\n")
    
    def create_scheduler_tab(self):
        """Create scheduler tab"""
        sched_tab = self.tabview.add("Scheduler")
        
        ctk.CTkLabel(sched_tab, text="Task Scheduler", 
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=10)
        
        # Calendar placeholder
        calendar_frame = ctk.CTkFrame(sched_tab)
        calendar_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.calendar_text = ctk.CTkTextbox(calendar_frame)
        self.calendar_text.pack(fill="both", expand=True, padx=5, pady=5)
        self.calendar_text.insert("1.0", "Calendar widget will be implemented here...\n\n")
        self.calendar_text.insert("end", "Features:\n")
        self.calendar_text.insert("end", "• Visual calendar with event indicators\n")
        self.calendar_text.insert("end", "• Schedule creation and editing\n")
        self.calendar_text.insert("end", "• Recurring task support\n")
        self.calendar_text.insert("end", "• Email notifications\n")
    
    def create_settings_tab(self):
        """Create settings tab with SAFE theme switching"""
        settings_tab = self.tabview.add("Settings")
        
        ctk.CTkLabel(settings_tab, text="Application Settings", 
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=10)
        
        # Theme section
        theme_frame = ctk.CTkFrame(settings_tab)
        theme_frame.pack(fill="x", padx=5, pady=5)
        
        ctk.CTkLabel(theme_frame, text="Theme Settings", 
                    font=ctk.CTkFont(size=12, weight="bold")).pack(anchor="w", padx=5, pady=2)
        
        # SAFE theme switch
        self.night_mode_switch = ctk.CTkSwitch(theme_frame, 
                                              text="Night Mode (Restart Required)", 
                                              variable=self.night_mode_var,
                                              command=self.safe_theme_change)
        self.night_mode_switch.pack(anchor="w", padx=20, pady=5)
        
        # Warning
        warning_label = ctk.CTkLabel(theme_frame, 
                                   text="⚠️ Theme changes require restart to prevent flashing",
                                   font=ctk.CTkFont(size=10),
                                   text_color="orange")
        warning_label.pack(anchor="w", padx=40, pady=2)
        
        # Other settings placeholder
        other_frame = ctk.CTkFrame(settings_tab)
        other_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.settings_text = ctk.CTkTextbox(other_frame)
        self.settings_text.pack(fill="both", expand=True, padx=5, pady=5)
        self.settings_text.insert("1.0", "Additional settings will be implemented here...\n\n")
        self.settings_text.insert("end", "Features:\n")
        self.settings_text.insert("end", "• SFTP connection settings\n")
        self.settings_text.insert("end", "• Email notification settings\n")
        self.settings_text.insert("end", "• Security and app lock settings\n")
        self.settings_text.insert("end", "• Debug and logging options\n")
    
    def create_credentials_tab(self):
        """Create credentials management tab"""
        cred_tab = self.tabview.add("Credentials")
        
        ctk.CTkLabel(cred_tab, text="Credential Manager", 
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=10)
        
        # Credentials placeholder
        cred_frame = ctk.CTkFrame(cred_tab)
        cred_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.cred_text = ctk.CTkTextbox(cred_frame)
        self.cred_text.pack(fill="both", expand=True, padx=5, pady=5)
        self.cred_text.insert("1.0", "Credential management will be implemented here...\n\n")
        self.cred_text.insert("end", "Features:\n")
        self.cred_text.insert("end", "• Encrypted credential storage\n")
        self.cred_text.insert("end", "• SFTP, LAN, and Email credentials\n")
        self.cred_text.insert("end", "• Single default selection per type\n")
        self.cred_text.insert("end", "• Import/Export capabilities\n")
    
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ctk.CTkFrame(self)
        status_frame.pack(fill="x", side="bottom", padx=10, pady=5)
        
        self.status_label = ctk.CTkLabel(status_frame, textvariable=self.status_var)
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Theme indicator
        theme_text = f"Theme: {self.theme_manager.current_theme}"
        self.theme_label = ctk.CTkLabel(status_frame, text=theme_text)
        self.theme_label.pack(side="right", padx=10, pady=5)
    
    def update_status(self, message):
        """Update status bar"""
        self.status_var.set(message)
        logging.info(f"Status: {message}")
    
    def safe_theme_change(self):
        """Save theme preference without changing it immediately"""
        message = self.theme_manager.save_theme_preference(self.night_mode_var.get())
        self.update_status(message)
        
        # Show restart reminder
        self.after(100, self.show_restart_reminder)
    
    def show_restart_reminder(self):
        """Show restart reminder"""
        original_status = self.status_var.get()
        self.update_status("Restart the application to apply theme changes safely")

        # Schedule callback with cleanup tracking
        callback_id = self.after(5000, lambda: self.safe_update_status(original_status))
        self.scheduled_callbacks.append(callback_id)

    def safe_update_status(self, message):
        """Safely update status if window still exists"""
        if not self.is_closing:
            try:
                self.update_status(message)
            except Exception:
                pass  # Widget destroyed, ignore

    def safe_after(self, delay, callback):
        """Schedule callback with cleanup tracking"""
        if not self.is_closing:
            callback_id = self.after(delay, callback)
            self.scheduled_callbacks.append(callback_id)
            return callback_id
        return None

    def on_closing(self):
        """Clean shutdown procedure"""
        self.is_closing = True

        # Cancel all scheduled callbacks
        for callback_id in self.scheduled_callbacks:
            try:
                self.after_cancel(callback_id)
            except Exception:
                pass  # Already executed or invalid

        self.scheduled_callbacks.clear()

        # Save configuration
        try:
            self.config_manager.save_config()
        except Exception as e:
            logging.error(f"Failed to save config on exit: {e}")

        logging.info("Application closing cleanly")

        # Destroy window
        self.destroy()
    
    def test_sftp_connection(self):
        """Test SFTP connection"""
        if self.is_closing:
            return

        self.update_status("Testing SFTP connections...")

        # Test fake endpoints
        test_hosts = ["sftp.example.com", "backup.example.com", "*************", "*********"]

        try:
            self.sftp_results.delete("1.0", "end")
            self.sftp_results.insert("1.0", "SFTP Connection Test Results:\n")
            self.sftp_results.insert("end", "=" * 40 + "\n\n")

            for host in test_hosts:
                if self.is_closing:
                    return

                self.sftp_results.insert("end", f"Testing {host}...\n")
                result = self.sftp_manager.connect_test(host)

                if result["success"]:
                    self.sftp_results.insert("end", f"✅ {result['message']}\n")
                else:
                    self.sftp_results.insert("end", f"❌ {result['message']}\n")

                self.sftp_results.insert("end", "\n")

                # Safe UI update
                if not self.is_closing:
                    self.update()

            if not self.is_closing:
                self.update_status("SFTP connection tests completed")

        except Exception as e:
            if not self.is_closing:
                logging.error(f"SFTP test error: {e}")
                self.update_status("SFTP test interrupted")
    
    def test_lan_connection(self):
        """Test LAN connection"""
        if self.is_closing:
            return

        self.update_status("Testing LAN connections...")

        try:
            self.sftp_results.delete("1.0", "end")
            self.sftp_results.insert("1.0", "LAN Connection Test Results:\n")
            self.sftp_results.insert("end", "=" * 40 + "\n\n")

            # Test local network addresses
            test_hosts = ["***********", "*************", "********", "127.0.0.1"]

            for host in test_hosts:
                if self.is_closing:
                    return

                self.sftp_results.insert("end", f"Testing {host}...\n")

                if self.sftp_manager.test_connection(host, 80, timeout=2):
                    self.sftp_results.insert("end", f"✅ {host} is reachable\n")
                else:
                    self.sftp_results.insert("end", f"❌ {host} is not reachable\n")

                self.sftp_results.insert("end", "\n")

                # Safe UI update
                if not self.is_closing:
                    self.update()

            if not self.is_closing:
                self.update_status("LAN connection tests completed")

        except Exception as e:
            if not self.is_closing:
                logging.error(f"LAN test error: {e}")
                self.update_status("LAN test interrupted")
    
    def test_all_endpoints(self):
        """Test all endpoints"""
        self.update_status("Testing all endpoints...")

        # Run both tests with safe callbacks
        self.test_sftp_connection()
        self.safe_after(2000, self.test_lan_connection)

        self.safe_after(4000, lambda: self.safe_update_status("All endpoint tests completed"))

if __name__ == "__main__":
    app = SFTPApp()
    app.mainloop()
