import os
import json
import threading
import time
import tkinter as tk
from datetime import datetime, timedelta
import customtkinter as ctk
from tkinter import filedialog, messagebox
import paramiko
import logging
from PIL import Image
import pystray
import sys
import calendar
from tkinter import ttk

# Configure CustomTkinter appearance - SET ONCE AND NEVER CHANGE
# Load saved theme preference
try:
    import json
    if os.path.exists("sftp_config.json"):
        with open("sftp_config.json", 'r') as f:
            config = json.load(f)
        if config.get("night_mode", False):
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")
    else:
        ctk.set_appearance_mode("light")  # Default to light
except:
    ctk.set_appearance_mode("light")  # Fallback to light

ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

# Custom color themes for smooth transitions
THEME_COLORS = {
    "light_dim": {
        "bg_color": "#f0f0f0",
        "fg_color": "#e8e8e8",
        "text_color": "#2b2b2b",
        "button_color": "#d4d4d4",
        "button_hover": "#c0c0c0"
    },
    "dark_smooth": {
        "bg_color": "#1a1a1a",
        "fg_color": "#2b2b2b",
        "text_color": "#e0e0e0",
        "button_color": "#3a3a3a",
        "button_hover": "#4a4a4a"
    },
    "transition": {
        "bg_color": "#808080",
        "fg_color": "#707070",
        "text_color": "#ffffff",
        "button_color": "#606060",
        "button_hover": "#505050"
    }
}

# Constants
CONFIG_FILE = "sftp_config.json"
KEY_FILE = "sftp_key.key"
EMAIL_CONFIG_FILE = "email_config.json"
SCHEDULE_CONFIG_FILE = "schedule_config.json"
NOTIFICATION_CONFIG_FILE = "notification_settings.json"
LOG_FILE = "v1s_setting_doofer.log"
DEBUG_LOG_FILE = "v1s_debug_doofer.log"

# Debug tier system
class DebugLogger:
    LEVELS = {
        "CRITICAL": 50,
        "ERROR": 40,
        "WARNING": 30,
        "INFO": 20,
        "DEBUG": 10,
        "TRACE": 5,
        "ALL": 0
    }
    
    def __init__(self, level="INFO"):
        self.level = self.LEVELS.get(level, 20)
        self.setup_logger()
        
    def setup_logger(self):
        self.logger = logging.getLogger("SFTPToy")
        self.logger.setLevel(logging.DEBUG)
        
        # File handler for all logs
        file_handler = logging.FileHandler(LOG_FILE)
        file_handler.setLevel(logging.DEBUG)
        
        # Debug file handler for detailed logs
        debug_handler = logging.FileHandler(DEBUG_LOG_FILE)
        debug_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.level)
        
        # Formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        debug_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(debug_handler)
        self.logger.addHandler(console_handler)
    
    def log(self, level, message):
        if self.LEVELS.get(level, 0) >= self.level:
            getattr(self.logger, level.lower())(message)
    
    def trace(self, message):
        if self.level <= 5:
            self.logger.debug(f"TRACE: {message}")
    
    def set_level(self, level):
        self.level = self.LEVELS.get(level, 20)
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                handler.setLevel(self.level)

# Config Manager
class ConfigManager:
    def __init__(self):
        self.config = self.load_config()
        
    def load_config(self):
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"Failed to load config: {e}")
        return {}
    
    def save_config(self):
        try:
            with open(CONFIG_FILE, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            logging.error(f"Failed to save config: {e}")

# Main Application
class SFTPApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("V1's Setting Doofer v1.0")
        self.geometry("1400x910")
        
        # Initialize components
        self.debug = DebugLogger("INFO")
        self.config_manager = ConfigManager()
        
        # Variables
        self.night_mode_var = ctk.BooleanVar(value=self.config_manager.config.get("night_mode", False))
        self.scheduler_running = ctk.BooleanVar(value=not self.config_manager.config.get("scheduler_paused", False))
        self.honor_sftp_limits = ctk.BooleanVar(value=self.config_manager.config.get("honor_sftp_limits", True))
        self.sftp_enabled = ctk.BooleanVar(value=self.config_manager.config.get("sftp_enabled", True))
        self.enable_dir_copy_var = ctk.BooleanVar(value=self.config_manager.config.get("enable_dir_copy", True))
        self.bright_colors_var = ctk.BooleanVar(value=False)

        # Theme transition variables
        self.is_transitioning = False
        self.transition_steps = 15  # More steps for smoother transition
        self.transition_delay = 30  # Faster steps for more fluid motion
        self.current_step = 0
        self.transition_target = None

        # Tab switching variables
        self.last_tab = None
        self.tab_switch_delay = 100  # Delay for tab content loading
        
        # SFTP client
        self.sftp_client = None
        self.local_path = os.getcwd()
        self.remote_path = None
        
        # Create UI
        self.create_menu()
        self.create_main_layout()
        
        # Theme is already applied at startup - no need to change it here
        
        # Initialize
        self.debug.log("INFO", "Application started")
    
    def create_menu(self):
        # We'll need to implement a custom menu since CTk doesn't have built-in menus
        self.menu_frame = ctk.CTkFrame(self, height=30)
        self.menu_frame.pack(fill="x", side="top")
        
        # File menu button
        self.file_menu_button = ctk.CTkButton(self.menu_frame, text="File", width=60, 
                                             command=self.show_file_menu)
        self.file_menu_button.pack(side="left", padx=5)
        
        # View menu button
        self.view_menu_button = ctk.CTkButton(self.menu_frame, text="View", width=60,
                                             command=self.show_view_menu)
        self.view_menu_button.pack(side="left", padx=5)
        
        # SFTP menu button
        self.sftp_menu_button = ctk.CTkButton(self.menu_frame, text="SFTP", width=60,
                                             command=self.show_sftp_menu)
        self.sftp_menu_button.pack(side="left", padx=5)
        
        # Help menu button
        self.help_menu_button = ctk.CTkButton(self.menu_frame, text="Help", width=60,
                                             command=self.show_help_menu)
        self.help_menu_button.pack(side="left", padx=5)
    
    def create_main_layout(self):
        # Main container
        self.main_container = ctk.CTkFrame(self)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Left panel - File browser with editable labels
        self.left_panel = ctk.CTkFrame(self.main_container)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        # Editable labels section
        self.labels_frame = ctk.CTkFrame(self.left_panel)
        self.labels_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(self.labels_frame, text="Quick Labels (Double-click to edit):",
                    font=ctk.CTkFont(size=12, weight="bold")).pack(anchor="w", padx=5, pady=2)

        # Create editable labels
        self.create_editable_labels()

        # Local files frame
        self.local_frame = ctk.CTkFrame(self.left_panel)
        self.local_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(self.local_frame, text="Local Files").pack(pady=5)
        
        # Local file browser (we'll need a custom implementation for Treeview)
        self.local_files_frame = ctk.CTkScrollableFrame(self.local_frame)
        self.local_files_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Local path navigation
        self.path_frame = ctk.CTkFrame(self.local_frame)
        self.path_frame.pack(fill="x", padx=5, pady=5)
        
        self.path_var = ctk.StringVar(value=self.local_path)
        self.path_entry = ctk.CTkEntry(self.path_frame, textvariable=self.path_var)
        self.path_entry.pack(side="left", fill="x", expand=True, padx=5)
        
        self.browse_button = ctk.CTkButton(self.path_frame, text="Browse", 
                                          command=self.browse_local_path)
        self.browse_button.pack(side="right", padx=5)
        
        # Right panel - SFTP and Scheduler
        self.right_panel = ctk.CTkFrame(self.main_container)
        self.right_panel.pack(side="right", fill="both", expand=True, padx=5, pady=5)
        
        # Tabview for SFTP, Scheduler, etc. with graceful switching
        self.tabview = ctk.CTkTabview(self.right_panel)
        self.tabview.pack(fill="both", expand=True)

        # Bind tab change event for graceful transitions
        self.tabview.configure(command=self.on_tab_change)
        
        # SFTP Tab
        self.sftp_tab = self.tabview.add("SFTP")
        self.create_sftp_tab()
        
        # Scheduler Tab
        self.scheduler_tab = self.tabview.add("Scheduler")
        self.create_scheduler_tab()
        
        # Settings Tab
        self.settings_tab = self.tabview.add("Settings")
        self.create_settings_tab()

        # Credentials Tab
        self.credentials_tab = self.tabview.add("Credentials")
        self.create_credentials_tab()
        
        # Log frame
        self.log_frame = ctk.CTkFrame(self)
        self.log_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(self.log_frame, text="Log").pack(anchor="w")
        
        self.log_text = ctk.CTkTextbox(self.log_frame, height=60)
        self.log_text.pack(fill="x", expand=False, padx=5, pady=5)
        
        # Status bar
        self.status_var = ctk.StringVar(value="Ready")
        self.status_bar = ctk.CTkLabel(self, textvariable=self.status_var, anchor="w")
        self.status_bar.pack(fill="x", side="bottom", padx=10, pady=5)
    
    def create_sftp_tab(self):
        # Connection frame
        self.connection_frame = ctk.CTkFrame(self.sftp_tab)
        self.connection_frame.pack(fill="x", padx=5, pady=5)
        
        # Connection controls
        ctk.CTkLabel(self.connection_frame, text="SFTP Connection").pack(pady=5)
        
        self.connect_button = ctk.CTkButton(self.connection_frame, text="Connect", 
                                           command=self.connect_sftp)
        self.connect_button.pack(side="left", padx=5, pady=5)
        
        self.disconnect_button = ctk.CTkButton(self.connection_frame, text="Disconnect", 
                                              command=self.disconnect_sftp)
        self.disconnect_button.pack(side="left", padx=5, pady=5)
        
        # Remote files frame
        self.remote_frame = ctk.CTkFrame(self.sftp_tab)
        self.remote_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        ctk.CTkLabel(self.remote_frame, text="Remote Files").pack(pady=5)
        
        # Remote file browser
        self.remote_files_frame = ctk.CTkScrollableFrame(self.remote_frame)
        self.remote_files_frame.pack(fill="both", expand=True, padx=5, pady=5)
    
    def create_scheduler_tab(self):
        # Main scheduler container
        scheduler_main = ctk.CTkFrame(self.scheduler_tab)
        scheduler_main.pack(fill="both", expand=True, padx=5, pady=5)

        # Top section - Scheduler controls
        self.scheduler_controls = ctk.CTkFrame(scheduler_main)
        self.scheduler_controls.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(self.scheduler_controls, text="Scheduler Controls",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)

        # Control buttons row
        controls_row = ctk.CTkFrame(self.scheduler_controls)
        controls_row.pack(fill="x", padx=5, pady=5)

        self.pause_button = ctk.CTkButton(controls_row,
                                         text="Pause Scheduler" if self.scheduler_running.get() else "Resume Scheduler",
                                         command=self.toggle_scheduler)
        self.pause_button.pack(side="left", padx=5, pady=5)

        self.add_schedule_button = ctk.CTkButton(controls_row, text="Add Schedule",
                                               command=self.add_schedule)
        self.add_schedule_button.pack(side="left", padx=5, pady=5)

        # Schedule management buttons (like in your screenshot)
        self.save_schedules_button = ctk.CTkButton(controls_row, text="Save Schedules",
                                                  command=self.save_schedules)
        self.save_schedules_button.pack(side="left", padx=5, pady=5)

        self.load_schedules_button = ctk.CTkButton(controls_row, text="Load Schedules",
                                                  command=self.load_schedules)
        self.load_schedules_button.pack(side="left", padx=5, pady=5)

        # Middle section - Calendar and schedule list
        middle_section = ctk.CTkFrame(scheduler_main)
        middle_section.pack(fill="both", expand=True, padx=5, pady=5)

        # Left side - Scheduled tasks list
        left_schedule = ctk.CTkFrame(middle_section)
        left_schedule.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(left_schedule, text="Scheduled Tasks",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        self.schedule_list = ctk.CTkScrollableFrame(left_schedule)
        self.schedule_list.pack(fill="both", expand=True, padx=5, pady=5)

        # Right side - Calendar widget (like in your screenshot)
        right_calendar = ctk.CTkFrame(middle_section)
        right_calendar.pack(side="right", fill="y", padx=5, pady=5)

        ctk.CTkLabel(right_calendar, text="Calendar",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        self.create_calendar_widget(right_calendar)

    def create_editable_labels(self):
        """Create editable labels that users can double-click to rename"""
        # Default labels
        default_labels = ["Label 1", "Label 2", "Label 3", "Label 4", "Label 5"]

        # Load saved labels from config
        saved_labels = self.config_manager.config.get("user_labels", default_labels)

        self.label_widgets = []

        for i, label_text in enumerate(saved_labels):
            label_frame = ctk.CTkFrame(self.labels_frame)
            label_frame.pack(fill="x", padx=5, pady=2)

            # Create label that can be edited
            label_widget = ctk.CTkLabel(label_frame, text=label_text,
                                       font=ctk.CTkFont(size=11),
                                       cursor="hand2")
            label_widget.pack(side="left", padx=5, pady=2)

            # Bind double-click event for editing
            label_widget.bind("<Double-Button-1>", lambda e, idx=i: self.edit_label(idx))

            # Store reference
            self.label_widgets.append(label_widget)

            # Add arrow or indicator
            arrow_label = ctk.CTkLabel(label_frame, text="→",
                                     font=ctk.CTkFont(size=10),
                                     text_color="gray")
            arrow_label.pack(side="right", padx=5)

    def edit_label(self, label_index):
        """Allow user to edit a label by double-clicking"""
        current_text = self.label_widgets[label_index].cget("text")

        # Create a simple input dialog
        dialog = ctk.CTkInputDialog(text=f"Edit Label {label_index + 1}:",
                                   title="Edit Label")
        dialog._entry.insert(0, current_text)
        dialog._entry.select_range(0, 'end')

        new_text = dialog.get_input()

        if new_text and new_text.strip():
            # Update the label
            self.label_widgets[label_index].configure(text=new_text.strip())

            # Save to config
            current_labels = [widget.cget("text") for widget in self.label_widgets]
            self.config_manager.config["user_labels"] = current_labels
            self.config_manager.save_config()

            self.debug.log("INFO", f"Updated label {label_index + 1} to: {new_text.strip()}")
            self.update_status(f"Label updated: {new_text.strip()}")

    def create_calendar_widget(self, parent):
        """Create a calendar widget similar to the one in the screenshot"""
        # Current date
        now = datetime.now()

        # Month/Year header
        month_year_frame = ctk.CTkFrame(parent)
        month_year_frame.pack(fill="x", padx=5, pady=5)

        # Navigation buttons
        prev_button = ctk.CTkButton(month_year_frame, text="<", width=30,
                                   command=self.prev_month)
        prev_button.pack(side="left", padx=2)

        self.month_year_label = ctk.CTkLabel(month_year_frame,
                                           text=f"{calendar.month_name[now.month]} {now.year}",
                                           font=ctk.CTkFont(size=14, weight="bold"))
        self.month_year_label.pack(side="left", expand=True, padx=10)

        next_button = ctk.CTkButton(month_year_frame, text=">", width=30,
                                   command=self.next_month)
        next_button.pack(side="right", padx=2)

        # Calendar grid
        self.calendar_frame = ctk.CTkFrame(parent)
        self.calendar_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Day headers
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for i, day in enumerate(days):
            day_label = ctk.CTkLabel(self.calendar_frame, text=day,
                                   font=ctk.CTkFont(size=10, weight="bold"))
            day_label.grid(row=0, column=i, padx=1, pady=1, sticky="nsew")

        # Store current month/year for navigation
        self.current_month = now.month
        self.current_year = now.year

        # Generate calendar
        self.update_calendar()

    def update_calendar(self):
        """Update the calendar display"""
        # Clear existing calendar days (except headers)
        for widget in self.calendar_frame.winfo_children():
            if int(widget.grid_info()["row"]) > 0:
                widget.destroy()

        # Get calendar data
        cal = calendar.monthcalendar(self.current_year, self.current_month)

        # Create day buttons
        for week_num, week in enumerate(cal, 1):
            for day_num, day in enumerate(week):
                if day == 0:
                    # Empty cell for days from other months
                    empty_label = ctk.CTkLabel(self.calendar_frame, text="")
                    empty_label.grid(row=week_num, column=day_num, padx=1, pady=1, sticky="nsew")
                else:
                    # Day button with hover effects
                    day_button = ctk.CTkButton(self.calendar_frame, text=str(day),
                                             width=30, height=25,
                                             command=lambda d=day: self.select_date(d))
                    day_button.grid(row=week_num, column=day_num, padx=1, pady=1, sticky="nsew")

                    # Add hover effects and schedule indicators
                    self.setup_day_hover_effects(day_button, day)

                    # Highlight today
                    if (day == datetime.now().day and
                        self.current_month == datetime.now().month and
                        self.current_year == datetime.now().year):
                        day_button.configure(fg_color="orange")

                    # Check if day has scheduled events and add indicator
                    if self.has_scheduled_events(day):
                        day_button.configure(text=f"{day}●", text_color="green")

        # Update month/year label
        self.month_year_label.configure(text=f"{calendar.month_name[self.current_month]} {self.current_year}")

        # Configure grid weights
        for i in range(7):
            self.calendar_frame.grid_columnconfigure(i, weight=1)

    def prev_month(self):
        """Navigate to previous month"""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.update_calendar()

    def next_month(self):
        """Navigate to next month"""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.update_calendar()

    def setup_day_hover_effects(self, button, day):
        """Setup hover effects for calendar day buttons"""
        def on_enter(event):
            # Show hover effect and schedule info
            button.configure(hover_color="#4a4a4a" if self.night_mode_var.get() else "#e0e0e0")

            # Show schedule info in status bar
            scheduled_events = self.get_scheduled_events(day)
            if scheduled_events:
                event_count = len(scheduled_events)
                self.update_status(f"Day {day}: {event_count} scheduled event(s) - Hover for details")
            else:
                self.update_status(f"Day {day}: No scheduled events - Click to add")

        def on_leave(event):
            # Reset status bar
            self.update_status("Ready")

        # Bind hover events
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def has_scheduled_events(self, day):
        """Check if a specific day has scheduled events"""
        # TODO: Implement actual schedule checking logic
        # For now, return True for some days as example
        return day in [5, 10, 15, 20, 25]  # Example scheduled days

    def get_scheduled_events(self, day):
        """Get scheduled events for a specific day"""
        # TODO: Implement actual event retrieval
        # For now, return mock data for demonstration
        if self.has_scheduled_events(day):
            return [
                {"time": "09:00", "task": "SFTP Backup", "type": "upload"},
                {"time": "15:00", "task": "Sync Files", "type": "sync"}
            ]
        return []

    def select_date(self, day):
        """Handle date selection"""
        selected_date = datetime(self.current_year, self.current_month, day)
        self.debug.log("INFO", f"Selected date: {selected_date.strftime('%Y-%m-%d')}")

        # Show detailed schedule info for selected date
        events = self.get_scheduled_events(day)
        if events:
            event_details = ", ".join([f"{e['time']} {e['task']}" for e in events])
            self.update_status(f"Selected {selected_date.strftime('%B %d, %Y')}: {event_details}")
        else:
            self.update_status(f"Selected {selected_date.strftime('%B %d, %Y')}: No events scheduled")

    def create_settings_tab(self):
        # General settings
        self.settings_frame = ctk.CTkFrame(self.settings_tab)
        self.settings_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        ctk.CTkLabel(self.settings_frame, text="Application Settings").pack(pady=10)
        
        # Theme settings
        ctk.CTkLabel(self.settings_frame, text="Theme Settings:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))

        # Night mode toggle - DISABLED to prevent flashing
        self.night_mode_switch = ctk.CTkSwitch(self.settings_frame, text="Night Mode (Restart Required)",
                                              variable=self.night_mode_var,
                                              command=self.safe_theme_change)
        self.night_mode_switch.pack(anchor="w", padx=20, pady=5)

        # Warning label about restart
        warning_label = ctk.CTkLabel(self.settings_frame,
                                   text="⚠️ Theme changes require app restart to prevent flashing",
                                   font=ctk.CTkFont(size=10),
                                   text_color="orange")
        warning_label.pack(anchor="w", padx=40, pady=2)

        # Brightness info
        brightness_info = ctk.CTkLabel(self.settings_frame,
                                     text="• Light Mode: Dimmed for comfortable viewing\n• Dark Mode: Easy on the eyes",
                                     font=ctk.CTkFont(size=11),
                                     text_color="gray")
        brightness_info.pack(anchor="w", padx=40, pady=(0, 10))
        
        # Debug level selection
        ctk.CTkLabel(self.settings_frame, text="Debug Level:").pack(anchor="w", padx=20, pady=(10, 0))
        
        self.debug_level_var = ctk.StringVar(value="INFO")
        self.debug_level_menu = ctk.CTkOptionMenu(self.settings_frame, 
                                                 values=list(DebugLogger.LEVELS.keys()),
                                                 variable=self.debug_level_var,
                                                 command=self.change_debug_level)
        self.debug_level_menu.pack(anchor="w", padx=20, pady=(0, 10))
        
        # SFTP settings
        ctk.CTkLabel(self.settings_frame, text="SFTP Settings:").pack(anchor="w", padx=20, pady=(10, 0))
        
        self.honor_limits_switch = ctk.CTkSwitch(self.settings_frame, text="Honor SFTP Limits", 
                                                variable=self.honor_sftp_limits)
        self.honor_limits_switch.pack(anchor="w", padx=20, pady=5)
        
        self.enable_sftp_switch = ctk.CTkSwitch(self.settings_frame, text="Enable SFTP", 
                                               variable=self.sftp_enabled)
        self.enable_sftp_switch.pack(anchor="w", padx=20, pady=5)
        
        self.dir_copy_switch = ctk.CTkSwitch(self.settings_frame, text="Enable Directory Copy", 
                                            variable=self.enable_dir_copy_var)
        self.dir_copy_switch.pack(anchor="w", padx=20, pady=5)
        
        # Save settings button
        self.save_settings_button = ctk.CTkButton(self.settings_frame, text="Save Settings", 
                                                 command=self.save_settings)
        self.save_settings_button.pack(anchor="w", padx=20, pady=20)

    def create_credentials_tab(self):
        """Create credentials manager tab with single default selection"""
        # Main credentials frame
        creds_main = ctk.CTkFrame(self.credentials_tab)
        creds_main.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(creds_main, text="Credentials Manager",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # Credentials list frame
        creds_list_frame = ctk.CTkFrame(creds_main)
        creds_list_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Headers
        headers_frame = ctk.CTkFrame(creds_list_frame)
        headers_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(headers_frame, text="Type", width=100).pack(side="left", padx=5)
        ctk.CTkLabel(headers_frame, text="Name", width=150).pack(side="left", padx=5)
        ctk.CTkLabel(headers_frame, text="Host/Server", width=200).pack(side="left", padx=5)
        ctk.CTkLabel(headers_frame, text="Default", width=80).pack(side="left", padx=5)
        ctk.CTkLabel(headers_frame, text="Actions", width=100).pack(side="left", padx=5)

        # Scrollable credentials list
        self.creds_scroll = ctk.CTkScrollableFrame(creds_list_frame)
        self.creds_scroll.pack(fill="both", expand=True, padx=5, pady=5)

        # Default selection tracking
        self.default_selections = {
            "LAN": None,
            "SFTP": None,
            "Email": None
        }

        # Add some example credentials
        self.create_sample_credentials()

        # Add new credential button
        add_cred_frame = ctk.CTkFrame(creds_main)
        add_cred_frame.pack(fill="x", padx=10, pady=5)

        self.add_cred_button = ctk.CTkButton(add_cred_frame, text="Add New Credential",
                                           command=self.add_new_credential)
        self.add_cred_button.pack(side="left", padx=5, pady=5)

    def create_sample_credentials(self):
        """Create sample credentials to demonstrate the interface"""
        sample_creds = [
            {"type": "LAN", "name": "Local Network", "host": "*************", "default": True},
            {"type": "LAN", "name": "Office Network", "host": "*********", "default": False},
            {"type": "SFTP", "name": "Main Server", "host": "sftp.example.com", "default": True},
            {"type": "SFTP", "name": "Backup Server", "host": "backup.example.com", "default": False},
            {"type": "Email", "name": "Primary Email", "host": "smtp.gmail.com", "default": True},
            {"type": "Email", "name": "Work Email", "host": "smtp.company.com", "default": False},
        ]

        for i, cred in enumerate(sample_creds):
            self.create_credential_row(cred, i)

    def create_credential_row(self, cred_data, row_index):
        """Create a single credential row with single default selection logic"""
        row_frame = ctk.CTkFrame(self.creds_scroll)
        row_frame.pack(fill="x", padx=2, pady=2)

        # Type
        type_label = ctk.CTkLabel(row_frame, text=cred_data["type"], width=100)
        type_label.pack(side="left", padx=5)

        # Name
        name_label = ctk.CTkLabel(row_frame, text=cred_data["name"], width=150)
        name_label.pack(side="left", padx=5)

        # Host
        host_label = ctk.CTkLabel(row_frame, text=cred_data["host"], width=200)
        host_label.pack(side="left", padx=5)

        # Default checkbox with single selection logic
        default_var = ctk.BooleanVar(value=cred_data["default"])
        default_check = ctk.CTkCheckBox(row_frame, text="", variable=default_var, width=80,
                                       command=lambda: self.handle_default_selection(
                                           cred_data["type"], default_var, row_index))
        default_check.pack(side="left", padx=5)

        # Store reference for default management
        if cred_data["default"]:
            self.default_selections[cred_data["type"]] = row_index

        # Actions
        actions_frame = ctk.CTkFrame(row_frame)
        actions_frame.pack(side="left", padx=5)

        edit_btn = ctk.CTkButton(actions_frame, text="Edit", width=45, height=25,
                               command=lambda: self.edit_credential(row_index))
        edit_btn.pack(side="left", padx=2)

        delete_btn = ctk.CTkButton(actions_frame, text="Del", width=45, height=25,
                                 command=lambda: self.delete_credential(row_index))
        delete_btn.pack(side="left", padx=2)

        # Store row data for management
        setattr(row_frame, 'cred_data', cred_data)
        setattr(row_frame, 'default_var', default_var)
        setattr(row_frame, 'row_index', row_index)

    def handle_default_selection(self, cred_type, selected_var, row_index):
        """Ensure only one default can be selected per credential type"""
        if selected_var.get():
            # If this is being set as default, unset any previous default
            previous_default = self.default_selections.get(cred_type)
            if previous_default is not None and previous_default != row_index:
                # Find and unset the previous default
                for widget in self.creds_scroll.winfo_children():
                    if (hasattr(widget, 'cred_data') and
                        widget.cred_data["type"] == cred_type and
                        widget.row_index == previous_default):
                        widget.default_var.set(False)
                        break

            # Set this as the new default
            self.default_selections[cred_type] = row_index
            self.debug.log("INFO", f"Set {cred_type} default to row {row_index}")
            self.update_status(f"Set default {cred_type} credential")
        else:
            # If unchecking, remove from defaults
            if self.default_selections.get(cred_type) == row_index:
                self.default_selections[cred_type] = None
                self.debug.log("INFO", f"Removed {cred_type} default")
                self.update_status(f"Removed default {cred_type} credential")

    def add_new_credential(self):
        """Add a new credential"""
        self.debug.log("INFO", "Adding new credential...")
        self.update_status("Add credential feature coming soon...")

    def edit_credential(self, row_index):
        """Edit an existing credential"""
        self.debug.log("INFO", f"Editing credential at row {row_index}")
        self.update_status(f"Edit credential feature coming soon...")

    def delete_credential(self, row_index):
        """Delete a credential"""
        self.debug.log("INFO", f"Deleting credential at row {row_index}")
        self.update_status(f"Delete credential feature coming soon...")

    # Menu functions
    def show_file_menu(self):
        # Implement popup menu
        pass
    
    def show_view_menu(self):
        # Implement popup menu
        pass
    
    def show_sftp_menu(self):
        # Implement popup menu
        pass
    
    def show_help_menu(self):
        # Implement popup menu
        pass
    
    # Utility functions
    def safe_theme_change(self):
        """Save theme preference without changing it immediately"""
        # Save setting but DON'T change appearance mode
        self.config_manager.config["night_mode"] = self.night_mode_var.get()
        self.config_manager.save_config()

        mode_name = "dark" if self.night_mode_var.get() else "light"
        self.update_status(f"Theme preference saved: {mode_name} mode (restart to apply)")
        self.debug.log("INFO", f"Theme preference saved: {mode_name} mode")

        # Show restart reminder
        self.show_restart_reminder()

    def show_restart_reminder(self):
        """Show a reminder that restart is needed for theme changes"""
        # Create a temporary status message
        original_status = self.status_var.get()
        self.update_status("💡 Restart the application to apply theme changes safely")

        # Reset status after 5 seconds
        self.after(5000, lambda: self.update_status(original_status))

    # REMOVED FLASHING TRANSITION METHODS FOR SAFETY

    def on_tab_change(self):
        """Handle graceful tab switching"""
        current_tab = self.tabview.get()

        if self.last_tab != current_tab:
            # Add a small delay for graceful loading
            self.update_status(f"Loading {current_tab} tab...")

            # Use after() to allow UI to update smoothly
            self.after(self.tab_switch_delay, lambda: self.complete_tab_switch(current_tab))

            self.last_tab = current_tab

    def complete_tab_switch(self, tab_name):
        """Complete the tab switch with status update"""
        self.update_status(f"{tab_name} tab loaded")
        self.debug.log("INFO", f"Switched to {tab_name} tab")

        # Refresh tab-specific content if needed
        if tab_name == "Scheduler":
            self.update_calendar()
        elif tab_name == "Credentials":
            # Could refresh credentials list here
            pass

    def apply_dim_light_theme(self):
        """Apply a dimmed light theme that's easier on the eyes"""
        try:
            # This is a workaround since CustomTkinter doesn't allow direct color customization
            # We'll use the built-in light mode but it will appear dimmer due to the transition effect
            self.update_status("Applied dimmed light theme for comfortable viewing")
        except Exception as e:
            self.debug.log("ERROR", f"Error applying dim theme: {e}")
    
    def browse_local_path(self):
        path = filedialog.askdirectory(initialdir=self.local_path)
        if path:
            self.local_path = path
            self.path_var.set(path)
            self.refresh_local_files()
    
    def refresh_local_files(self):
        # Clear current files
        for widget in self.local_files_frame.winfo_children():
            widget.destroy()
        
        # Add parent directory
        parent_button = ctk.CTkButton(self.local_files_frame, text="..", 
                                     command=lambda: self.navigate_local(".."))
        parent_button.pack(anchor="w", padx=5, pady=2)
        
        try:
            # List directories first
            for item in sorted([d for d in os.listdir(self.local_path) 
                               if os.path.isdir(os.path.join(self.local_path, d))]):
                dir_button = ctk.CTkButton(self.local_files_frame, text=f"📁 {item}", 
                                          command=lambda d=item: self.navigate_local(d))
                dir_button.pack(anchor="w", padx=5, pady=2)
            
            # Then list files
            for item in sorted([f for f in os.listdir(self.local_path) 
                               if os.path.isfile(os.path.join(self.local_path, f))]):
                file_button = ctk.CTkButton(self.local_files_frame, text=f"📄 {item}", 
                                           command=lambda f=item: self.select_file(f))
                file_button.pack(anchor="w", padx=5, pady=2)
        except Exception as e:
            self.debug.log("ERROR", f"Error refreshing local files: {e}")
            self.update_status(f"Error: {e}")
    
    def navigate_local(self, directory):
        if directory == "..":
            self.local_path = os.path.dirname(self.local_path)
        else:
            self.local_path = os.path.join(self.local_path, directory)
        
        self.path_var.set(self.local_path)
        self.refresh_local_files()
    
    def select_file(self, filename):
        full_path = os.path.join(self.local_path, filename)
        self.debug.log("INFO", f"Selected file: {full_path}")
        self.update_status(f"Selected: {filename}")
    
    def connect_sftp(self):
        """Connect to SFTP server with real connection testing"""
        self.debug.log("INFO", "Starting SFTP connection test...")
        self.update_status("Testing SFTP connection...")

        # Get default SFTP credentials
        sftp_cred = self.get_default_credential("SFTP")
        if not sftp_cred:
            self.update_status("Error: No default SFTP credential configured")
            return

        # Start connection test in background thread
        import threading
        connection_thread = threading.Thread(target=self.test_sftp_connection, args=(sftp_cred,))
        connection_thread.daemon = True
        connection_thread.start()

    def test_sftp_connection(self, credential):
        """Test SFTP connection in background thread"""
        try:
            host = credential["host"]
            self.debug.log("INFO", f"Testing connection to {host}")

            # Check if this is a fake endpoint
            if self.is_fake_endpoint(host):
                self.test_fake_sftp_endpoint(credential)
            else:
                self.test_real_sftp_endpoint(credential)

        except Exception as e:
            self.debug.log("ERROR", f"SFTP connection test failed: {e}")
            self.after(0, lambda: self.update_status(f"SFTP connection failed: {e}"))

    def is_fake_endpoint(self, host):
        """Check if this is a fake test endpoint"""
        fake_hosts = [
            "sftp.example.com",
            "backup.example.com",
            "test.sftp.local",
            "demo.sftp.server",
            "*************",
            "*********"
        ]
        return host in fake_hosts

    def test_fake_sftp_endpoint(self, credential):
        """Simulate connection to fake SFTP endpoint"""
        host = credential["host"]

        # Simulate connection delay
        import time
        time.sleep(2)

        # Simulate different responses for different fake hosts
        if "example.com" in host:
            # Simulate successful connection
            self.after(0, lambda: self.handle_fake_sftp_success(credential))
        elif "*************" in host:
            # Simulate network timeout
            self.after(0, lambda: self.handle_fake_sftp_timeout(credential))
        elif "*********" in host:
            # Simulate authentication failure
            self.after(0, lambda: self.handle_fake_sftp_auth_fail(credential))
        else:
            # Default to connection refused
            self.after(0, lambda: self.handle_fake_sftp_refused(credential))

    def test_real_sftp_endpoint(self, credential):
        """Test connection to real SFTP endpoint"""
        try:
            import socket
            host = credential["host"]
            port = 22  # Default SFTP port

            # Test basic connectivity
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 5 second timeout
            result = sock.connect_ex((host, port))
            sock.close()

            if result == 0:
                self.after(0, lambda: self.handle_real_connection_success(credential))
            else:
                self.after(0, lambda: self.handle_real_connection_failed(credential, "Connection refused"))

        except socket.gaierror:
            self.after(0, lambda: self.handle_real_connection_failed(credential, "Host not found"))
        except Exception as e:
            self.after(0, lambda: self.handle_real_connection_failed(credential, str(e)))

    def handle_fake_sftp_success(self, credential):
        """Handle successful fake SFTP connection"""
        host = credential["host"]
        self.sftp_client = f"FAKE_CONNECTION_{host}"  # Mock connection object
        self.update_status(f"✅ Connected to SFTP: {host} (FAKE ENDPOINT)")
        self.debug.log("INFO", f"Fake SFTP connection successful: {host}")

        # Update UI
        self.connect_button.configure(text="Connected", state="disabled")
        self.disconnect_button.configure(state="normal")

        # Populate fake remote files
        self.populate_fake_remote_files()

    def handle_fake_sftp_timeout(self, credential):
        """Handle fake SFTP timeout"""
        host = credential["host"]
        self.update_status(f"❌ SFTP connection timeout: {host} (FAKE ENDPOINT)")
        self.debug.log("WARNING", f"Fake SFTP timeout: {host}")

    def handle_fake_sftp_auth_fail(self, credential):
        """Handle fake SFTP authentication failure"""
        host = credential["host"]
        self.update_status(f"❌ SFTP authentication failed: {host} (FAKE ENDPOINT)")
        self.debug.log("WARNING", f"Fake SFTP auth failure: {host}")

    def handle_fake_sftp_refused(self, credential):
        """Handle fake SFTP connection refused"""
        host = credential["host"]
        self.update_status(f"❌ SFTP connection refused: {host} (FAKE ENDPOINT)")
        self.debug.log("WARNING", f"Fake SFTP connection refused: {host}")

    def handle_real_connection_success(self, credential):
        """Handle successful real connection"""
        host = credential["host"]
        self.update_status(f"✅ Port 22 open on {host} - SFTP service detected")
        self.debug.log("INFO", f"Real SFTP port accessible: {host}")

    def handle_real_connection_failed(self, credential, error):
        """Handle failed real connection"""
        host = credential["host"]
        self.update_status(f"❌ SFTP connection failed: {host} - {error}")
        self.debug.log("ERROR", f"Real SFTP connection failed: {host} - {error}")

    def populate_fake_remote_files(self):
        """Populate the remote files frame with fake data"""
        # Clear existing remote files
        for widget in self.remote_files_frame.winfo_children():
            widget.destroy()

        # Add fake remote files and directories
        fake_items = [
            ("📁", "documents"),
            ("📁", "images"),
            ("📁", "backups"),
            ("📄", "readme.txt"),
            ("📄", "config.json"),
            ("📄", "data.csv"),
            ("📁", "logs"),
            ("📄", "report.pdf")
        ]

        for icon, name in fake_items:
            item_button = ctk.CTkButton(self.remote_files_frame, text=f"{icon} {name}",
                                       command=lambda n=name: self.select_remote_file(n))
            item_button.pack(anchor="w", padx=5, pady=2)

    def select_remote_file(self, filename):
        """Handle remote file selection"""
        self.debug.log("INFO", f"Selected remote file: {filename}")
        self.update_status(f"Selected remote: {filename}")

    def get_default_credential(self, cred_type):
        """Get the default credential for a specific type"""
        # For now, return mock credential data
        # In a real implementation, this would read from the credentials manager
        mock_credentials = {
            "SFTP": {"host": "sftp.example.com", "name": "Main Server", "type": "SFTP"},
            "LAN": {"host": "*************", "name": "Local Network", "type": "LAN"},
            "Email": {"host": "smtp.gmail.com", "name": "Primary Email", "type": "Email"}
        }
        return mock_credentials.get(cred_type)

    def disconnect_sftp(self):
        """Disconnect from SFTP server"""
        if self.sftp_client:
            try:
                if isinstance(self.sftp_client, str) and "FAKE_CONNECTION" in self.sftp_client:
                    # Handle fake connection
                    self.sftp_client = None
                    self.debug.log("INFO", "Disconnected from fake SFTP")
                    self.update_status("Disconnected from SFTP (fake endpoint)")
                else:
                    # Handle real connection
                    self.sftp_client.close()
                    self.sftp_client = None
                    self.debug.log("INFO", "Disconnected from SFTP")
                    self.update_status("Disconnected from SFTP")

                # Update UI
                self.connect_button.configure(text="Connect", state="normal")
                self.disconnect_button.configure(state="disabled")

                # Clear remote files
                for widget in self.remote_files_frame.winfo_children():
                    widget.destroy()

            except Exception as e:
                self.debug.log("ERROR", f"Error disconnecting: {e}")
                self.update_status(f"Disconnect error: {e}")
        else:
            self.update_status("Not connected to any SFTP server")

    def test_lan_connectivity(self):
        """Test LAN connectivity to various endpoints"""
        self.debug.log("INFO", "Starting LAN connectivity tests...")
        self.update_status("Testing LAN connectivity...")

        # Get LAN credential
        lan_cred = self.get_default_credential("LAN")
        if not lan_cred:
            self.update_status("Error: No default LAN credential configured")
            return

        # Start LAN test in background thread
        import threading
        lan_thread = threading.Thread(target=self.perform_lan_tests, args=(lan_cred,))
        lan_thread.daemon = True
        lan_thread.start()

    def perform_lan_tests(self, credential):
        """Perform comprehensive LAN connectivity tests"""
        import socket
        import subprocess
        import time

        host = credential["host"]

        try:
            # Test 1: Ping test
            self.after(0, lambda: self.update_status(f"Testing ping to {host}..."))
            ping_result = self.test_ping(host)

            # Test 2: Port scan for common services
            self.after(0, lambda: self.update_status(f"Scanning common ports on {host}..."))
            port_results = self.test_common_ports(host)

            # Test 3: Network reachability
            self.after(0, lambda: self.update_status(f"Testing network reachability to {host}..."))
            reach_result = self.test_network_reachability(host)

            # Compile results
            self.after(0, lambda: self.display_lan_test_results(host, ping_result, port_results, reach_result))

        except Exception as e:
            self.debug.log("ERROR", f"LAN test failed: {e}")
            self.after(0, lambda: self.update_status(f"LAN test failed: {e}"))

    def test_ping(self, host):
        """Test ping connectivity"""
        try:
            if self.is_fake_endpoint(host):
                # Fake ping result
                return {"success": True, "time": "12ms", "fake": True}
            else:
                # Real ping test (simplified)
                import subprocess
                import platform

                param = "-n" if platform.system().lower() == "windows" else "-c"
                result = subprocess.run(["ping", param, "1", host],
                                      capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    return {"success": True, "time": "real", "fake": False}
                else:
                    return {"success": False, "error": "No response", "fake": False}
        except Exception as e:
            return {"success": False, "error": str(e), "fake": False}

    def test_common_ports(self, host):
        """Test common ports for services"""
        import socket

        common_ports = {
            22: "SSH/SFTP",
            80: "HTTP",
            443: "HTTPS",
            21: "FTP",
            23: "Telnet",
            25: "SMTP",
            53: "DNS",
            110: "POP3",
            143: "IMAP",
            993: "IMAPS"
        }

        results = {}

        for port, service in common_ports.items():
            try:
                if self.is_fake_endpoint(host):
                    # Fake port results
                    if port in [22, 80, 443]:  # Simulate some open ports
                        results[port] = {"open": True, "service": service, "fake": True}
                    else:
                        results[port] = {"open": False, "service": service, "fake": True}
                else:
                    # Real port test
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex((host, port))
                    sock.close()

                    results[port] = {
                        "open": result == 0,
                        "service": service,
                        "fake": False
                    }
            except Exception:
                results[port] = {"open": False, "service": service, "error": True}

        return results

    def test_network_reachability(self, host):
        """Test basic network reachability"""
        try:
            if self.is_fake_endpoint(host):
                return {"reachable": True, "fake": True}
            else:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3)
                result = sock.connect_ex((host, 80))  # Try HTTP port
                sock.close()
                return {"reachable": result == 0, "fake": False}
        except Exception:
            return {"reachable": False, "fake": False}

    def display_lan_test_results(self, host, ping_result, port_results, reach_result):
        """Display comprehensive LAN test results"""
        fake_indicator = " (FAKE)" if ping_result.get("fake", False) else ""

        # Create summary
        summary = f"LAN Test Results for {host}{fake_indicator}:\n"

        # Ping results
        if ping_result["success"]:
            summary += f"✅ Ping: Success ({ping_result.get('time', 'N/A')})\n"
        else:
            summary += f"❌ Ping: Failed ({ping_result.get('error', 'Unknown')})\n"

        # Port scan results
        open_ports = [f"{port}({data['service']})" for port, data in port_results.items() if data["open"]]
        if open_ports:
            summary += f"✅ Open Ports: {', '.join(open_ports[:3])}{'...' if len(open_ports) > 3 else ''}\n"
        else:
            summary += "❌ No common ports open\n"

        # Reachability
        if reach_result["reachable"]:
            summary += "✅ Network: Reachable"
        else:
            summary += "❌ Network: Unreachable"

        self.update_status(summary.replace('\n', ' | '))
        self.debug.log("INFO", f"LAN test completed for {host}")

        # Log detailed results
        for port, data in port_results.items():
            if data["open"]:
                self.debug.log("INFO", f"Port {port} ({data['service']}) is open on {host}")

    def add_test_endpoints_button(self):
        """Add a button to test all endpoints"""
        test_frame = ctk.CTkFrame(self.sftp_tab)
        test_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(test_frame, text="Connectivity Testing",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        button_frame = ctk.CTkFrame(test_frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        test_sftp_btn = ctk.CTkButton(button_frame, text="Test SFTP",
                                     command=self.connect_sftp)
        test_sftp_btn.pack(side="left", padx=5, pady=5)

        test_lan_btn = ctk.CTkButton(button_frame, text="Test LAN",
                                    command=self.test_lan_connectivity)
        test_lan_btn.pack(side="left", padx=5, pady=5)

        test_all_btn = ctk.CTkButton(button_frame, text="Test All Endpoints",
                                    command=self.test_all_endpoints)
        test_all_btn.pack(side="left", padx=5, pady=5)

    def test_all_endpoints(self):
        """Test all configured endpoints"""
        self.debug.log("INFO", "Starting comprehensive endpoint testing...")
        self.update_status("Testing all endpoints...")

        # Test in sequence with delays
        self.after(1000, self.connect_sftp)
        self.after(3000, self.test_lan_connectivity)
    
    def toggle_scheduler(self):
        self.scheduler_running.set(not self.scheduler_running.get())
        self.pause_button.configure(
            text="Pause Scheduler" if self.scheduler_running.get() else "Resume Scheduler"
        )
        self.config_manager.config["scheduler_paused"] = not self.scheduler_running.get()
        self.config_manager.save_config()
        self.debug.log("INFO", f"Scheduler {'resumed' if self.scheduler_running.get() else 'paused'}")
    
    def add_schedule(self):
        """Add a new schedule entry"""
        self.debug.log("INFO", "Adding new schedule...")
        # TODO: Implement schedule addition dialog
        self.update_status("Schedule addition feature coming soon...")

    def save_schedules(self):
        """Save current schedules to file"""
        try:
            # Save schedules to JSON file
            schedules_data = {
                "schedules": [],  # TODO: Collect actual schedule data
                "saved_at": datetime.now().isoformat()
            }

            with open(SCHEDULE_CONFIG_FILE, 'w') as f:
                json.dump(schedules_data, f, indent=4)

            self.debug.log("INFO", "Schedules saved successfully")
            self.update_status("Schedules saved to file")
        except Exception as e:
            self.debug.log("ERROR", f"Failed to save schedules: {e}")
            self.update_status(f"Error saving schedules: {e}")

    def load_schedules(self):
        """Load schedules from file"""
        try:
            if os.path.exists(SCHEDULE_CONFIG_FILE):
                with open(SCHEDULE_CONFIG_FILE, 'r') as f:
                    schedules_data = json.load(f)

                self.debug.log("INFO", "Schedules loaded successfully")
                self.update_status(f"Loaded schedules from {schedules_data.get('saved_at', 'unknown time')}")
            else:
                self.debug.log("WARNING", "No schedule file found")
                self.update_status("No saved schedules found")
        except Exception as e:
            self.debug.log("ERROR", f"Failed to load schedules: {e}")
            self.update_status(f"Error loading schedules: {e}")
    
    def change_debug_level(self, level):
        self.debug.set_level(level)
        self.debug.log("INFO", f"Debug level changed to {level}")
    
    def save_settings(self):
        # Save all settings to config
        self.config_manager.config["night_mode"] = self.night_mode_var.get()
        self.config_manager.config["honor_sftp_limits"] = self.honor_sftp_limits.get()
        self.config_manager.config["sftp_enabled"] = self.sftp_enabled.get()
        self.config_manager.config["enable_dir_copy"] = self.enable_dir_copy_var.get()
        self.config_manager.save_config()
        self.debug.log("INFO", "Settings saved")
        self.update_status("Settings saved")
    
    def update_status(self, message):
        self.status_var.set(message)
        self.log_text.insert("end", f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
        self.log_text.see("end")

# Main execution
if __name__ == "__main__":
    app = SFTPApp()
    app.mainloop()
