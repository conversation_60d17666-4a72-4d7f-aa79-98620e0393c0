"""
SFTP Application - Clean Architecture Wiring Plan
=================================================

This file defines the clean architecture for the SFTP application,
extracting the "magic" from the working sftptoy35_46_main_cmLS_refactored.py
and organizing it into a maintainable, modular structure.
"""

import os
import json
import threading
import time
import tkinter as tk
from datetime import datetime, timedelta
import customtkinter as ctk
from tkinter import filedialog, messagebox
import paramiko
import logging
from cryptography.fernet import Fernet
import hashlib
import base64
import calendar

# ============================================================================
# 1. CONFIGURATION MANAGEMENT
# ============================================================================

class ConfigManager:
    """Centralized configuration management with encryption support"""
    
    def __init__(self):
        self.config_file = "sftp_config.json"
        self.key_file = "sftp_key.key"
        self.config = {}
        self.fernet = self._get_fernet()
        self.load_config()
    
    def _get_fernet(self):
        """Get or create encryption key"""
        if not os.path.exists(self.key_file):
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
        
        with open(self.key_file, 'rb') as f:
            return Fernet(f.read())
    
    def load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
            except Exception as e:
                logging.error(f"Failed to load config: {e}")
                self.config = {}
        else:
            self.config = self._get_default_config()
            self.save_config()
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            logging.error(f"Failed to save config: {e}")
    
    def _get_default_config(self):
        """Get default configuration"""
        return {
            "sftp": {},
            "schedules": [],
            "credentials": {
                "sftp": [],
                "lan": [],
                "email": []
            },
            "ui": {
                "night_mode": False,
                "window_geometry": "1400x910"
            },
            "security": {
                "app_lock_enabled": False,
                "app_lock_password": ""
            }
        }
    
    def encrypt_password(self, password):
        """Encrypt a password"""
        return self.fernet.encrypt(password.encode()).decode()
    
    def decrypt_password(self, encrypted_password):
        """Decrypt a password"""
        try:
            return self.fernet.decrypt(encrypted_password.encode()).decode()
        except Exception:
            return encrypted_password  # Return as-is if decryption fails

# ============================================================================
# 2. SFTP MANAGEMENT
# ============================================================================

class SFTPManager:
    """Advanced SFTP manager with throttling and robust connection handling"""
    
    def __init__(self):
        self.transport = None
        self.client = None
        self.connected = False
        self.host = None
        self.port = 22
        self.username = None
        self.password = None
        self.home_path = ""
    
    def connect(self, host, port, username, password):
        """Connect to SFTP server"""
        try:
            self.host = host
            self.port = port
            self.username = username
            self.password = password
            
            # Create transport
            self.transport = paramiko.Transport((host, port))
            self.transport.connect(username=username, password=password)
            
            # Create SFTP client
            self.client = paramiko.SFTPClient.from_transport(self.transport)
            self.home_path = self.client.getcwd() or "/"
            self.connected = True
            
            logging.info(f"SFTP connected to {host}:{port}")
            return True
            
        except Exception as e:
            logging.error(f"SFTP connection failed: {e}")
            self.disconnect()
            return False
    
    def disconnect(self):
        """Disconnect from SFTP server"""
        try:
            if self.client:
                self.client.close()
            if self.transport:
                self.transport.close()
        except Exception as e:
            logging.error(f"SFTP disconnect error: {e}")
        finally:
            self.client = None
            self.transport = None
            self.connected = False
    
    def upload_throttled(self, local_path, remote_path, max_chunk_size=32768):
        """Upload file with throttling to respect server limits"""
        if not self.connected:
            raise RuntimeError("SFTP not connected")
        
        try:
            with open(local_path, "rb") as local_file:
                with self.client.file(remote_path, "wb") as remote_file:
                    remote_file.set_pipelined(True)
                    while True:
                        chunk = local_file.read(max_chunk_size)
                        if not chunk:
                            break
                        remote_file.write(chunk)
                        time.sleep(0.01)  # Small delay for throttling
            
            logging.info(f"Upload completed: {local_path} -> {remote_path}")
            return True
            
        except Exception as e:
            logging.error(f"Upload failed: {e}")
            return False
    
    def list_directory(self, path=None):
        """List directory contents"""
        if not self.connected:
            raise RuntimeError("SFTP not connected")
        
        target_path = path if path else self.home_path
        return self.client.listdir_attr(target_path)
    
    def test_connection(self, host, port=22, timeout=5):
        """Test basic connectivity to SFTP server"""
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False

# ============================================================================
# 3. THEME MANAGEMENT
# ============================================================================

class ThemeManager:
    """Safe theme management without flashing"""
    
    def __init__(self, app):
        self.app = app
        self.is_night_mode = False
        self.theme_applied = False
    
    def apply_startup_theme(self, night_mode=False):
        """Apply theme once at startup - NO DYNAMIC SWITCHING"""
        if self.theme_applied:
            return
        
        if night_mode:
            ctk.set_appearance_mode("dark")
            self.is_night_mode = True
        else:
            ctk.set_appearance_mode("light")
            self.is_night_mode = False
        
        self.theme_applied = True
        logging.info(f"Startup theme applied: {'dark' if night_mode else 'light'}")
    
    def save_theme_preference(self, night_mode):
        """Save theme preference for next startup"""
        # DO NOT APPLY THEME - just save preference
        config_manager = getattr(self.app, 'config_manager', None)
        if config_manager:
            config_manager.config["ui"]["night_mode"] = night_mode
            config_manager.save_config()
            logging.info(f"Theme preference saved: {'dark' if night_mode else 'light'}")

# ============================================================================
# 4. SCHEDULER SYSTEM
# ============================================================================

class Scheduler:
    """Advanced scheduler with calendar integration"""
    
    def __init__(self, app):
        self.app = app
        self.running = True
        self.schedules = []
        self.thread = None
        self.start_scheduler()
    
    def start_scheduler(self):
        """Start the scheduler thread"""
        if self.thread and self.thread.is_alive():
            return
        
        self.thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.thread.start()
        logging.info("Scheduler started")
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                self._check_schedules()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logging.error(f"Scheduler error: {e}")
                time.sleep(60)
    
    def _check_schedules(self):
        """Check if any schedules need to run"""
        now = datetime.now()
        
        for schedule in self.schedules:
            if self._should_run_schedule(schedule, now):
                self._execute_schedule(schedule)
    
    def _should_run_schedule(self, schedule, now):
        """Check if schedule should run now"""
        # Implement schedule matching logic
        hour = schedule.get("hour", 0)
        minute = schedule.get("minute", 0)
        
        return (now.hour == hour and now.minute == minute)
    
    def _execute_schedule(self, schedule):
        """Execute a scheduled task"""
        try:
            # Implement schedule execution
            logging.info(f"Executing schedule: {schedule.get('title', 'Untitled')}")
        except Exception as e:
            logging.error(f"Schedule execution failed: {e}")

# ============================================================================
# 5. MAIN APPLICATION WIRING
# ============================================================================

class SFTPApp(ctk.CTk):
    """Main application with clean component wiring"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize core components
        self.config_manager = ConfigManager()
        self.theme_manager = ThemeManager(self)
        self.sftp_manager = SFTPManager()
        self.scheduler = Scheduler(self)
        
        # Apply startup theme (ONCE, NO SWITCHING)
        night_mode = self.config_manager.config["ui"]["night_mode"]
        self.theme_manager.apply_startup_theme(night_mode)
        
        # Setup UI
        self.setup_window()
        self.create_ui()
        
        logging.info("SFTP Application initialized with clean architecture")
    
    def setup_window(self):
        """Setup main window"""
        self.title("V1's Setting Doofer - Clean Architecture")
        geometry = self.config_manager.config["ui"]["window_geometry"]
        self.geometry(geometry)
    
    def create_ui(self):
        """Create the user interface"""
        # This will be implemented in the actual application
        pass

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('sftp_app.log'),
            logging.StreamHandler()
        ]
    )
    
    # Run application
    app = SFTPApp()
    app.mainloop()
