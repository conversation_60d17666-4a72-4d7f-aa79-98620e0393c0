
import tkinter as tk
from tkinter import ttk

class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Night Mode Test")
        self.geometry("600x400")

        self.night_mode_var = tk.BooleanVar(value=False)

        menubar = tk.Menu(self)
        view_menu = tk.Menu(menubar, tearoff=0)
        view_menu.add_checkbutton(
            label="Night Mode",
            variable=self.night_mode_var,
            command=self.toggle_night_mode
        )
        menubar.add_cascade(label="View", menu=view_menu)
        self.config(menu=menubar)

        self.label = ttk.Label(self, text="Night Mode Example")
        self.label.pack(pady=20)

        self.button = ttk.Button(self, text="Click Me")
        self.button.pack(pady=10)

    def toggle_night_mode(self):
        style = ttk.Style()
        if self.night_mode_var.get():
            style.configure(".", background="#2e2e2e", foreground="white")
            style.configure("TLabel", background="#2e2e2e", foreground="white")
            style.configure("TButton", background="#3e3e3e", foreground="white")
            style.configure("TCheckbutton", background="#2e2e2e", foreground="white")
            self.configure(bg="#2e2e2e")
            print("Night Mode enabled")
        else:
            style.configure(".", background="#f0f0f0", foreground="black")
            style.configure("TLabel", background="#f0f0f0", foreground="black")
            style.configure("TButton", background="#f0f0f0", foreground="black")
            style.configure("TCheckbutton", background="#f0f0f0", foreground="black")
            self.configure(bg="#f0f0f0")
            print("Night Mode disabled")

if __name__ == "__main__":
    app = MainApp()
    app.mainloop()
