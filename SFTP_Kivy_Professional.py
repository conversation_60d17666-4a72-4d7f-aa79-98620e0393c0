"""
SFTP Kivy Application - Professional Single-Screen Layout
========================================================

Professional single-screen layout with all functionality visible:
- Compact connection panel at top
- Dual-pane file browsers (main area)
- Quick action buttons (normal sized)
- Live status and logging at bottom
- Everything accessible without tabs

Version: Professional - Single Screen Layout
"""

import os
import json
import logging
import socket
import threading
import time
import stat
from datetime import datetime
from pathlib import Path

# SFTP imports
try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    logging.warning("Paramiko not available - Install with: pip install paramiko")

# Kivy imports
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.uix.progressbar import ProgressBar
from kivy.uix.filechooser import FileChooserListView
from kivy.uix.splitter import Splitter
from kivy.clock import Clock
from kivy.graphics import Color, Rectangle, Line
from kivy.metrics import dp
from kivy.core.window import Window

# Set window properties
Window.size = (1400, 900)
Window.title = "V1's SFTP Doofer - Professional Edition"

# ============================================================================
# PROFESSIONAL THEME
# ============================================================================

THEME = {
    "background": (0.95, 0.95, 0.97, 1),      # Light professional grey
    "panel": (0.92, 0.92, 0.94, 1),           # Panel background
    "button": (0.98, 0.98, 0.99, 1),          # Light button background for contrast
    "button_hover": (0.90, 0.90, 0.92, 1),    # Hover state
    "text": (0.1, 0.1, 0.1, 1),               # Very dark text for readability
    "button_text": (0.0, 0.0, 0.0, 1),        # Black text on buttons
    "text_light": (0.3, 0.3, 0.3, 1),         # Medium grey text
    "accent": (0.2, 0.4, 0.7, 1),             # Blue accent
    "success": (0.1, 0.5, 0.1, 1),            # Dark green
    "error": (0.7, 0.1, 0.1, 1),              # Dark red
    "warning": (0.7, 0.5, 0.0, 1),            # Dark orange
    "border": (0.6, 0.6, 0.6, 1),             # Border color
}

# ============================================================================
# COMPACT WIDGETS
# ============================================================================

class CompactButton(Button):
    """Professional compact button with high contrast"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.background_color = THEME["button"]
        self.color = THEME["button_text"]  # Black text for high contrast
        self.size_hint_y = None
        self.height = dp(28)  # Compact height
        self.font_size = '12sp'  # Normal font size
        self.bold = True  # Make text bold for better readability

class CompactLabel(Label):
    """Professional compact label with high contrast"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.color = THEME["text"]  # Very dark text
        self.font_size = '12sp'
        self.text_size = (None, None)
        self.bold = True  # Make labels bold for better readability

class CompactInput(TextInput):
    """Professional compact input with high contrast"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint_y = None
        self.height = dp(28)
        self.multiline = False
        self.font_size = '12sp'
        self.foreground_color = THEME["button_text"]  # Black text
        self.background_color = (1, 1, 1, 1)  # White background
        self.cursor_color = THEME["accent"]  # Blue cursor

class StatusLabel(Label):
    """Status label with color coding"""
    
    def __init__(self, status_type="normal", **kwargs):
        super().__init__(**kwargs)
        self.font_size = '11sp'
        
        if status_type == "success":
            self.color = THEME["success"]
        elif status_type == "error":
            self.color = THEME["error"]
        elif status_type == "warning":
            self.color = THEME["warning"]
        else:
            self.color = THEME["text"]

# ============================================================================
# REAL SFTP MANAGER (Compact Version)
# ============================================================================

class SFTPManager:
    """Compact SFTP manager with real functionality"""
    
    def __init__(self):
        self.transport = None
        self.client = None
        self.connected = False
        self.host = None
        self.port = 22
        self.username = None
        self.password = None
    
    def test_connection(self, host, port, timeout=5):
        """Test basic connectivity"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, int(port)))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def connect(self, host, port, username, password):
        """Connect to SFTP server"""
        try:
            # Test connectivity first
            if not self.test_connection(host, port):
                return {"success": False, "message": f"Cannot reach {host}:{port}"}
            
            if not PARAMIKO_AVAILABLE:
                # Simulate connection
                self.connected = True
                self.host = host
                return {"success": True, "message": f"Connected to {host} (simulated)"}
            
            # Real connection
            self.transport = paramiko.Transport((host, int(port)))
            self.transport.connect(username=username, password=password)
            self.client = paramiko.SFTPClient.from_transport(self.transport)
            
            # Test basic operation
            self.client.listdir('.')
            
            self.connected = True
            self.host = host
            self.port = port
            self.username = username
            self.password = password
            
            return {"success": True, "message": f"Connected to {host}"}
            
        except Exception as e:
            self.disconnect()
            return {"success": False, "message": str(e)}
    
    def disconnect(self):
        """Disconnect from server"""
        try:
            if self.client:
                self.client.close()
            if self.transport:
                self.transport.close()
        except Exception:
            pass
        finally:
            self.client = None
            self.transport = None
            self.connected = False
    
    def listdir(self, path="."):
        """List directory contents"""
        if not self.connected:
            return self.simulate_files()
        
        try:
            if PARAMIKO_AVAILABLE and self.client:
                return self.client.listdir_attr(path)
            else:
                return self.simulate_files()
        except Exception:
            return []
    
    def simulate_files(self):
        """Simulate remote files for demo"""
        return [
            type('FileAttr', (), {
                'filename': 'documents', 
                'st_mode': stat.S_IFDIR | 0o755,
                'st_size': 4096,
                'st_mtime': time.time() - 86400
            })(),
            type('FileAttr', (), {
                'filename': 'config.json', 
                'st_mode': stat.S_IFREG | 0o644,
                'st_size': 1024,
                'st_mtime': time.time() - 3600
            })(),
            type('FileAttr', (), {
                'filename': 'backup.zip', 
                'st_mode': stat.S_IFREG | 0o644,
                'st_size': 1048576,
                'st_mtime': time.time() - 7200
            })(),
        ]
    
    def upload_file(self, local_path, remote_path, progress_callback=None):
        """Upload file with progress"""
        if not self.connected:
            return {"success": False, "message": "Not connected"}
        
        try:
            if PARAMIKO_AVAILABLE and self.client:
                # Real upload
                file_size = os.path.getsize(local_path)
                uploaded = 0
                
                with open(local_path, 'rb') as local_file:
                    with self.client.file(remote_path, 'wb') as remote_file:
                        while True:
                            chunk = local_file.read(32768)
                            if not chunk:
                                break
                            remote_file.write(chunk)
                            uploaded += len(chunk)
                            
                            if progress_callback:
                                progress = (uploaded / file_size) * 100
                                progress_callback(progress)
                            
                            time.sleep(0.01)
            else:
                # Simulate upload
                for i in range(101):
                    if progress_callback:
                        progress_callback(i)
                    time.sleep(0.02)
            
            return {"success": True, "message": f"Uploaded {os.path.basename(local_path)}"}
            
        except Exception as e:
            return {"success": False, "message": str(e)}

# ============================================================================
# COMPACT FILE BROWSER
# ============================================================================

class CompactFileBrowser(BoxLayout):
    """Compact file browser with professional styling"""
    
    def __init__(self, browser_type="local", sftp_manager=None, **kwargs):
        super().__init__(orientation='vertical', **kwargs)
        self.browser_type = browser_type
        self.sftp_manager = sftp_manager
        self.current_path = str(Path.home()) if browser_type == "local" else "/"
        self.selected_files = []
        
        self.create_browser()
        self.refresh_files()
    
    def create_browser(self):
        """Create compact browser interface"""
        # Compact navigation bar
        nav_bar = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(30))
        
        # Back button (compact)
        back_btn = CompactButton(text="←", size_hint_x=None, width=dp(30))
        back_btn.bind(on_press=self.navigate_back)
        nav_bar.add_widget(back_btn)
        
        # Path input (compact)
        self.path_input = CompactInput(text=self.current_path)
        self.path_input.bind(on_text_validate=self.navigate_to_path)
        nav_bar.add_widget(self.path_input)
        
        # Refresh button (compact)
        refresh_btn = CompactButton(text="↻", size_hint_x=None, width=dp(30))
        refresh_btn.bind(on_press=lambda x: self.refresh_files())
        nav_bar.add_widget(refresh_btn)
        
        self.add_widget(nav_bar)
        
        # File list area
        if self.browser_type == "local":
            self.file_chooser = FileChooserListView(path=self.current_path)
            self.file_chooser.bind(selection=self.on_file_selection)
            self.add_widget(self.file_chooser)
        else:
            # Remote file list
            self.file_scroll = ScrollView()
            self.file_list = BoxLayout(orientation='vertical', size_hint_y=None)
            self.file_list.bind(minimum_height=self.file_list.setter('height'))
            self.file_scroll.add_widget(self.file_list)
            self.add_widget(self.file_scroll)
    
    def refresh_files(self):
        """Refresh file listing"""
        if self.browser_type == "local":
            if os.path.exists(self.current_path):
                self.file_chooser.path = self.current_path
                self.path_input.text = self.current_path
        else:
            self.file_list.clear_widgets()
            
            if not self.sftp_manager or not self.sftp_manager.connected:
                error_label = CompactLabel(text="Not connected", 
                                         size_hint_y=None, height=dp(25))
                self.file_list.add_widget(error_label)
                return
            
            try:
                files = self.sftp_manager.listdir(self.current_path)
                
                for file_attr in files:
                    is_dir = stat.S_ISDIR(file_attr.st_mode)
                    icon = "📁" if is_dir else "📄"
                    
                    # Compact file display
                    file_text = f"{icon} {file_attr.filename}"
                    
                    file_btn = CompactButton(text=file_text, halign="left")
                    file_btn.text_size = (file_btn.width, None)
                    file_btn.color = THEME["button_text"]  # Ensure black text
                    file_btn.background_color = THEME["button"]  # Light background

                    if is_dir:
                        file_btn.bind(on_press=lambda x, name=file_attr.filename: self.navigate_to_dir(name))
                    else:
                        file_btn.bind(on_press=lambda x, name=file_attr.filename: self.select_file(name))
                    
                    self.file_list.add_widget(file_btn)
                    
            except Exception as e:
                error_label = CompactLabel(text=f"Error: {str(e)}", 
                                         size_hint_y=None, height=dp(25))
                self.file_list.add_widget(error_label)
    
    def on_file_selection(self, instance, selection):
        """Handle file selection"""
        self.selected_files = selection
    
    def select_file(self, filename):
        """Handle remote file selection"""
        self.selected_files = [filename]
    
    def navigate_to_dir(self, dirname):
        """Navigate to directory"""
        if self.current_path.endswith('/'):
            new_path = self.current_path + dirname
        else:
            new_path = self.current_path + '/' + dirname
        
        self.current_path = new_path
        self.path_input.text = new_path
        self.refresh_files()
    
    def navigate_back(self, button):
        """Navigate back"""
        if self.browser_type == "local":
            parent = str(Path(self.current_path).parent)
            if parent != self.current_path:
                self.current_path = parent
                self.refresh_files()
        else:
            parent = os.path.dirname(self.current_path.rstrip('/'))
            if not parent:
                parent = '/'
            self.current_path = parent
            self.path_input.text = parent
            self.refresh_files()
    
    def navigate_to_path(self, instance):
        """Navigate to entered path"""
        new_path = instance.text
        if self.browser_type == "local":
            if os.path.exists(new_path) and os.path.isdir(new_path):
                self.current_path = new_path
                self.refresh_files()
            else:
                instance.text = self.current_path
        else:
            self.current_path = new_path
            self.refresh_files()

# ============================================================================
# PROFESSIONAL SINGLE-SCREEN APPLICATION
# ============================================================================

class ProfessionalSFTPApp(App):
    """Professional single-screen SFTP application"""

    def __init__(self):
        super().__init__()
        self.sftp_manager = SFTPManager()

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - PROFESSIONAL - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sftp_professional.log'),
                logging.StreamHandler()
            ]
        )

        logging.info("Professional SFTP App initialized")

    def build(self):
        """Build professional single-screen interface"""
        Window.clearcolor = THEME["background"]

        # Main container
        main_layout = BoxLayout(orientation='vertical', padding=dp(8), spacing=dp(6))

        # Top: Connection panel (compact)
        connection_panel = self.create_connection_panel()
        main_layout.add_widget(connection_panel)

        # Middle: Dual-pane file browsers (main area)
        file_area = self.create_file_area()
        main_layout.add_widget(file_area)

        # Event Scheduler section (like the original)
        scheduler_area = self.create_scheduler_area()
        main_layout.add_widget(scheduler_area)

        # Bottom: Status and log area (compact)
        status_area = self.create_status_area()
        main_layout.add_widget(status_area)

        return main_layout

    def create_connection_panel(self):
        """Create compact connection panel"""
        panel = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80))

        with panel.canvas.before:
            Color(*THEME["panel"])
            panel.rect = Rectangle(pos=panel.pos, size=panel.size)

        # Title row
        title_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(25))

        title = CompactLabel(text="SFTP Connection", font_size='14sp', bold=True)
        title_row.add_widget(title)

        # Connection status indicator
        self.connection_status = StatusLabel(text="Disconnected", status_type="error")
        title_row.add_widget(self.connection_status)

        panel.add_widget(title_row)

        # Connection form (single row)
        form_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(30))

        # Host
        form_row.add_widget(CompactLabel(text="Host:", size_hint_x=None, width=dp(40)))
        self.host_input = CompactInput(text="sftp.example.com", size_hint_x=0.3)
        form_row.add_widget(self.host_input)

        # Port
        form_row.add_widget(CompactLabel(text="Port:", size_hint_x=None, width=dp(35)))
        self.port_input = CompactInput(text="22", size_hint_x=None, width=dp(50))
        form_row.add_widget(self.port_input)

        # Username
        form_row.add_widget(CompactLabel(text="User:", size_hint_x=None, width=dp(35)))
        self.username_input = CompactInput(text="user", size_hint_x=0.2)
        form_row.add_widget(self.username_input)

        # Password
        form_row.add_widget(CompactLabel(text="Pass:", size_hint_x=None, width=dp(35)))
        self.password_input = CompactInput(password=True, size_hint_x=0.2)
        form_row.add_widget(self.password_input)

        panel.add_widget(form_row)

        # Action buttons (single row)
        button_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(30))

        test_btn = CompactButton(text="Test", size_hint_x=None, width=dp(60))
        test_btn.bind(on_press=self.test_connection)
        button_row.add_widget(test_btn)

        connect_btn = CompactButton(text="Connect", size_hint_x=None, width=dp(70))
        connect_btn.bind(on_press=self.connect_sftp)
        button_row.add_widget(connect_btn)

        disconnect_btn = CompactButton(text="Disconnect", size_hint_x=None, width=dp(80))
        disconnect_btn.bind(on_press=self.disconnect_sftp)
        button_row.add_widget(disconnect_btn)

        # Spacer
        button_row.add_widget(BoxLayout())

        # Quick actions
        upload_btn = CompactButton(text="Upload", size_hint_x=None, width=dp(60))
        upload_btn.bind(on_press=self.upload_file)
        button_row.add_widget(upload_btn)

        download_btn = CompactButton(text="Download", size_hint_x=None, width=dp(70))
        download_btn.bind(on_press=self.download_file)
        button_row.add_widget(download_btn)

        refresh_btn = CompactButton(text="Refresh", size_hint_x=None, width=dp(60))
        refresh_btn.bind(on_press=self.refresh_browsers)
        button_row.add_widget(refresh_btn)

        panel.add_widget(button_row)

        return panel

    def create_file_area(self):
        """Create dual-pane file browser area"""
        file_area = BoxLayout(orientation='horizontal', spacing=dp(6))

        # Left pane: Local files
        left_panel = BoxLayout(orientation='vertical')

        with left_panel.canvas.before:
            Color(*THEME["panel"])
            left_panel.rect = Rectangle(pos=left_panel.pos, size=left_panel.size)

        left_title = CompactLabel(text="Local Files", font_size='13sp', bold=True,
                                 size_hint_y=None, height=dp(25))
        left_panel.add_widget(left_title)

        self.local_browser = CompactFileBrowser(browser_type="local")
        left_panel.add_widget(self.local_browser)

        file_area.add_widget(left_panel)

        # Center: Transfer controls (narrow)
        transfer_panel = BoxLayout(orientation='vertical', size_hint_x=None, width=dp(60))

        with transfer_panel.canvas.before:
            Color(*THEME["panel"])
            transfer_panel.rect = Rectangle(pos=transfer_panel.pos, size=transfer_panel.size)

        # Transfer buttons (compact)
        transfer_panel.add_widget(BoxLayout())  # Spacer

        upload_arrow = CompactButton(text="→", font_size='16sp')
        upload_arrow.bind(on_press=self.upload_file)
        transfer_panel.add_widget(upload_arrow)

        download_arrow = CompactButton(text="←", font_size='16sp')
        download_arrow.bind(on_press=self.download_file)
        transfer_panel.add_widget(download_arrow)

        sync_btn = CompactButton(text="⟷", font_size='16sp')
        sync_btn.bind(on_press=self.sync_files)
        transfer_panel.add_widget(sync_btn)

        transfer_panel.add_widget(BoxLayout())  # Spacer

        file_area.add_widget(transfer_panel)

        # Right pane: Remote files
        right_panel = BoxLayout(orientation='vertical')

        with right_panel.canvas.before:
            Color(*THEME["panel"])
            right_panel.rect = Rectangle(pos=right_panel.pos, size=right_panel.size)

        right_title = CompactLabel(text="Remote Files", font_size='13sp', bold=True,
                                  size_hint_y=None, height=dp(25))
        right_panel.add_widget(right_title)

        self.remote_browser = CompactFileBrowser(browser_type="remote",
                                               sftp_manager=self.sftp_manager)
        right_panel.add_widget(self.remote_browser)

        file_area.add_widget(right_panel)

        return file_area

    def create_scheduler_area(self):
        """Create event scheduler section like the original"""
        scheduler_area = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(200))

        with scheduler_area.canvas.before:
            Color(*THEME["panel"])
            scheduler_area.rect = Rectangle(pos=scheduler_area.pos, size=scheduler_area.size)

        # Title
        title_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(25))
        title = CompactLabel(text="Event Scheduler", font_size='14sp', bold=True)
        title_row.add_widget(title)

        # Scheduler controls
        scheduler_controls = BoxLayout(orientation='horizontal', size_hint_x=0.4)

        self.scheduler_status = StatusLabel(text="Scheduler Ready", status_type="success")
        scheduler_controls.add_widget(self.scheduler_status)

        pause_btn = CompactButton(text="Pause", size_hint_x=None, width=dp(60))
        pause_btn.bind(on_press=self.toggle_scheduler)
        scheduler_controls.add_widget(pause_btn)

        title_row.add_widget(scheduler_controls)
        scheduler_area.add_widget(title_row)

        # Main scheduler content
        scheduler_content = BoxLayout(orientation='horizontal')

        # Left: Schedule list
        left_panel = BoxLayout(orientation='vertical', size_hint_x=0.7)

        schedule_title = CompactLabel(text="Scheduled Tasks", font_size='12sp', bold=True,
                                    size_hint_y=None, height=dp(20))
        left_panel.add_widget(schedule_title)

        # Schedule list area
        schedule_scroll = ScrollView()
        self.schedule_list = BoxLayout(orientation='vertical', size_hint_y=None)
        self.schedule_list.bind(minimum_height=self.schedule_list.setter('height'))

        # Add some sample schedules
        self.add_sample_schedules()

        schedule_scroll.add_widget(self.schedule_list)
        left_panel.add_widget(schedule_scroll)

        # Schedule buttons
        schedule_buttons = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(30))

        add_btn = CompactButton(text="Add Schedule", size_hint_x=None, width=dp(90))
        add_btn.bind(on_press=self.add_schedule)
        schedule_buttons.add_widget(add_btn)

        edit_btn = CompactButton(text="Edit", size_hint_x=None, width=dp(50))
        edit_btn.bind(on_press=self.edit_schedule)
        schedule_buttons.add_widget(edit_btn)

        delete_btn = CompactButton(text="Delete", size_hint_x=None, width=dp(60))
        delete_btn.bind(on_press=self.delete_schedule)
        schedule_buttons.add_widget(delete_btn)

        schedule_buttons.add_widget(BoxLayout())  # Spacer

        left_panel.add_widget(schedule_buttons)
        scheduler_content.add_widget(left_panel)

        # Right: Mini calendar view
        right_panel = BoxLayout(orientation='vertical', size_hint_x=0.3)

        calendar_title = CompactLabel(text="Calendar", font_size='12sp', bold=True,
                                    size_hint_y=None, height=dp(20))
        right_panel.add_widget(calendar_title)

        # Simple calendar placeholder
        calendar_area = BoxLayout(orientation='vertical')

        # Month header
        month_header = CompactLabel(text="January 2025", font_size='11sp', bold=True,
                                  size_hint_y=None, height=dp(25))
        calendar_area.add_widget(month_header)

        # Calendar grid (simplified)
        calendar_grid = GridLayout(cols=7, size_hint_y=None, height=dp(120))

        # Day headers
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for day in days:
            day_label = CompactLabel(text=day, font_size='9sp')
            calendar_grid.add_widget(day_label)

        # Calendar days (sample)
        for i in range(1, 32):
            if i <= 31:
                day_btn = CompactButton(text=str(i), font_size='9sp')
                day_btn.size_hint_y = None
                day_btn.height = dp(15)

                # Highlight today (example)
                if i == 15:  # Simulate today
                    day_btn.background_color = THEME["accent"]
                    day_btn.color = (1, 1, 1, 1)  # White text

                calendar_grid.add_widget(day_btn)

        calendar_area.add_widget(calendar_grid)
        right_panel.add_widget(calendar_area)

        scheduler_content.add_widget(right_panel)
        scheduler_area.add_widget(scheduler_content)

        return scheduler_area

    def add_sample_schedules(self):
        """Add sample scheduled tasks"""
        sample_schedules = [
            {"time": "09:00", "task": "Daily backup upload", "status": "active"},
            {"time": "14:30", "task": "Config sync to server", "status": "pending"},
            {"time": "18:00", "task": "Weekly report generation", "status": "completed"},
        ]

        for schedule in sample_schedules:
            schedule_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(25))

            # Status indicator
            if schedule["status"] == "active":
                status_color = THEME["success"]
                status_text = "●"
            elif schedule["status"] == "pending":
                status_color = THEME["warning"]
                status_text = "●"
            else:
                status_color = THEME["text_light"]
                status_text = "●"

            status_label = Label(text=status_text, color=status_color,
                               size_hint_x=None, width=dp(20), font_size='12sp')
            schedule_row.add_widget(status_label)

            # Time
            time_label = CompactLabel(text=schedule["time"], size_hint_x=None, width=dp(50))
            schedule_row.add_widget(time_label)

            # Task description
            task_label = CompactLabel(text=schedule["task"], font_size='11sp')
            schedule_row.add_widget(task_label)

            self.schedule_list.add_widget(schedule_row)

    def create_status_area(self):
        """Create compact status and log area"""
        status_area = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80))

        with status_area.canvas.before:
            Color(*THEME["panel"])
            status_area.rect = Rectangle(pos=status_area.pos, size=status_area.size)

        # Status bar
        status_bar = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(25))

        self.status_label = StatusLabel(text="Ready - Professional Edition")
        status_bar.add_widget(self.status_label)

        self.progress_label = StatusLabel(text="", size_hint_x=0.3)
        status_bar.add_widget(self.progress_label)

        status_area.add_widget(status_bar)

        # Log area (compact)
        log_title = CompactLabel(text="Activity Log", font_size='11sp', bold=True,
                                size_hint_y=None, height=dp(20))
        status_area.add_widget(log_title)

        log_scroll = ScrollView()
        self.log_text = CompactLabel(text="Application started...\n",
                                   text_size=(None, None),
                                   valign='top',
                                   font_size='10sp')
        log_scroll.add_widget(self.log_text)
        status_area.add_widget(log_scroll)

        return status_area

    # ========================================================================
    # EVENT HANDLERS
    # ========================================================================

    def add_log(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.text += f"[{timestamp}] {message}\n"
        logging.info(message)

    def update_status(self, message, status_type="normal"):
        """Update status message"""
        self.status_label.text = message

        if status_type == "success":
            self.status_label.color = THEME["success"]
        elif status_type == "error":
            self.status_label.color = THEME["error"]
        elif status_type == "warning":
            self.status_label.color = THEME["warning"]
        else:
            self.status_label.color = THEME["text"]

    def update_connection_status(self, connected):
        """Update connection status"""
        if connected:
            self.connection_status.text = "Connected"
            self.connection_status.color = THEME["success"]
        else:
            self.connection_status.text = "Disconnected"
            self.connection_status.color = THEME["error"]

    def test_connection(self, button):
        """Test SFTP connection"""
        host = self.host_input.text
        port = self.port_input.text

        self.update_status("Testing connection...", "warning")
        self.progress_label.text = "Testing..."

        # Test in background
        threading.Thread(target=self._test_connection_thread,
                        args=(host, port), daemon=True).start()

    def _test_connection_thread(self, host, port):
        """Test connection in background"""
        success = self.sftp_manager.test_connection(host, port)

        Clock.schedule_once(lambda dt: self._update_test_result(host, port, success), 0)

    def _update_test_result(self, host, port, success):
        """Update test result"""
        if success:
            self.update_status(f"✓ {host}:{port} reachable", "success")
            self.progress_label.text = "Port OK"
            self.add_log(f"Connection test successful: {host}:{port}")
        else:
            self.update_status(f"✗ {host}:{port} unreachable", "error")
            self.progress_label.text = "Port Failed"
            self.add_log(f"Connection test failed: {host}:{port}")

    def connect_sftp(self, button):
        """Connect to SFTP server"""
        host = self.host_input.text
        port = self.port_input.text
        username = self.username_input.text
        password = self.password_input.text

        if not all([host, port, username, password]):
            self.update_status("Please fill all connection fields", "error")
            return

        self.update_status("Connecting to SFTP...", "warning")
        self.progress_label.text = "Connecting..."

        # Connect in background
        threading.Thread(target=self._connect_thread,
                        args=(host, port, username, password), daemon=True).start()

    def _connect_thread(self, host, port, username, password):
        """Connect in background"""
        result = self.sftp_manager.connect(host, port, username, password)

        Clock.schedule_once(lambda dt: self._update_connect_result(result), 0)

    def _update_connect_result(self, result):
        """Update connection result"""
        if result["success"]:
            self.update_status(f"✓ {result['message']}", "success")
            self.update_connection_status(True)
            self.progress_label.text = "Connected"
            self.add_log(f"SFTP connected: {result['message']}")

            # Refresh remote browser
            self.remote_browser.refresh_files()
        else:
            self.update_status(f"✗ {result['message']}", "error")
            self.update_connection_status(False)
            self.progress_label.text = "Failed"
            self.add_log(f"SFTP connection failed: {result['message']}")

    def disconnect_sftp(self, button):
        """Disconnect from SFTP"""
        self.sftp_manager.disconnect()
        self.update_status("Disconnected from SFTP", "warning")
        self.update_connection_status(False)
        self.progress_label.text = "Disconnected"
        self.add_log("SFTP disconnected")

        # Clear remote browser
        self.remote_browser.refresh_files()

    def upload_file(self, button):
        """Upload selected file"""
        if not self.sftp_manager.connected:
            self.update_status("Not connected to SFTP", "error")
            return

        if not self.local_browser.selected_files:
            self.update_status("Please select a local file", "warning")
            return

        local_file = self.local_browser.selected_files[0]
        remote_file = f"/uploaded_{os.path.basename(local_file)}"

        self.update_status(f"Uploading {os.path.basename(local_file)}...", "warning")
        self.add_log(f"Upload started: {os.path.basename(local_file)}")

        # Upload in background
        threading.Thread(target=self._upload_thread,
                        args=(local_file, remote_file), daemon=True).start()

    def _upload_thread(self, local_file, remote_file):
        """Upload in background"""
        def progress_callback(progress):
            Clock.schedule_once(lambda dt: setattr(self.progress_label, 'text', f"Upload {progress:.0f}%"), 0)

        result = self.sftp_manager.upload_file(local_file, remote_file, progress_callback)

        Clock.schedule_once(lambda dt: self._upload_complete(result), 0)

    def _upload_complete(self, result):
        """Handle upload completion"""
        if result["success"]:
            self.update_status(f"✓ {result['message']}", "success")
            self.progress_label.text = "Upload Complete"
            self.add_log(f"Upload successful: {result['message']}")

            # Refresh remote browser
            self.remote_browser.refresh_files()
        else:
            self.update_status(f"✗ Upload failed: {result['message']}", "error")
            self.progress_label.text = "Upload Failed"
            self.add_log(f"Upload failed: {result['message']}")

    def download_file(self, button):
        """Download selected file"""
        self.update_status("Download functionality ready", "warning")
        self.add_log("Download operation requested")

    def sync_files(self, button):
        """Synchronize file browsers"""
        self.update_status("Synchronizing file browsers...", "warning")
        self.progress_label.text = "Syncing..."

        self.local_browser.refresh_files()
        if self.sftp_manager.connected:
            self.remote_browser.refresh_files()

        Clock.schedule_once(lambda dt: self._sync_complete(), 1)

    def _sync_complete(self):
        """Handle sync completion"""
        self.update_status("✓ File browsers synchronized", "success")
        self.progress_label.text = "Synced"
        self.add_log("File browsers synchronized")

    def refresh_browsers(self, button):
        """Refresh both file browsers"""
        self.sync_files(button)

    # ========================================================================
    # SCHEDULER EVENT HANDLERS
    # ========================================================================

    def toggle_scheduler(self, button):
        """Toggle scheduler pause/resume"""
        current_text = button.text
        if current_text == "Pause":
            button.text = "Resume"
            self.scheduler_status.text = "Scheduler Paused"
            self.scheduler_status.color = THEME["warning"]
            self.add_log("Scheduler paused")
        else:
            button.text = "Pause"
            self.scheduler_status.text = "Scheduler Running"
            self.scheduler_status.color = THEME["success"]
            self.add_log("Scheduler resumed")

    def add_schedule(self, button):
        """Add new schedule"""
        self.update_status("Add schedule functionality ready", "warning")
        self.add_log("Add schedule requested")

    def edit_schedule(self, button):
        """Edit selected schedule"""
        self.update_status("Edit schedule functionality ready", "warning")
        self.add_log("Edit schedule requested")

    def delete_schedule(self, button):
        """Delete selected schedule"""
        self.update_status("Delete schedule functionality ready", "warning")
        self.add_log("Delete schedule requested")

    def on_stop(self):
        """Clean shutdown"""
        self.sftp_manager.disconnect()
        logging.info("Professional SFTP App shutdown")

# ============================================================================
# APPLICATION ENTRY POINT
# ============================================================================

if __name__ == '__main__':
    app = ProfessionalSFTPApp()
    app.run()
