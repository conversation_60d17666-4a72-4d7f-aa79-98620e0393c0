2025-06-09 20:45:55,580 - SFTPToy - INFO - Application started
2025-06-09 20:46:24,663 - SFTPToy - INFO - Settings saved
2025-06-09 20:46:32,761 - SFTPToy - INFO - Adding new schedule...
2025-06-09 20:46:33,396 - SFTPToy - INFO - Scheduler paused
2025-06-09 20:46:35,991 - SFTPToy - INFO - Connecting to SFTP...
2025-06-09 20:51:30,197 - SFTPToy - INFO - Applied dimmed light mode on startup
2025-06-09 20:51:30,205 - SFTPToy - INFO - Application started
2025-06-09 20:52:04,567 - SFTPToy - INFO - Theme transition started to dark mode
2025-06-09 20:52:05,253 - SFTPToy - INFO - Dark mode transition completed
2025-06-09 20:52:05,558 - SFTPToy - INFO - Theme transition started to light mode
2025-06-09 20:52:06,253 - SFTPToy - INFO - Light mode transition completed
2025-06-09 20:52:35,428 - SFTPToy - INFO - Applied dimmed light mode on startup
2025-06-09 20:52:35,432 - SFTPToy - INFO - Application started
2025-06-09 20:52:43,429 - SFTPToy - INFO - Debug level changed to ALL
2025-06-09 20:52:47,075 - SFTPToy - INFO - Theme transition started to dark mode
2025-06-09 20:52:47,801 - SFTPToy - INFO - Dark mode transition completed
2025-06-09 20:52:47,950 - SFTPToy - INFO - Theme transition started to light mode
2025-06-09 20:52:48,702 - SFTPToy - INFO - Light mode transition completed
2025-06-09 20:52:50,307 - SFTPToy - INFO - Adding new schedule...
2025-06-09 20:52:50,872 - SFTPToy - INFO - Scheduler resumed
2025-06-09 20:52:54,050 - SFTPToy - INFO - Scheduler paused
2025-06-09 21:01:27,093 - SFTPToy - INFO - Applied dimmed light mode on startup
2025-06-09 21:01:27,102 - SFTPToy - INFO - Application started
2025-06-09 21:01:38,863 - SFTPToy - INFO - Theme transition started to dark mode
2025-06-09 21:01:39,700 - SFTPToy - INFO - Dark mode transition completed
2025-06-09 21:01:49,377 - SFTPToy - INFO - Set LAN default to row 1
2025-06-09 21:01:50,313 - SFTPToy - INFO - Removed SFTP default
2025-06-09 21:01:50,696 - SFTPToy - INFO - Set SFTP default to row 3
2025-06-09 21:01:51,298 - SFTPToy - INFO - Removed Email default
2025-06-09 21:01:51,665 - SFTPToy - INFO - Set Email default to row 5
2025-06-09 21:01:52,019 - SFTPToy - INFO - Set Email default to row 4
2025-06-09 21:01:52,291 - SFTPToy - INFO - Removed SFTP default
2025-06-09 21:01:52,615 - SFTPToy - INFO - Set SFTP default to row 2
2025-06-09 21:01:53,132 - SFTPToy - INFO - Set LAN default to row 0
2025-06-09 21:01:53,743 - SFTPToy - INFO - Set SFTP default to row 3
2025-06-09 21:01:54,132 - SFTPToy - INFO - Set LAN default to row 1
2025-06-09 21:02:27,542 - SFTPToy - INFO - Set LAN default to row 0
2025-06-09 21:02:28,163 - SFTPToy - INFO - Set SFTP default to row 2
2025-06-09 21:02:29,553 - SFTPToy - INFO - Set Email default to row 5
2025-06-09 21:02:29,973 - SFTPToy - INFO - Set Email default to row 4
2025-06-09 21:02:30,327 - SFTPToy - INFO - Set SFTP default to row 3
2025-06-09 21:02:31,087 - SFTPToy - INFO - Set SFTP default to row 2
2025-06-09 21:02:31,482 - SFTPToy - INFO - Set LAN default to row 1
2025-06-09 21:02:32,530 - SFTPToy - INFO - Set LAN default to row 0
2025-06-09 21:08:33,608 - SFTPToy - INFO - Applied dark mode on startup
2025-06-09 21:08:33,611 - SFTPToy - INFO - Application started
2025-06-09 21:08:36,022 - SFTPToy - INFO - Switched to Settings tab
2025-06-09 21:08:36,944 - SFTPToy - INFO - Graceful theme transition started to light mode
2025-06-09 21:08:39,469 - SFTPToy - INFO - Graceful transition to dimmed light mode completed
2025-06-09 21:08:40,042 - SFTPToy - INFO - Graceful theme transition started to dark mode
2025-06-09 21:08:42,628 - SFTPToy - INFO - Graceful transition to dark mode completed
2025-06-09 21:11:50,171 - SFTPToy - INFO - Applied dark mode on startup
2025-06-09 21:11:50,175 - SFTPToy - INFO - Application started
2025-06-09 21:11:53,255 - SFTPToy - INFO - Switched to Settings tab
2025-06-09 21:11:54,446 - SFTPToy - INFO - Safe switch to dimmed light mode
2025-06-09 21:11:55,864 - SFTPToy - INFO - Safe switch to dark mode
2025-06-09 21:11:59,467 - SFTPToy - INFO - Safe switch to dimmed light mode
2025-06-09 21:12:00,024 - SFTPToy - INFO - Safe switch to dark mode
2025-06-09 21:15:00,304 - SFTPToy - INFO - Application started
2025-06-09 21:15:01,942 - SFTPToy - INFO - Switched to Settings tab
2025-06-09 21:15:05,135 - SFTPToy - INFO - Theme preference saved: light mode
2025-06-09 21:15:06,287 - SFTPToy - INFO - Theme preference saved: dark mode
2025-06-09 21:15:07,180 - SFTPToy - INFO - Theme preference saved: light mode
2025-06-09 21:15:07,960 - SFTPToy - INFO - Theme preference saved: dark mode
2025-06-10 01:41:42,347 - SFTPToy - INFO - Application started
2025-06-10 05:17:16,717 - INFO - Created default config: schedule_config.json
2025-06-10 05:17:16,728 - INFO - Created default config: email_config.json
2025-06-10 05:17:16,740 - INFO - Created default config: notification_settings.json
2025-06-10 05:17:16,794 - INFO - Loaded config from sftp_config.json
2025-06-10 05:17:16,799 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:17:50,453 - INFO - Night mode toggled
2025-06-10 05:17:56,505 - INFO - Bright calendar colors toggled
2025-06-10 05:18:08,314 - INFO - Manual window opened
2025-06-10 05:18:14,260 - INFO - === STARTING WIRING CHECK ===
2025-06-10 05:18:14,262 - INFO - Testing button functionality...
2025-06-10 05:18:14,264 - INFO - Scheduler paused
2025-06-10 05:18:14,265 - INFO - Testing mode switching...
2025-06-10 05:18:14,266 - INFO - Switched to LAN mode
2025-06-10 05:18:14,267 - INFO - Testing file navigation...
2025-06-10 05:18:14,276 - INFO - Local: Navigated up to G:\My Drive\Python\MyPyStuff\Jay_Py\Jay Py\Augmentor!
2025-06-10 05:18:14,277 - INFO - Testing SFTP connection...
2025-06-10 05:18:14,278 - INFO - Testing schedule tree...
2025-06-10 05:18:14,765 - INFO - Scheduler resumed
2025-06-10 05:18:15,268 - INFO - Switched to SFTP mode
2025-06-10 05:18:17,280 - INFO - === WIRING CHECK COMPLETE ===
2025-06-10 05:18:17,383 - INFO - SFTP connect failed: Cannot reach sftp.example.com:22
2025-06-10 05:18:26,298 - INFO - === STARTING WIRING CHECK ===
2025-06-10 05:18:26,299 - INFO - Testing button functionality...
2025-06-10 05:18:26,301 - INFO - Scheduler paused
2025-06-10 05:18:26,302 - INFO - Testing mode switching...
2025-06-10 05:18:26,302 - INFO - Switched to LAN mode
2025-06-10 05:18:26,304 - INFO - Testing file navigation...
2025-06-10 05:18:26,313 - INFO - Local: Navigated up to G:\My Drive\Python\MyPyStuff\Jay_Py\Jay Py\Augmentor!
2025-06-10 05:18:26,315 - INFO - Testing SFTP connection...
2025-06-10 05:18:26,315 - INFO - Testing schedule tree...
2025-06-10 05:18:26,813 - INFO - Scheduler resumed
2025-06-10 05:18:27,307 - INFO - Switched to SFTP mode
2025-06-10 05:18:29,327 - INFO - === WIRING CHECK COMPLETE ===
2025-06-10 05:18:29,332 - INFO - SFTP connect failed: Cannot reach sftp.example.com:22
2025-06-10 05:18:50,042 - INFO - Config saved to sftp_config.json
2025-06-10 05:18:50,045 - INFO - Enhanced Tkinter SFTP App v4 shutdown
2025-06-10 05:19:26,723 - INFO - Loaded config from sftp_config.json
2025-06-10 05:19:26,734 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:19:30,420 - INFO - Credential Manager opened
2025-06-10 05:19:57,113 - INFO - Config saved to sftp_config.json
2025-06-10 05:19:57,126 - INFO - Master password has been set.
2025-06-10 05:19:57,133 - INFO - Master password set.
2025-06-10 05:19:57,161 - INFO - [LockScreen] Lock screen created
2025-06-10 05:19:57,164 - INFO - Application locked
2025-06-10 05:20:03,410 - INFO - [LockScreen] Password correct - unlocking
2025-06-10 05:20:05,746 - INFO - === TESTING ALL CONNECTIONS ===
2025-06-10 05:20:05,786 - INFO - === CONNECTION TEST COMPLETE ===
2025-06-10 05:20:16,220 - INFO - Credential Manager opened
2025-06-10 05:20:27,064 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:20:27,070 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:20:27,092 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:20:56,470 - INFO - Config saved to sftp_config.json
2025-06-10 05:20:56,477 - INFO - Enhanced Tkinter SFTP App v4 shutdown
2025-06-10 05:23:58,389 - INFO - Loaded config from sftp_config.json
2025-06-10 05:23:58,393 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:30:58,791 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:30:58,801 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:30:58,808 - WARNING - Email settings incomplete - cannot send notification
2025-06-10 05:32:12,343 - INFO - Loaded config from sftp_config.json
2025-06-10 05:32:12,349 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:32:44,875 - INFO - Loaded config from sftp_config.json
2025-06-10 05:32:44,880 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:33:27,279 - INFO - Loaded config from sftp_config.json
2025-06-10 05:33:27,289 - INFO - Enhanced Tkinter SFTP App v4 initialized
2025-06-10 05:39:52,912 - INFO - Config saved to sftp_config.json
2025-06-10 05:39:52,915 - INFO - Enhanced Tkinter SFTP App v4 shutdown
